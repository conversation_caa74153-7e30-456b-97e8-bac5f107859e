package zone.loong.cube.framework.kernel.extension.register

import jakarta.annotation.PostConstruct
import org.springframework.stereotype.Component
import zone.loong.cube.framework.kernel.extension.Extension
import zone.loong.cube.framework.kernel.extension.ExtensionPointI
import zone.loong.cube.framework.kernel.extension.Extensions
import zone.loong.cube.framework.kernel.log.annotation.Log
import zone.loong.cube.framework.kernel.log.annotation.Log.Companion.log

@Log
@Component
class ExtensionBootstrap(
    private val extensionRegister: ExtensionRegister,
    private val extensions: List<ExtensionPointI>
) {
    @PostConstruct
    fun init() {
        for (ext in extensions) {
            if (ext.javaClass.isAnnotationPresent(Extension::class.java)) {
                extensionRegister.doRegistration(ext)
            } else if (ext.javaClass.isAnnotationPresent(Extensions::class.java)) {
                extensionRegister.doRegistrationExtensions(ext)
            } else {
                log.error(
                    "There is no annotation for @Extension or @Extension on this extension class:{}",
                    ext.javaClass
                )
            }
        }
    }
}