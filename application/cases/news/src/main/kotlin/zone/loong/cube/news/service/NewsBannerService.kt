package zone.loong.cube.news.service

import zone.loong.cube.news.model.NewsBanner
import zone.loong.cube.news.model.dto.NewsBlockSpecification
import zone.loong.cube.news.model.dto.SaveNewsBlock
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.dto.NewsBannerSpecification
import zone.loong.cube.news.model.dto.SaveNewsBanner

interface NewsBannerService {
    fun fetchBanners(pageQuery: PageQuery<NewsBannerSpecification>): Page<NewsBanner>
    fun save(saveNewsBanner: SaveNewsBanner): NewsBanner
    fun delete(id: String)
    fun detail(id: String): NewsBanner
}