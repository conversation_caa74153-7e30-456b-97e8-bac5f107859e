export zone.loong.cube.platform.auth.model.Account
    -> package zone.loong.cube.platform.auth.model.dto

specification AccountSpecification{
    userName
    orgId : String?
    roleId:String?
    groupId:String?
    flat(user) {
        like(name) as realName
        like(code)
    }
}

AccountInfo{
    userName as account
    preference
    roles{
        code
        name
        permissions{
            type
            code
            name
        }
    }
    groups{
        code
        name
        permissions{
            type
            code
            name
        }
    }
    user{
        name
        email
        phone
        orgs{
            name
            parent*
        }
    }

}


AccountWithOrgs{
    id
    userName
    user{
        name
        email
        phone
        orgs{
            code
            name
            parent*
        }
    }
}

input AccountPreference{
    account:String
    preference
}

input AccountAuthorization{
    id
    roles?{
        id
    }
    groups?{
        id
    }
}

AccountWithUserInfo{
    id
    userName as code
    flat(user) {
        name
        email
        phone
    }
    orgs:List<String>
}