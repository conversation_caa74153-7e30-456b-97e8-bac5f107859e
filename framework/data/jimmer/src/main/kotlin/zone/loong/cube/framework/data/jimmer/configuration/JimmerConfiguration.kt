package zone.loong.cube.framework.data.jimmer.configuration

import org.babyfish.jimmer.spring.cfg.SqlClientConfig
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.boot.autoconfigure.EnableAutoConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Lazy
import zone.loong.cube.framework.data.jimmer.interceptor.ACLDraftInterceptor
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.data.jimmer.interceptor.AuditDraftInterceptor
import zone.loong.cube.framework.data.jimmer.listener.ResourceListener
import zone.loong.cube.framework.data.kernel.configure.DataKernelAuthProviderConfigure
import zone.loong.cube.framework.data.kernel.service.AuthProvider

@Configuration
@EnableAutoConfiguration
@Import(DataKernelAuthProviderConfigure::class, SqlClientConfig::class,ResourceListener::class)
class JimmerConfiguration(@Lazy private val kSqlClient: KSqlClient,)  {

    @Bean
    fun auditDraftInterceptor(authProvider: AuthProvider): AuditDraftInterceptor {
        return AuditDraftInterceptor(authProvider)
    }

    @Bean
    fun aclFilter(authProvider: AuthProvider): ACLFilter {
        return ACLFilter(authProvider)
    }

    @Bean
    fun aclDraftInterceptor(): ACLDraftInterceptor {
        return ACLDraftInterceptor(kSqlClient)
    }
//    @Bean
//    fun resourceListener(kSqlClient: KSqlClient): ResourceListener {
//        return ResourceListener(kSqlClient)
//    }
}