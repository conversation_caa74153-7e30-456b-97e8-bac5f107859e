package zone.loong.cube.platform.auth.service.impl

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.stereotype.Service
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.platform.auth.model.Permission
import zone.loong.cube.platform.auth.model.dto.PermissionSave
import zone.loong.cube.platform.auth.model.dto.PermissionTree
import zone.loong.cube.platform.auth.model.groups
import zone.loong.cube.platform.auth.model.id
import zone.loong.cube.platform.auth.model.parentId
import zone.loong.cube.platform.auth.model.roles
import zone.loong.cube.platform.auth.service.PermissionService

@Service
class PermissionServiceImpl(private val kSqlClient: KSqlClient):PermissionService {

    override fun fetchPermission(): List<PermissionTree> {
        return kSqlClient.createQuery(Permission::class) {
            where(table.parentId eq null)
            select(table.fetch(PermissionTree::class))
        }.execute()
    }

    override fun fetchPermission(id: String):Permission{
        return kSqlClient.findById(Permission::class,id)?:throw BizException("资源不存在或已被删除")
    }

    override fun savePermission(savePermission: PermissionSave): Permission {
        return kSqlClient.entities.save(savePermission).modifiedEntity
    }

    override fun fetchRolePermission(groupId: String): List<Permission> {
        return kSqlClient.createQuery(Permission::class) {
            where(table.groups {
                id eq groupId
            })
            select(table)
        }.execute()
    }

    override fun fetchGroupPermission(roleId: String): List<Permission> {
        return kSqlClient.createQuery(Permission::class) {
            where(table.roles {
                id eq roleId
            })
            select(table)
        }.execute()
    }
}