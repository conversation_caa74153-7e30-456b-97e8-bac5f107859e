package zone.loong.cube.cases.share.controller

import org.springframework.web.bind.annotation.*
import zone.loong.cube.cases.share.model.ShareResource
import zone.loong.cube.cases.share.model.dto.FileSave
import zone.loong.cube.cases.share.model.dto.FolderSave
import zone.loong.cube.cases.share.model.dto.ResourceRename
import zone.loong.cube.cases.share.model.elasticsearch.ESShareResource
import zone.loong.cube.cases.share.service.ShareResourceService
import zone.loong.cube.cases.share.task.ShareGroupSyncTask
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.kernel.model.PageQuery

@RestController
@RequestMapping("/share/resource")
class ShareResourceController(
    private val resourceService: ShareResourceService,
    private val shareGroupSyncTask: ShareGroupSyncTask
) {

    @PostMapping()
    fun fetchResource(@RequestBody pageQuery: PageQuery<ESSpecification>): ESPage<ESShareResource> {
        return resourceService.fetchMixedResource(pageQuery)
    }


    @GetMapping("/path/{resourceId}")
    fun fetchPath(@PathVariable resourceId: String): List<ShareResource> {
        return resourceService.fetchResource(resourceId).path
    }

    @PostMapping("/folder")
    fun saveFolder(@RequestBody folder: FolderSave) {
        resourceService.saveFolder(folder)
    }


    @PostMapping("/rename")
    fun rename(@RequestBody resourceRename: ResourceRename) {
        resourceService.rename(resourceRename)
    }


    @PostMapping("/file")
    fun save(@RequestBody file: FileSave) {
        resourceService.savefile(file)
    }

    @GetMapping("/delete")
    fun delete(@RequestParam("id") id: String) {
        resourceService.delete(id)
    }

    @GetMapping("/sync")
    fun sync() {
        resourceService.sync()
    }

    @GetMapping("/syncGroup")
    fun syncGroup() {
        shareGroupSyncTask.syncShareGroupUser()
    }
}