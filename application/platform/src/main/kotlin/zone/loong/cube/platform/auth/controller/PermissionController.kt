package zone.loong.cube.platform.auth.controller

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.platform.auth.model.Permission
import zone.loong.cube.platform.auth.model.dto.PermissionSave
import zone.loong.cube.platform.auth.model.dto.PermissionTree
import zone.loong.cube.platform.auth.service.PermissionService

@RestController
@RequestMapping("/auth/permissions")
class PermissionController(private val permissionService: PermissionService) {
    @GetMapping("/{id}")
    fun get(@PathVariable("id") id: String): Permission {
        return permissionService.fetchPermission(id)
    }

    @GetMapping
    fun fetchPermission(): List<PermissionTree> {
        return permissionService.fetchPermission()
    }

    @PostMapping
    fun savePermission(@RequestBody permission: PermissionSave): Permission {
        return permissionService.savePermission(permission)
    }

    @PostMapping("/group/{id}")
    fun fetchGroupPermission(@PathVariable("id") id : String):List<Permission>{
        return permissionService.fetchGroupPermission(id)
    }


    @PostMapping("/role/{id}")
    fun fetchRolePermission(@PathVariable("id") id : String):List<Permission>{
        return permissionService.fetchRolePermission(id)
    }

}