package zone.loong.cube.cases.experience.service

import zone.loong.cube.cases.experience.model.ExperienceCase
import zone.loong.cube.cases.experience.model.dto.CaseViewWithName
import zone.loong.cube.cases.experience.model.dto.SaveCase
import zone.loong.cube.cases.experience.model.elasticsearch.ESExperienceCase
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.kernel.model.PageQuery

interface ExperienceCasesService {
    fun fetchCases(pageQuery: PageQuery<ESSpecification>): ESPage<ESExperienceCase>
    fun detail(id: String): CaseViewWithName
    fun save(saveCase: SaveCase): ExperienceCase
    fun delete(id: String)
    fun sync()
}