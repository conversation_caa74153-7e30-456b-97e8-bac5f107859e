package zone.loong.cube.platform.auth.service.impl

import cn.dev33.satoken.stp.StpUtil
import com.alicp.jetcache.anno.CacheInvalidate
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import org.babyfish.jimmer.kt.isLoaded
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.case
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.or
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.springframework.context.annotation.Primary
import org.springframework.expression.EvaluationException
import org.springframework.expression.Expression
import org.springframework.expression.ExpressionParser
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import org.springframework.stereotype.Service
import zone.loong.cube.framework.auth.security.exception.AuthException
import zone.loong.cube.framework.auth.security.model.BanLevel
import zone.loong.cube.framework.auth.security.service.AccountService
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.platform.auth.model.*
import zone.loong.cube.platform.auth.model.dto.*
import zone.loong.cube.platform.organization.model.*

@Service
@Primary
class AccountServiceImpl(
    private val kSqlClient: KSqlClient,
) : AccountService {
    @Cached(
        expire = 3600,
        cacheType = CacheType.BOTH,
        cacheNullValue = true,
        name = "user-cache-identity-",
        key = "#accountIdentity"
    )
    override fun fetchAccount(accountIdentity: String): Account {
        return kSqlClient
            //关闭过滤器
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .createQuery(Account::class) {
                where(table.userName eq accountIdentity)
                select(table.fetchBy {
                    allScalarFields()
                    roles {
                        allScalarFields()
                        permissions {
                            allScalarFields()
                        }
                    }
                    groups {
                        allScalarFields()
                        permissions {
                            allScalarFields()
                        }
                    }
                    user {
                        allScalarFields()
                        orgs {
                            code()
                            name()
                            //查询到公司节点
                            `parent*`()
                        }
                        orgIds()
                        allOrg()
                    }

                })
            }.fetchOneOrNull() ?: throw AuthException("账户不存在或账户异常")
    }

    fun savePreference(accountPreference: AccountPreference) {
        kSqlClient.entities.save(accountPreference.toEntity {
            userName = accountPreference.account
        })
    }

    fun authenticate(accountAuthorization: AccountAuthorization) {
        kSqlClient.entities.save(accountAuthorization)
    }

    fun fetchGroups(userId: String): List<Group> {
        return kSqlClient.createQuery(Group::class) {
            where(table.accounts {
                id eq userId
            })
            select(table)
        }.execute()
    }

    fun fetchRoles(userId: String): List<Role> {
        return kSqlClient.createQuery(Role::class) {
            where(table.accounts {
                id eq userId
            })
            select(table)
        }.execute()
    }

    fun fetchAccountInfo(): AccountInfo {
        //todo 有动态组后，查询要做修改，需要增加动态组信息
        val accountCode = StpUtil.getLoginIdAsString()

        return kSqlClient
            .createQuery(Account::class) {
                where(table.userName eq accountCode)
                select(table.fetch(AccountInfo::class))
            }.fetchOneOrNull() ?: throw BizException("账户不存在或已被禁用，请联系管理员")
    }


    fun fetchWorkspace(): List<Permission> {
        val accountCode = StpUtil.getLoginIdAsString()
        return kSqlClient.createQuery(Permission::class) {
            orderBy(table.sequence)
            where(
                or(
                    table.roles {
                        accounts {
                            userName eq accountCode
                        }
                    },
                    table.groups {
                        accounts {
                            userName eq accountCode
                        }
                    }
                ))
            select(table)
        }.execute()
    }

    @CacheInvalidate(name = "user-cache-identity-", key = "#accountIdentity")
    fun cleanAccount(accountIdentity: String) {

    }

    @Cached(
        name = "user-cache-token-",
        key = "#token",
        expire = 3600,
        cacheType = CacheType.BOTH,
        cacheNullValue = true
    )
    override fun fetchAccountByToken(token: String): Account {
        return fetchAccount(accountIdentity = StpUtil.getLoginIdByToken(token) as String)
    }

    @Cached(
        name = "user-cache-groups-",
        key = "#accountIdentity",
        expire = 3600,
        cacheType = CacheType.BOTH,
        cacheNullValue = true
    )
    override fun fetchAccountGroups(accountIdentity: String): List<String> {
        val account = fetchAccount(accountIdentity)

        val groupTerms = kSqlClient.createQuery(GroupTerm::class) {
            select(table.fetchBy {
                group {
                    code()
                }
                expression()
            })
        }.execute()

        val dyGroups: List<String> = groupTerms.filter { it ->
            evaluateCondition(it.expression, account)
        }.map { it.group.code }
        return if (isLoaded(account, Account::groups)) {
            account.groups.map { it.code } + dyGroups
        } else {
            dyGroups
        }

    }

    @CacheInvalidate(name = "user-cache-groups-", key = "#accountIdentity")
    fun cleanGroup(accountIdentity: String) {

    }

    @Cached(
        name = "user-cache-roles-",
        key = "#accountIdentity",
        expire = 3600,
        cacheType = CacheType.BOTH,
        cacheNullValue = true
    )
    override fun fetchAccountRoles(accountIdentity: String): List<String> {
        return fetchAccount(accountIdentity).roles.map { it.code }
    }

    override fun banAccount(accountIdentity: String, banLevel: BanLevel, banReason: String) {

    }

    override fun unbanAccount(accountIdentity: String) {

    }

    fun fetchAccount(pageQuery: PageQuery<AccountSpecification>): Page<AccountWithUserInfo> {
        val orgId = pageQuery.specification.orgId ?: ""
        val groupId = pageQuery.specification.groupId
        val roleId = pageQuery.specification.roleId



        return kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .page(pageQuery) {
                //解决游离岗位问
                if (orgId.isNotEmpty()) {
                    where(table.user.orgs {
                        or(
                            id eq orgId,
                            parentId eq orgId,
                            parent.parentId eq orgId,
                            parent.parent.parentId eq orgId,
                            parent.parent.parent.parentId eq orgId,
                        )
                    })
                }
//
                roleId?.apply {
                    orderBy(
                        case()
                            .match(table.id valueIn subQuery(Role::class) {
                                where(table.id eq roleId)
                                select(table.accounts.id)
                            }, 0)
                            .otherwise(2)
                    )
                }
                groupId?.apply {
                    orderBy(
                        case()
                            .match(table.id valueIn subQuery(Group::class) {
                                where(table.id eq groupId)
                                select(table.accounts.id)
                            }, 0)
                            .otherwise(2)
                    )
                }
                select(table.fetchBy {
                    userName()
                    user {
                        name()
                        email()
                        phone()
                        orgs({
                            filter { where(table.enable eq true, table.parent.enable eq true) }
                        }
                        ) {
                            code()
                            name()
                            `parent*`({
                                filter {
                                    where(table.enable eq true, table.parent.enable eq true)
                                }
                            })
                        }
                    }
                    roles()
                    groups()
                })
            }.convertData { accountWithOrg ->
                AccountWithUserInfo(accountWithOrg).copy(
                    orgs = accountWithOrg.user?.orgs?.let {
                        it.map { org ->
                            org.flattenParent().map { it.name }.reduce { left, right -> "$left/$right" }
                        }
                    } ?: emptyList(),
                )
            }
    }

    private fun evaluateCondition(spelExpression: String, rootObject: Any): Boolean {
        val parser: ExpressionParser = SpelExpressionParser()
        val expression: Expression = parser.parseExpression(spelExpression)
        val context = StandardEvaluationContext(rootObject)
        try {
            return expression.getValue(context, Boolean::class.java) ?: false
        } catch (_: EvaluationException) {

        }
        return false
    }
    fun getUserInfo(): User? {
        // 获取当前登录用户信息
        val tokenInfo = StpUtil.getTokenInfo()
        // 获取登录人信息
        val user = kSqlClient.createQuery(User::class){
            where(table.code eq tokenInfo.loginId.toString())
            select(table.fetchBy {
                name()
                orgs {
                    parent{
                        name()
                    }
                }
            })
        }.fetchOneOrNull()
        return user
    }

}