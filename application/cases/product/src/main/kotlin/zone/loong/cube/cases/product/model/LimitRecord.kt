package zone.loong.cube.cases.product.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.IDAware
import zone.loong.cube.platform.auth.model.Account

@Entity
@Table(name = "CASES_PRODUCT_ATTACHMENT_LIMIT_RECORDS")
interface LimitRecord : IDAware {
    @Key
    val tag: String

    @ManyToMany
    @JoinTable(name = "CASES_PRODUCT_LIMIT_RECORD_ATTACHMENT_MAPPING")
    val attachments: List<ProductAttachment>

    @OneToOne
    @Key
    val account: Account

    //    下载次数
    val counts: Int
}