package zone.loong.cube.platform.organization.service

import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.model.Tree
import zone.loong.cube.platform.organization.model.Org
import zone.loong.cube.platform.organization.model.dto.OrganizationSpecification

interface OrganizationService {
    fun fetchOrganization(pid: String?): List<Org>
    fun fetchOrganization(pageQuery: PageQuery<OrganizationSpecification>): Page<Org>
    fun filterOrganization(keyword: String): List<Tree>
}