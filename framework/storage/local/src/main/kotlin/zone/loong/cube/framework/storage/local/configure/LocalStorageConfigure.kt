package zone.loong.cube.framework.storage.local.configure

import org.dromara.x.file.storage.core.FileStorageProperties.LocalPlusConfig
import org.dromara.x.file.storage.core.FileStorageService
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.stereotype.Component
import zone.loong.cube.framework.storage.kernel.StorageConfigure
import zone.loong.cube.framework.storage.kernel.service.FileRecorder
import zone.loong.cube.framework.storage.local.LocalStorageProperties
import zone.loong.cube.framework.storage.local.constants.LocalStorageConstants
import zone.loong.cube.framework.storage.local.service.LocalStorageService

@Component
@Import(StorageConfigure::class)
@EnableConfigurationProperties(LocalStorageProperties::class)
class LocalStorageConfigure {
    @Bean
    fun localPlusConfig(
        localStorageProperties: LocalStorageProperties
    ): LocalPlusConfig {
        val localPlusConfig = LocalPlusConfig()
        localPlusConfig.setStoragePath(localStorageProperties.storagePath)
        localPlusConfig.setDomain(localStorageProperties.domain)
        localPlusConfig.setBasePath(localStorageProperties.basePath)
        localPlusConfig.setPlatform(LocalStorageConstants.PLATFORM)
        return localPlusConfig
    }
    @Bean
    fun localStorageService(fileStorageService: FileStorageService, fileRecorder: FileRecorder): LocalStorageService {
        return LocalStorageService(LocalStorageConstants.PLATFORM,fileStorageService,fileRecorder)
    }
}