package zone.loong.cube.framework.tika.service.impl

import org.apache.tika.Tika
import org.springframework.web.multipart.MultipartFile
import zone.loong.cube.framework.tika.TikaProperties
import zone.loong.cube.framework.tika.TikaSensitive
import zone.loong.cube.framework.tika.model.SensitiveItem
import zone.loong.cube.framework.tika.model.SensitiveResult
import zone.loong.cube.framework.tika.service.TikaService
import java.io.File
import java.io.InputStream
import java.nio.file.Path
import java.util.regex.Matcher
import java.util.regex.Pattern

class TikaServiceImpl(private val tika: Tika, private val properties: TikaProperties) : TikaService {
    override fun getText(path: Path): String {
        return try {
            tika.parseToString(path)
        } catch (_: Exception) {
            ""
        }catch (_: Error){
            ""
        }
    }

    override fun getText(inputStream: InputStream):String {
        return try {
             tika.parseToString(inputStream)
        } catch (_: Exception) {
            ""
        }catch (_: Error){
            ""
        }
    }

    override fun getText(file: File): String {
        return getText(file.inputStream())
    }

    override fun getText(file: MultipartFile): String {
       return getText(file.inputStream)
    }

    override fun checkSensitiveInfo(inputStream: InputStream): SensitiveResult {
        val fileContent: String = getText(inputStream)
        return detectAndAppend(fileContent, properties.sensitives)
    }

    override fun checkSensitiveInfo(file: File): SensitiveResult {
       return checkSensitiveInfo(file.inputStream())
    }

    override fun checkSensitiveInfo(file: MultipartFile): SensitiveResult {
        return  checkSensitiveInfo(file.inputStream)
    }

    override fun checkSensitiveInfo(inputStream: InputStream, sensitives: List<TikaSensitive>): SensitiveResult {
        val fileContent: String = getText(inputStream)
        return detectAndAppend(fileContent, sensitives)
    }

    override fun checkSensitiveInfo(file: File, sensitives: List<TikaSensitive>) : SensitiveResult {
        return checkSensitiveInfo(file.inputStream(), sensitives)
    }

    override fun checkSensitiveInfo(file: MultipartFile, sensitives:List<TikaSensitive>) : SensitiveResult {
        return  checkSensitiveInfo(file.inputStream, sensitives)
    }

    override fun getMime(path: Path): String {
        return tika.detect(path)
    }

    override fun getMimeType(inputStream: InputStream): String {
        return tika.detect(inputStream)
    }

    override fun getMimeType(file: File): String {
        return tika.detect(file)
    }

    override fun getMimeType(mimeType: String): String {
        return tika.detect(mimeType)
    }

    private fun detectAndAppend(content: String, sensitives: List<TikaSensitive>) : SensitiveResult {
        val sensitiveResult = SensitiveResult()
        sensitives.forEach {sensitive ->
            val pattern: Pattern = Pattern.compile(sensitive.regex)
            val matcher: Matcher = pattern.matcher(content)
            while (matcher.find()) {
                sensitiveResult.hasSensitive = true
                sensitiveResult.sensitiveInfos.add(
                    SensitiveItem(sensitive.description,matcher.group())
                )
            }
        }
        return sensitiveResult
    }
}