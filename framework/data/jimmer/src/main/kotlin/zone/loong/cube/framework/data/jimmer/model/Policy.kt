package zone.loong.cube.framework.data.jimmer.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.kernel.model.Policy
import zone.loong.cube.framework.data.kernel.model.PolicyType
import zone.loong.cube.framework.data.kernel.model.StrategyType

@Entity
@Table(name = "ACCESS_POLICIES")
interface Policy : Policy, AuditAware {
    @ManyToOne
    @Key
    @OnDissociate(DissociateAction.DELETE)
    val acl: ACL?

    @Key
    override val policyType: PolicyType

    @Key
    override val strategyType: StrategyType

    @Key
    override val value: String

    //依赖于哪条策略产生的，用于解决依赖传递的问题
    @ManyToOne
    @Key
    @OnDissociate(DissociateAction.DELETE)
    val refPolicy: zone.loong.cube.framework.data.jimmer.model.Policy?

    @OneToMany(mappedBy = "refPolicy")
    val policies: List<zone.loong.cube.framework.data.jimmer.model.Policy>

    val ruleId: String?
}