package zone.loong.cube.framework.auth.security.model

import java.time.LocalDateTime

// 访问记录
interface AccessRecord {
    // 访问类型
    val type: AccessType

    // 访问时间
    val time: LocalDateTime

    // 访问IP
    val ip: String

    // 访问设备
    val device: String

    // 访问地址
    val address: String

    // 访问用户
    val identity: String
}


// 访问类型
enum class AccessType {
    // 登录
    LOGIN,

    // 登出
    LOGOUT,

    // 踢出
    KICK_OUT,

    // 顶掉
    REPLACE
}