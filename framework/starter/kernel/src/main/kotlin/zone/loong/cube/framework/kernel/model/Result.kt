package zone.loong.cube.framework.kernel.model

import zone.loong.cube.framework.kernel.exception.ResultCode


class Result(var code: Int, var msg: String, var data: Any? = Any()) {
    // 手动设置返回vo
    constructor(data: Any) : this(ResultCode.SUCCESS.code, ResultCode.SUCCESS.msg, data)

    companion object {
        fun error(code: Int, msg: String): Result {
//            val traceId = TLogContext.getTraceId()
            val errorData = LinkedHashMap<String, String>()
            errorData["traceId"] = "1"
            //写入traceId方便查找问题
            return Result(code, msg, errorData)
        }
    }
}
