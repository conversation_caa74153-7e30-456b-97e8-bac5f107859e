package zone.loong.cube.platform.config

import cn.dev33.satoken.context.mock.SaTokenContextMockUtil
import cn.dev33.satoken.stp.StpUtil
import org.springframework.core.task.TaskDecorator

class TokenDecorator : TaskDecorator {
    override fun decorate(runnable: Runnable): Runnable {
        return Runnable {
            SaTokenContextMockUtil.setMockContext {
                StpUtil.login("admin")
                val tokenValue = StpUtil.getTokenValue()
                StpUtil.setTokenValueToStorage(tokenValue)
                runnable.run()
            }
        }
    }
}