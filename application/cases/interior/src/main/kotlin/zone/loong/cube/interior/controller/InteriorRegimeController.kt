package zone.loong.cube.interior.controller

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.interior.model.InteriorRegime
import zone.loong.cube.interior.model.dto.InteriorRegimeSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorRegime
import zone.loong.cube.interior.service.InteriorRegimeService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

@RestController
@RequestMapping("/interior/regime")
class InteriorRegimeController(
    private val interiorRegimeService: InteriorRegimeService
) {

    @PostMapping("/page")
    fun fetch(@RequestBody pageQuery: PageQuery<InteriorRegimeSpecification>): Page<InteriorRegime> {
        return interiorRegimeService.fetchRegimes(pageQuery)
    }

    @PostMapping("/save")
    fun save(@RequestBody saveInteriorRegime: SaveInteriorRegime): InteriorRegime {
        return interiorRegimeService.save(saveInteriorRegime)
    }

    @GetMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        interiorRegimeService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): InteriorRegime {
        return interiorRegimeService.detail(id)
    }
}