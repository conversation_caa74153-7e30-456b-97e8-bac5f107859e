package zone.loong.cube.platform.storage.service.impl

import cn.dev33.satoken.temp.SaTempUtil
import jakarta.servlet.http.HttpServletResponse
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.dromara.x.file.storage.core.Downloader
import org.dromara.x.file.storage.core.FileInfo
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.multipart.MultipartFile
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.framework.storage.kernel.model.FileDetail
import zone.loong.cube.framework.storage.kernel.model.copy
import zone.loong.cube.framework.storage.kernel.model.id
import zone.loong.cube.framework.storage.kernel.service.FileRecorder
import zone.loong.cube.framework.storage.local.service.LocalStorageService
import zone.loong.cube.framework.storage.preview.service.PreviewService
import zone.loong.cube.platform.storage.service.StorageService
import java.net.URL


@Service
class StorageServiceImpl(
    private val kSqlClient: KSqlClient,
    private val localStorageService: LocalStorageService,
    private val fileRecorder: FileRecorder,
    private val previewService: PreviewService
) :
    StorageService {
    override fun getFileById(idList: List<String>): List<FileDetail> {
        val fileDetailList = mutableListOf<FileDetail>()
        kSqlClient.createQuery(FileDetail::class) {
            where(table.id valueIn idList)
            select(table)
        }.execute().forEach {
            fileDetailList.add(it.copy {
                status = "success"
            })
        }
        return fileDetailList
    }

    @Transactional
    override fun upload(multipartFile: MultipartFile): String {
        return localStorageService.upload("", multipartFile).id.also {
            previewService.addTask(it)
        }
    }

    @Transactional
    override fun upload(fileUrl: URL): String {
        return localStorageService.upload("", fileUrl).id.also {
            previewService.addTask(it)
        }
    }

    @Transactional
    override fun uploadInfo(multipartFile: MultipartFile): FileInfo {
        return localStorageService.upload("", multipartFile)
    }

    override fun download(id: String, httpServletResponse: HttpServletResponse) {
        localStorageService.download( fileRecorder.getById(id), httpServletResponse)
    }

    override fun downloadByName(fileName: String, httpServletResponse: HttpServletResponse) {
        localStorageService.download( fileRecorder.getByName(fileName), httpServletResponse)
    }

    override fun download(id: String, token: String, httpServletResponse: HttpServletResponse) {
        SaTempUtil.parseToken(token) ?: throw BizException("token 已失效")
        //删除token
        SaTempUtil.deleteToken(token)
        localStorageService.download( fileRecorder.getById(id), httpServletResponse)
    }


    override fun download(id: String): Downloader {
       return  localStorageService.download( fileRecorder.getById(id))
    }
}