package zone.loong.cube.cases.experience.log

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.stereotype.Component
import zone.loong.cube.cases.experience.model.ExperienceCase
import zone.loong.cube.cases.experience.model.id
import zone.loong.cube.cases.experience.model.title
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.platform.annotation.ModuleResourceFetch
import zone.loong.cube.platform.annotation.Resource

@Component
class CaseLog(private val kSqlClient: KSqlClient) : ModuleResourceFetch {
    override fun fetchResource(identity: String): Resource {
        val resourceName = kSqlClient.filters {
            disableByTypes(ACLFilter::class)
        }.createQuery(ExperienceCase::class) {
            where(table.id eq identity)
            select(table.title)
        }.fetchOne()
        return Resource().apply {
            name = resourceName
        }
    }
}