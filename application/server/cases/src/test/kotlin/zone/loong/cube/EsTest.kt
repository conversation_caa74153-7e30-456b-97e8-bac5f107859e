//package zone.loong.cube
//
//import cn.dev33.satoken.SaManager
//import cn.dev33.satoken.spring.SaTokenContextForSpringInJakartaServlet
//import cn.dev33.satoken.stp.StpUtil
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import zone.loong.cube.framework.data.elasticsearch.service.SearchIndexService
//import zone.loong.cube.platform.organization.service.sync.OrganizationClient
//import zone.loong.cube.platform.organization.task.OrganizationTask
//
//@SpringBootTest
//class ApplicationTest(
//    @Autowired private val searchIndexService: SearchIndexService,
//    @Autowired private val organizationTask: OrganizationTask,
//    @Autowired private val organizationClient: OrganizationClient
//) {
//    @Test
//    fun createIndex() {
//    }
//
//    @Test
//    fun testRichText() {
//        SaManager.setSaTokenContext(SaTokenContextForSpringInJakartaServlet())
//        StpUtil.login("admin")
////        organizationTask.syncCompanyTask()
////        organizationTask.syncDepartmentTask()
////        organizationTask.syncPositionTask()
//        organizationTask.syncUserTask()
//
//    }
//}
//
//
