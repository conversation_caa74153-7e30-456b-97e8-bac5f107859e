package zone.loong.cube.cases.experience.controller

import org.springframework.web.bind.annotation.*
import zone.loong.cube.cases.experience.log.CaseLog
import zone.loong.cube.cases.experience.model.ExperienceCase
import zone.loong.cube.cases.experience.model.dto.CaseViewWithName
import zone.loong.cube.cases.experience.model.dto.SaveCase
import zone.loong.cube.cases.experience.model.elasticsearch.ESExperienceCase
import zone.loong.cube.cases.experience.service.ExperienceCasesService
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.annotation.ModuleLog

@RestController
@RequestMapping("/experience/cases")
class ExperienceCasesController(private val experienceCasesService: ExperienceCasesService) {

    @PostMapping()
    fun fetchCases(@RequestBody pageQuery: PageQuery<ESSpecification>): ESPage<ESExperienceCase> {
        return experienceCasesService.fetchCases(pageQuery)
    }

    @GetMapping("/{id}")
    @ModuleLog(workspace = "经验案例库", module = "经验案例", operation = "预览", identity = "#id", fetch = CaseLog::class)
    fun detail(@PathVariable("id") id: String): CaseViewWithName {
        return experienceCasesService.detail(id)
    }


    @PostMapping("/save")
    fun save(@RequestBody saveCase: SaveCase): ExperienceCase {
        return experienceCasesService.save(saveCase)
    }

    @GetMapping("/delete")
    fun delete(@RequestParam("id") id: String) {
        return experienceCasesService.delete(id)
    }

    @GetMapping("/sync")
    fun sync(){
        experienceCasesService.sync()
    }
}