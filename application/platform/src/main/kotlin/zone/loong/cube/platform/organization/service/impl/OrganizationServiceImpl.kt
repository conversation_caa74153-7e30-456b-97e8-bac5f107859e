package zone.loong.cube.platform.organization.service.impl

import org.babyfish.jimmer.kt.isLoaded
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.like
import org.babyfish.jimmer.sql.kt.ast.expression.or
import org.redisson.api.RBucket
import org.redisson.api.RedissonClient
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.model.Tree
import zone.loong.cube.platform.organization.model.*
import zone.loong.cube.platform.organization.model.dto.OrganizationSpecification
import zone.loong.cube.platform.organization.service.OrganizationService
import java.util.concurrent.TimeUnit


@Service
class OrganizationServiceImpl(
    private val kSqlClient: KSqlClient,
    private val redisson: RedissonClient
) : OrganizationService {
    override fun fetchOrganization(pid: String?): List<Org> {
        return kSqlClient.createQuery(Org::class) {
            pid?.takeIf { it.isNotEmpty() }?.let {
                where(table.parentId eq pid)
            } ?: where(table.parentId eq "")
            where(table.enable eq true)
            select(table.fetchBy {
                code()
                name()
                parent()
            })
        }.execute()
    }

    override fun fetchOrganization(pageQuery: PageQuery<OrganizationSpecification>): Page<Org> {
        return kSqlClient.page(pageQuery) {
            where(table.enable eq true)
            select(table)
        }
    }

    override fun filterOrganization(keyword: String): List<Tree> {
//        val cachedTree = getTreeListFromRedis("tree-org-cache-" +  keyword)
//        if (cachedTree != null) {
//            return cachedTree as List<Tree>
//        }
        val orgs = kSqlClient
            .createQuery(Org::class) {
                where(table.enable eq true, or(table.name like keyword, table.code eq keyword))
                select(table.fetchBy {
                    code()
                    name()
                    enable()
                    parent {
                        code()
                        name()
                        enable()
                    }
                    `parent*`()
                })
            }.execute()

        //展平树
        val flattenOrg = orgs.flatMap { it.flattenParent() }.distinctBy { it.id }
        val tree = flattenOrg.map {
            Tree().apply {
                id = it.id
                label = it.name
                value = it.code
                if (isLoaded(it, Org::parent)) {
                    pid = it.parent?.id ?: ""
                }
                children = mutableListOf()
            }
        }
        val result = Tree.transformPid(tree)
//        saveTreeListToRedis("tree-org-cache-" +  keyword, result, 1, TimeUnit.DAYS)
        return result
    }

    private fun transformToTree(orgs: List<Org>): MutableList<Tree> {
        return orgs.map {
            Tree().apply {
                id = it.id
                label = it.name
                value = it.code
                if (isLoaded(it, Org::children)) {
                    children = transformToTree(it.children)
                }

            }
        }.toMutableList()
    }


    fun saveTreeListToRedis(key: String?, treeList: List<Tree>, ttl: Long, unit: TimeUnit?) {
        val bucket: RBucket<List<Tree>> = redisson.getBucket(key)
        bucket.set(treeList, ttl, unit)
    }

    fun getTreeListFromRedis(key: String?): MutableList<Tree?>? {
        val bucket: RBucket<MutableList<Tree?>?> = redisson.getBucket(key)
        return bucket.get()
    }

}