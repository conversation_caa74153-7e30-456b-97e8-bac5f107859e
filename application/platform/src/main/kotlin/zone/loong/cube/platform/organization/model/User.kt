package zone.loong.cube.platform.organization.model

import org.babyfish.jimmer.Formula
import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.platform.auth.model.Account

@Entity
@Table(name = "SYS_USER")
interface User : AuditAware {
    @Key
    val code: String
    val name: String
    val email: String?
    val phone: String
    val sex: String
    val levelName: String?
    val enable: Bo<PERSON><PERSON>

    @ManyToMany
    @JoinTable(name = "SYS_ORG_USER_MAPPING")
    val orgs: List<Org>

    @OneToMany(mappedBy = "user")
    val accounts: List<Account>

    @Formula(dependencies = ["orgs"])
    val orgIds: List<String>
        get() = orgs.flatMap { org ->
            org.flattenParent().map { it.id }
        }
    @Formula(dependencies = ["orgs"])
    val allOrg: List<Org>
        get() = orgs.flatMap { org ->
            org.flattenParent()
        }
}