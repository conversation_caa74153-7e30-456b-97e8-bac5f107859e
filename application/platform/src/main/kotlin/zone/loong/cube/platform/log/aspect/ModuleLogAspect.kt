package zone.loong.cube.platform.log.aspect


import org.aspectj.lang.JoinPoint
import org.aspectj.lang.annotation.Aspect
import org.aspectj.lang.annotation.Before
import org.aspectj.lang.annotation.Pointcut
import org.aspectj.lang.reflect.MethodSignature
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.expression.ExpressionParser
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import org.springframework.stereotype.Component
import zone.loong.cube.platform.annotation.ModuleLog
import zone.loong.cube.platform.annotation.ModuleResourceFetch
import zone.loong.cube.platform.log.model.Module

@Aspect
@Component
class ModuleLogAspect(private val kSqlClient: KSqlClient, private val fetchers: List<ModuleResourceFetch>) {
    @Pointcut("@annotation(zone.loong.cube.platform.annotation.ModuleLog)")
    public fun pointCut() {
    }

    @Before("pointCut()")
    fun before(joinPoint: JoinPoint) {
        val signature = joinPoint.signature
        when (signature) {
            is MethodSignature -> {
                val annotation = signature.method.getAnnotation(ModuleLog::class.java)
                annotation?.let { moduleLog ->
                    val targetFetcher = fetchers.find { fetcher ->
                        fetcher::class == moduleLog.fetch
                    }
                    val identity = parseSpelExpression(moduleLog.identity, joinPoint, signature)
                    kSqlClient.save(Module {
                        workspace = moduleLog.workspace
                        module = moduleLog.module
                        resourceId = identity
                        operation  = moduleLog.operation
                        resourceName = targetFetcher?.fetchResource(identity)?.name ?: ""
                    }, mode = SaveMode.INSERT_ONLY)
                }
            }
        }

    }

    /**
     * 解析 SpEL 表达式
     */
    private fun parseSpelExpression(
        expression: String,
        joinPoint: JoinPoint,
        signature: MethodSignature
    ): String {
        return try {
            val parser: ExpressionParser = SpelExpressionParser()
            // 创建 SpEL 上下文
            val context = StandardEvaluationContext()

            // 获取方法参数名和值
            val parameterNames = signature.parameterNames
            val args = joinPoint.args

            // 将方法参数添加到 SpEL 上下文中
            parameterNames?.forEachIndexed { index, paramName ->
                if (index < args.size) {
                    context.setVariable(paramName, args[index])
                }
            }

            // 解析并执行 SpEL 表达式
            val spelExpression = parser.parseExpression(expression)
            spelExpression.getValue(context, String::class.java) ?: ""
        } catch (e: Exception) {
            // SpEL 解析失败时，返回原始字符串
            println("SpEL 表达式解析失败: $expression, 错误: ${e.message}")
            expression
        }
    }
}