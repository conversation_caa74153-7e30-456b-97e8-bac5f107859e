package zone.loong.cube.platform.dataAuth.service

import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.model.Policy
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.Group
import zone.loong.cube.platform.auth.model.Role
import zone.loong.cube.platform.auth.model.dto.ACLSave
import zone.loong.cube.platform.auth.model.dto.ACLSpecification
import zone.loong.cube.platform.auth.model.dto.ACLView
import zone.loong.cube.platform.auth.model.dto.AccountWithUserInfo
import zone.loong.cube.platform.organization.model.Org

interface ACLService {
    fun authorization(aclSave: ACLSave)
    fun fetchACL(pageQuery: PageQuery<ACLSpecification>): Page<ACLView>
    fun fetchACL(aclSpecification: ACLSpecification): List<ACLView>
    fun deleteRefPolicy(policyIds: List<String>)
    fun fetchAccount(resourceId: String): List<AccountWithUserInfo>
    fun fetchOrganization(resourceId: String): List<Org>
    fun fetchGroups(resourceId: String): List<Group>
    fun fetchRoles(resourceId: String): List<Role>
    fun addPolicy(policies: List<Policy>)

    @Transactional()
    fun authorization(acls: List<ACLSave>)
}