package zone.loong.cube.cases.share.service.impl

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.exists
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.cases.share.model.*
import zone.loong.cube.cases.share.model.dto.FileSave
import zone.loong.cube.cases.share.model.dto.FolderSave
import zone.loong.cube.cases.share.model.dto.ResourceRename
import zone.loong.cube.cases.share.model.elasticsearch.ESShareResource
import zone.loong.cube.cases.share.service.ESShareCasesService
import zone.loong.cube.cases.share.service.ShareResourceService
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.elasticsearch.service.SearchService
import zone.loong.cube.framework.data.elasticsearch.service.page
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.data.jimmer.model.ACL
import zone.loong.cube.framework.data.jimmer.model.resourceId
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.exception.BizException

@Service
class ShareResourceServiceImpl(
    private val kSqlClient: KSqlClient,
    private val searchService: SearchService,
    private val esShareCasesService: ESShareCasesService,
) : ShareResourceService {

    override fun fetchMixedResource(pageQuery: PageQuery<ESSpecification>): ESPage<ESShareResource> {
        return searchService.page<ESShareResource>(pageQuery, true)
    }

    override fun fetchResource(id: String): ShareResource {
        return kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .createQuery(ShareResource::class) {
                where(table.id eq id)
                orderBy(table.createdTime)
                select(table.fetchBy {
                    name()
                    path()
                    `parent*`()

                })
            }.fetchOneOrNull() ?: throw BizException("资源不存在或已被删除")
    }

    @Transactional
    override fun saveFolder(folder: FolderSave) {
        checkNotNull(folder.parentId) { "上级ID不能为空" }
        checkResourceName(folder.parentId, folder.name)
        saveResource(folder.toEntity {
            type = ShareResourceType.DIRECTORY
        })
    }

    @Transactional
    override fun savefile(file: FileSave) {
        checkNotNull(file.parentId) { throw BizException("上级ID不能为空") }
        checkNotNull(file.fileId) { throw BizException("文件Id不能为空") }
        checkResourceName(file.parentId, file.name)
        saveResource(file.toEntity {
            type = ShareResourceType.FILE
            file {
                id = file.fileId
                originalFilename = file.name
            }
        }, true)
    }

    @Transactional
    override fun rename(resourceRename: ResourceRename) {
        val originResource = kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .createQuery(ShareResource::class) {
                where(table.id eq resourceRename.id)
                select(table)
            }.fetchOneOrNull() ?: throw BizException("资源不存在或已被删除")
        checkNotNull(originResource.parent?.id) { throw BizException("根目录不允许修改") }
        checkResourceName(originResource.parent!!.id, resourceRename.name)
        originResource.file?.let {
            saveResource(resourceRename.toEntity {
                file {
                    id = it.id
                    originalFilename = resourceRename.name
                }
            })
        } ?: saveResource(resourceRename.toEntity())
    }

    @Transactional
    fun saveResource(resource: ShareResource, withFile: Boolean = false): ShareResource {
        return kSqlClient.entities.save(resource).modifiedEntity.also {
            esShareCasesService.sync(it.id, withFile)
        }
    }


    private fun checkResourceName(parentId: String, name: String) {
        kSqlClient.filters {
            disableByTypes(ACLFilter::class)
        }.exists(ShareResource::class) {
            where(table.name eq name)
            where(table.parentId eq parentId)
        }.takeIf { it }?.let {
            throw BizException("该名称已存在")
        }
    }

    /**
     * 删除逻辑
     * 1.删除当前案例
     * 2.删除当前案例对应的授权
     * 3.删除当前案例授权的传播模型
     */
    @Transactional
    override fun delete(id: String) {
        val hasOther = kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .exists(ShareResource::class) {
            where(table.parentId eq id)
        }
        if (hasOther) {
            throw BizException("该文件夹存在子文件夹，或文件夹内有其他文件，暂不支持删除，请删除子文件/文件夹后再试")
        }
        kSqlClient.deleteById(ShareResource::class, id)
        kSqlClient.createDelete(ACL::class) {
            where(table.resourceId eq id)
        }.execute()
        esShareCasesService.delete(id)
    }

    override fun sync() {
        val casesIds = kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .entities.findAll(ShareResource::class).map { it.id }
        esShareCasesService.syncWithFile(casesIds)
    }

}