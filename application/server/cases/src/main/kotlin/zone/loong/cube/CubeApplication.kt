package zone.loong.cube

import com.alicp.jetcache.anno.config.EnableMethodCache
import org.babyfish.jimmer.client.EnableImplicitApi
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.runApplication
import org.springframework.scheduling.annotation.EnableScheduling

@SpringBootApplication
@EnableScheduling
@EnableMethodCache(basePackages = ["zone.loong.cube"])
@EnableImplicitApi
class CubeApplication

fun main(args: Array<String>) {
    runApplication<CubeApplication>(*args)
}