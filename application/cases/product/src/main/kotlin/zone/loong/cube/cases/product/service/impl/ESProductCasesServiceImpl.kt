package zone.loong.cube.cases.product.service.impl

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.data.elasticsearch.core.ElasticsearchOperations
import org.springframework.data.elasticsearch.core.document.Document
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates
import org.springframework.data.elasticsearch.core.query.UpdateQuery
import org.springframework.stereotype.Service
import zone.loong.cube.cases.product.model.ProductCase
import zone.loong.cube.cases.product.model.by
import zone.loong.cube.cases.product.model.elasticsearch.ESProductCase
import zone.loong.cube.cases.product.model.fetchBy
import zone.loong.cube.cases.product.model.id
import zone.loong.cube.cases.product.service.ESProductCasesService
import zone.loong.cube.framework.data.elasticsearch.service.SearchService
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.framework.tika.service.TikaService
import zone.loong.cube.platform.storage.service.StorageService

@Service
class ESProductCasesServiceImpl(
    private val searchService: SearchService,
    private val kSqlClient: KSqlClient,
    private val storageService: StorageService,
    private val elasticsearchOperations: ElasticsearchOperations,
    private val tikaService: TikaService
) : ESProductCasesService {
    override fun sync(caseId: String, extractFile: Boolean) {
        val case = kSqlClient.findById(newFetcher(ProductCase::class).by {
            allTableFields()
            classify {
                code()
                name()
                `parent*`()
            }
            attachments {
                file {

                }
            }
            acl {
                allScalarFields()
                policies {
                    allScalarFields()
                }
            }
        }, caseId) ?: throw BizException("案例被删除")
        val esExperienceCase = ESProductCase.buildFromCase(case).apply {
            if (extractFile) {
                attachment.forEach { fileId ->
                    storageService.download(fileId).inputStream { inputStream ->
                        tikaService.getText(inputStream).takeIf { text -> text.isNotEmpty() }?.let { text ->
                            content.add(text)
                        }
                    }
                }
            }
        }
        searchService.save(esExperienceCase)
    }


    override fun updateAcl(caseIds: List<String>) {
        if (caseIds.isEmpty()) {
            return
        }
        kSqlClient
            .filters{
                disableByTypes(ACLFilter::class)
            }
            .createQuery(ProductCase::class) {
            where(table.id valueIn caseIds)
            select(table.fetchBy {
                allTableFields()
                acl {
                    policies {
                        allScalarFields()
                    }
                }
            })
        }.let {
            val updateQueries = it.execute().filter { case-> case.acl?.policies?.isNotEmpty() == true }.map {
                    case ->
                val document = Document.create()
                document["policies"] = case.acl?.policies?.map { policy ->
                    mapOf(
                        "policyType" to policy.policyType,
                        "strategyType" to policy.strategyType,
                        "value" to policy.value
                    )
                }

                UpdateQuery.builder(case.id)
                    .withDocument(document)
                    .build()
            }
                if (updateQueries.isNotEmpty()){
                    elasticsearchOperations.bulkUpdate(updateQueries, IndexCoordinates.of("cases_product"))
                }

        }

    }

    override fun syncWithFile(caseIds: List<String>) {
        kSqlClient.createQuery(ProductCase::class) {
            where(table.id valueIn caseIds)
            select(table.fetchBy {
                allTableFields()
                classify {
                    code()
                    name()
                    `parent*`()
                }
                attachments {
                    file {

                    }
                }
                acl {
                    allScalarFields()
                    policies {
                        allScalarFields()
                    }
                }
                acl {
                    allScalarFields()
                    policies {
                        allScalarFields()
                    }
                }
            })
        }.map {
            ESProductCase.buildFromCase(it).apply {
                attachment.forEach { fileId ->
                    storageService.download(fileId).inputStream { inputStream ->
                        tikaService.getText(inputStream).takeIf { text -> text.isNotEmpty() }?.let { text ->
                            content.add(text)
                        }
                    }
                }

            }
        }.forEach {
            searchService.save(it)
        }
    }

    override fun delete(caseId: String) {
        searchService.deleteById(caseId, ESProductCase::class)
    }
}