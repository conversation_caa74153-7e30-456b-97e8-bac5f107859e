package zone.loong.cube.news.task

import com.dtflys.forest.annotation.PostRequest
import com.fasterxml.jackson.annotation.JsonProperty
import org.springframework.stereotype.Component
import com.dtflys.forest.annotation.Var
import com.fasterxml.jackson.annotation.JsonFormat
import zone.loong.cube.framework.http.forest.annotation.Rest
import java.time.LocalDateTime


class Response<T> {
    val msg: String? = null
    val code: Int? = null
    val success: Boolean = false
    val data: List<T> = emptyList()
}

class OfficialAccounts() {
    /** 流程id */
    @JsonProperty("fdId")
    val fdId: String = ""

    /** 标题 */
    @JsonProperty("docTitle")
    val docTitle: String = ""

    /** 作者 */
    @JsonProperty("docAuthor")
    val docAuthor: String = ""

    /** 所属部门 */
    @JsonProperty("deptName")
    val deptName: String = ""

    /** 所属部门编码 */
    @JsonProperty("deptCode")
    val deptCode: String = ""

    /** 简介 */
    @JsonProperty("summary")
    var summary: String? = ""

    /** 详情 */
    @JsonProperty("description")
    var description: String? = ""

    /** 内容（富文本） */
    @JsonProperty("content")
    var content: String? = ""

    /** 附件 */
    @JsonProperty("files")
    var files: String? = ""

    /** 外链链接 */
    @JsonProperty("fdLinkUrl")
    var fdLinkUrl: String? = ""

    /** 标签 */
    @JsonProperty("tags")
    var tags: String? = ""

    /** 状态 */
    @JsonProperty("status")
    var status: String? = ""

    /** 创建时间 */
    @JsonProperty("docCreateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    var docCreateTime: LocalDateTime? = null

    /** 修改时间 */
    @JsonProperty("modifiedTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    var modifiedTime: LocalDateTime? = null
}

@Rest(code = "news-official-accounts-oa")
@Component
interface OfficialAccountsClient {
    @PostRequest("/ekp/api/sys-publics/sysPublicsRestService/getNewsList?{suffix}")
    fun fetchOfficialAccounts(@Var("suffix") suffix: String): Response<OfficialAccounts>

    @PostRequest("/ekp/api/sys-publics/sysPublicsRestService/getNewFiles?{suffix}")
    fun fetchGetNewsFiles(@Var("suffix") suffix: String): Response<String>

    @PostRequest("/ekp/api/sys-publics/sysPublicsRestService/getNewContent?{suffix}")
    fun fetchGetNewsContent(@Var("suffix") suffix: String): Response<String>

}