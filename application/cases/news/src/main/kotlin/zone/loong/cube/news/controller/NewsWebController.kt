package zone.loong.cube.news.controller

import jakarta.websocket.server.PathParam
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.log.NewsLog
import zone.loong.cube.news.model.NewsBanner
import zone.loong.cube.news.model.NewsNavigation
import zone.loong.cube.news.model.NewsNewspaper
import zone.loong.cube.news.model.NewsResourceManager
import zone.loong.cube.news.model.dto.NewsResourceManagerSpecification
import zone.loong.cube.news.service.NewsWebService
import zone.loong.cube.platform.annotation.ModuleLog

@RestController
@RequestMapping("/news/webApi")
class NewsWebController(
    private val newsWebService: NewsWebService
) {

    /** 获取标题栏 */
    @GetMapping("/getTitleBar")
    fun getTitleBar() : List<NewsNavigation> {
        return newsWebService.getTitleBar()
    }

    /** 根据模块id获取新闻 */
    @PostMapping("/getNewsByModule")
    fun getNewsByModule(@RequestBody pageQuery: PageQuery<NewsResourceManagerSpecification>): Page<NewsResourceManager> {
        return newsWebService.getNewsByModule(pageQuery)
    }

    /** 根据新闻id获取新闻详情 */
    @GetMapping("/getNewsDetail")
    @ModuleLog(workspace = "新闻平台", module = "新闻", operation = "查看", identity = "#id", fetch = NewsLog::class)
    fun getNewsDetail(@PathParam("id") id: String): NewsResourceManager {
        return newsWebService.getNewsDetail(id)
    }

    /** 新闻/报刊点击接口 */
    @GetMapping("/addClickIncident")
    fun addClickIncident(@PathParam("id") id: String){
        return newsWebService.addClickIncident(id)
    }

    /** 首页推荐模块接口 */
    @GetMapping("/getRecommendData")
    fun getRecommendData(@PathParam("navigationId") navigationId: String, @PathParam("limitNum") limitNum: Int): Map<String, Any> {
        return newsWebService.getRecommendData(navigationId, limitNum)
    }

    /** 建华人报刊接口 */
    @GetMapping("/getNewsPaperData")
    fun getNewsPaperData(@PathParam("navigationId") navigationId: String): Map<String, Any> {
        return newsWebService.getNewsPaperData(navigationId)
    }

    /** 报刊-上下期/发布时间查询 */
    @GetMapping("/getNewsPaperDetail")
    fun getNewsPaperDetail(@PathParam("periods") periods: Int?, @PathParam("issueDate") issueDate: String): NewsNewspaper {
        return newsWebService.getNewsPaperDetail(periods, issueDate)
    }

    /** 报刊-期数/版面封装 */
    @GetMapping("/getNewsPaperTitleBar")
    fun getNewsPaperTitleBar(): List<Any> {
        return newsWebService.getNewsPaperTitleBar()
    }

    /** 根据栏目获取banner数据 */
    @GetMapping("/getNewsBanner")
    fun getBannerList(@PathParam("navigationId") navigationId: String): List<NewsBanner> {
        return newsWebService.getNewsBanner(navigationId)
    }

    /** 根据栏目获取置顶数据 */
    @GetMapping("/getPragmaticJournalData")
    fun getPragmaticJournalData(@PathParam("navigationId") navigationId: String): Map<String, Any> {
        return newsWebService.getPragmaticJournalData(navigationId)
    }


}