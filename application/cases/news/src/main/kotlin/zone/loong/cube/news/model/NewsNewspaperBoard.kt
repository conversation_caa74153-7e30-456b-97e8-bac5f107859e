package zone.loong.cube.news.model

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.DissociateAction
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.JoinColumn
import org.babyfish.jimmer.sql.JoinTable
import org.babyfish.jimmer.sql.LogicalDeleted
import org.babyfish.jimmer.sql.ManyToMany
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OnDissociate
import org.babyfish.jimmer.sql.OneToOne
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail

/**
 * 报纸板面管理
 */
@Entity
interface NewsNewspaperBoard : AuditAware {

    /**
     * 唯一标识
     */
    val code: String?

    /**
     * 版面标题
     */
    val title: String?

    /**
     * 报纸id
     */
    @ManyToOne
    @JoinColumn(name = "newspaper_id")
    @OnDissociate(DissociateAction.DELETE)
    val newspaper: NewsNewspaper?

    /**
     * 报刊
     */
    @ManyToMany
    @JoinTable(
        name = "NEWS_RESOURCE_MANAGER_NEWSPAPER_BOARD",
        joinColumnName = "NEWSPAPER_BOARD_ID",
        inverseJoinColumnName = "NEWS_ID"
    )
    val newsResourceManager: List<NewsResourceManager>

    /**
     * 版面图
     */
    @OneToOne
    @JoinColumn(name = "image_id")
    val image: FileDetail?

    /**
     * 序列
     */
    val sequence: Integer?

    /**
     * 状态
     */
    val status: String?

    /**
     * 首图展开版面
     */
    val unfold: String?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}

