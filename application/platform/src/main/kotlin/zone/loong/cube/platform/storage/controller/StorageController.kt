package zone.loong.cube.platform.storage.controller

import cn.dev33.satoken.annotation.SaIgnore
import jakarta.servlet.http.HttpServletResponse
import org.dromara.x.file.storage.core.FileInfo
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import zone.loong.cube.framework.storage.kernel.model.FileDetail
import zone.loong.cube.platform.storage.service.StorageService

@RestController
@RequestMapping("/storage")
class StorageController(private val storageService: StorageService) {
    @PostMapping("/getFileByIdList")
    fun getFileById(@RequestBody idList: List<String>): List<FileDetail>{
        return storageService.getFileById(idList)
    }

    @PostMapping("/upload")
    fun upload(@RequestParam("file") file: MultipartFile): String {
        return storageService.upload(file)
    }

    @PostMapping("/uploadInfo")
    fun uploadInfo(@RequestParam("file") file: MultipartFile): FileInfo {
        return storageService.uploadInfo(file)
    }

    @GetMapping("/download/{id}")
    fun download(@PathVariable("id") id: String, httpServletResponse: HttpServletResponse) {
        storageService.download(id, httpServletResponse)
    }

    /**
     * 用于kkFileView 下载
     */
    @GetMapping("/file/{fileName}")
    @SaIgnore
    fun downloadFile(@PathVariable("fileName") fileName: String, httpServletResponse: HttpServletResponse) {
        storageService.downloadByName(fileName, httpServletResponse)
    }

    @GetMapping("/download")
    @SaIgnore
    fun download(
        @RequestParam("id") id: String,
        @RequestParam("token") token: String,
        httpServletResponse: HttpServletResponse
    ) {
        storageService.download(id, token, httpServletResponse)
    }
}