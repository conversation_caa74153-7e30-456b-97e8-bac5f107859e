package zone.loong.cube.framework.cache.jetcache.autoconfigure

import jakarta.annotation.PostConstruct
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.context.annotation.Import
import zone.loong.cube.framework.cache.jetcache.configure.CubeJetCacheConfigure
import zone.loong.cube.framework.kernel.log.annotation.Log
import zone.loong.cube.framework.kernel.log.annotation.Log.Companion.log

@AutoConfiguration
@Import(CubeJetCacheConfigure::class)
@Log
class CubeJetCacheAutoconfigure {
    @PostConstruct
    fun load() {
        log.info("[CUBE FRAMEWORK] MODULE [CACHE-JET-CACHE] LOAD [SUCCESS].")
    }
}