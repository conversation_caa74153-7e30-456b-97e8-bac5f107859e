package zone.loong.cube.cases.product.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.cases.product.constants.ProductConstants
import zone.loong.cube.framework.data.jimmer.model.ACLAware
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.data.kernel.annotations.ACL

@Entity
@Table(name = "CASES_PRODUCT_CLASSIFY")
@ACL(
    type = ProductConstants.CLASSIFY_RESOURCE,
    name = ProductConstants.CLASSIFY_RESOURCE_NAME,
    parentResourceId = "#parent?.id"
)
interface ProductClassify : ACLAware, AuditAware {
    @Key
    val code: String
    val name: String

    @ManyToOne
    val parent: ProductClassify?

    @OneToMany(mappedBy = "parent", orderedProps = [OrderedProp(value = "createdTime", desc = false)])
    val children: List<ProductClassify>

    @OneToMany(mappedBy = "classify", orderedProps = [OrderedProp(value = "createdTime", desc = false)])
    val cases: List<ProductCase>


    fun flattenChildren(): List<ProductClassify> {
        return children + children.flatMap { it.flattenChildren() }
    }

    fun flattenParent(): List<ProductClassify> {
        return parent?.let {
            listOf(it) + it.flattenParent()
        } ?: emptyList()
    }
}