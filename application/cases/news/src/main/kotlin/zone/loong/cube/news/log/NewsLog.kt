package zone.loong.cube.news.log

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.stereotype.Component
import zone.loong.cube.news.model.NewsResourceManager
import zone.loong.cube.news.model.id
import zone.loong.cube.news.model.title
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.platform.annotation.ModuleResourceFetch
import zone.loong.cube.platform.annotation.Resource

@Component
class NewsLog(private val kSqlClient: KSqlClient) : ModuleResourceFetch {
    override fun fetchResource(identity: String): Resource {
        val resourceName = kSqlClient.filters {
            disableByTypes(ACLFilter::class)
        }.createQuery(NewsResourceManager::class) {
            where(table.id eq identity)
            select(table.title)
        }.fetchOne()
        return Resource().apply {
            name = resourceName
        }
    }
}