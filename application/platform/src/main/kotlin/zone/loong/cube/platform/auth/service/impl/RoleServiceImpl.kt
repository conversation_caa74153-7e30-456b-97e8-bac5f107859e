package zone.loong.cube.platform.auth.service.impl

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.*
import zone.loong.cube.platform.auth.model.dto.*
import zone.loong.cube.platform.auth.service.RoleService

@Service
class RoleServiceImpl(private val kSqlClient: KSqlClient) : RoleService {
    override fun fetchRole(pageQuery: PageQuery<RoleSpecification>): Page<Role> {
        return kSqlClient.page(pageQuery) {
            select(table.fetchBy {
                allScalarFields()
            })
        }
    }

    override fun fetchAccounts(roleId: String): List<AccountWithUserInfo> {
        return kSqlClient.createQuery(Account::class) {
            where(table.roles {
                id eq roleId
            })
            select(table.fetch(AccountWithUserInfo::class))
        }.execute()
    }

    override fun fetchPermissions(roleId: String): List<String> {
        return kSqlClient.createQuery(Permission::class) {
            where(table.roles {
                id eq roleId
            })
            select(table.id)
        }.execute()
    }

    override fun authorize(roleAuthorization: RoleAuthorization) {
        kSqlClient.entities.save(roleAuthorization)
    }

    //保存角色
    override fun saveRole(role: RoleSave): Role {
        return kSqlClient.entities.save(role).modifiedEntity
    }

    //分配用户
    override fun roleAccount(roleAccount: RoleAccount): Role {
        return kSqlClient.entities.save(roleAccount).modifiedEntity
    }

    //分配权限
    override fun rolePermission(rolePermission: RolePermission): Role {
        return kSqlClient.entities.save(rolePermission).modifiedEntity
    }
}