server:
  port: 9099
spring:
  datasource:
    url: ***********************************************************************
    username: case-manage
    password: WDQWPkxNZTk8r2Qa
    driver-class-name: org.postgresql.Driver
  data:
    redis:
      password: redis_SQ52Pm
      host: *************
      repositories:
        enabled: false
  elasticsearch:
    password: Elastic_2XYrDD
    uris: http://*************:9200
    username: elastic
cube:
  rest:
    hosts:
      - code: organization-sync
        url: http://*************
        port: 8848
      - code: ding-talk-sync
        url: http://www.jianhuabm.com
        port: 6001
      - code: login-by-code
        url: http://**************
        port: 9005
      - code: file-view
        url: http://*************
        port: 5678
      - code: news-official-accounts-oa
#        url: http://oa.jianhuabm.com
#        port: 80
        url: http://*************
        port: 8085
  cache:
    jetcache:
      broadcast-channel: cube-cache-jetcache
      limit: 200
      stat-interval-minutes: 15
  storage:
    local:
      storage-path: /data/storage/file/
      #测试环境：************:9933    本地开发：127.0.0.1:8000
      domain: http://************:9933/api/storage/file/
    preview:
      kk-file-view-url: http://***************:5678/
forest:
  max-connections: 1000
  connect-timeout: 600000
  read-timeout: 600000
