# ACL权限系统性能优化指南

## 性能问题分析

### 原始实现的性能瓶颈
1. **递归深度过深**：在复杂树结构中可能导致栈溢出
2. **单条处理**：逐个处理节点，数据库交互频繁
3. **同步阻塞**：权限刷新会阻塞主业务流程
4. **无限制递归**：没有深度限制，可能导致无限循环

### 优化后的性能特性
1. **广度优先遍历**：避免深度递归，内存使用更稳定
2. **批量操作**：减少数据库交互次数
3. **异步处理**：不阻塞主业务流程
4. **深度限制**：防止无限递归
5. **性能监控**：实时监控和优化建议

## 性能配置详解

### 1. 批处理大小 (batch-size)
```yaml
cube:
  acl:
    performance:
      batch-size: 100  # 推荐值：50-200
```

**影响因素：**
- 数据库性能：高性能数据库可以设置更大的批处理大小
- 内存限制：批处理大小过大会占用更多内存
- 网络延迟：批处理可以减少网络往返次数

**调优建议：**
- 小型系统：50-100
- 中型系统：100-200
- 大型系统：200-500

### 2. 异步处理 (async-enabled)
```yaml
cube:
  acl:
    performance:
      async-enabled: true
```

**适用场景：**
- ✅ 树结构复杂，节点数量多
- ✅ 对实时性要求不高的权限更新
- ✅ 需要避免阻塞主业务流程

**不适用场景：**
- ❌ 需要立即生效的权限变更
- ❌ 简单的权限结构（节点少于100个）

### 3. 最大深度限制 (max-depth)
```yaml
cube:
  acl:
    performance:
      max-depth: 10  # 推荐值：5-15
```

**设置原则：**
- 根据实际业务的组织架构深度设置
- 一般企业组织架构深度不超过10层
- 可以通过监控日志观察实际使用的最大深度

## 性能监控和调优

### 1. 性能指标监控
```kotlin
// 获取性能统计
val stats = aclPerformanceMonitor.getPerformanceStats()

// 关键指标
stats.operationStats.forEach { (operation, stat) ->
    println("操作: $operation")
    println("平均耗时: ${stat.averageTime}ms")
    println("总次数: ${stat.totalCount}")
    println("慢操作次数: ${stat.slowOperationCount}")
}
```

### 2. 优化建议
```kotlin
// 获取自动优化建议
val suggestions = aclPerformanceMonitor.getOptimizationSuggestions()
suggestions.forEach { suggestion ->
    println("建议: $suggestion")
}
```

### 3. 性能阈值设置
- **慢操作阈值**：5秒（可根据业务需求调整）
- **批处理超时**：30秒
- **异步操作超时**：30秒

## 数据库优化

### 1. 索引优化
```sql
-- ACL表索引
CREATE INDEX idx_acl_parent_resource_id ON ACCESS_ACL(parent_resource_id);
CREATE INDEX idx_acl_resource_type ON ACCESS_ACL(resource_type);

-- Policy表索引
CREATE INDEX idx_policy_acl_id ON ACCESS_POLICIES(acl_id);
CREATE INDEX idx_policy_strategy_type ON ACCESS_POLICIES(strategy_type);
CREATE INDEX idx_policy_ref_policy_id ON ACCESS_POLICIES(ref_policy_id);

-- 复合索引
CREATE INDEX idx_policy_acl_strategy ON ACCESS_POLICIES(acl_id, strategy_type);
```

### 2. 查询优化
- 使用批量查询减少数据库往返
- 合理使用 `fetchBy` 只查询需要的字段
- 避免 N+1 查询问题

### 3. 连接池配置
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20    # 根据并发量调整
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
```

## 缓存策略

### 1. ACL缓存
```kotlin
@Cacheable("acl-cache", key = "#resourceId")
fun fetchACLByResourceId(resourceId: String): ACL {
    // 实现缓存逻辑
}
```

### 2. 权限策略缓存
```kotlin
@Cacheable("policy-cache", key = "#resourceId + '_' + #strategyType")
fun fetchPoliciesByStrategy(resourceId: String, strategyType: StrategyType): List<Policy> {
    // 实现缓存逻辑
}
```

### 3. 缓存失效策略
- 权限变更时清除相关缓存
- 设置合理的缓存过期时间
- 使用缓存预热提高首次访问性能

## 性能测试

### 1. 压力测试场景
```kotlin
// 测试大量节点的权限刷新
@Test
fun testLargeTreeRefresh() {
    // 创建1000个节点的树结构
    val rootId = createTreeStructure(1000)
    
    val startTime = System.currentTimeMillis()
    aclManagementService.refreshChildrenPolicies(rootId)
    val duration = System.currentTimeMillis() - startTime
    
    // 验证性能指标
    assertThat(duration).isLessThan(10000) // 10秒内完成
}
```

### 2. 并发测试
```kotlin
@Test
fun testConcurrentRefresh() {
    val futures = (1..10).map { 
        CompletableFuture.runAsync {
            aclManagementService.refreshChildrenPolicies("test-$it")
        }
    }
    
    CompletableFuture.allOf(*futures.toTypedArray()).get(30, TimeUnit.SECONDS)
}
```

## 监控和告警

### 1. 关键指标监控
- 权限刷新平均耗时
- 慢操作频率
- 异步任务队列长度
- 数据库连接池使用率

### 2. 告警规则
```yaml
# 示例告警配置
alerts:
  - name: "ACL慢操作告警"
    condition: "avg_operation_time > 5000ms"
    action: "发送邮件通知"
  
  - name: "ACL异步队列积压"
    condition: "async_queue_size > 100"
    action: "发送短信告警"
```

## 最佳实践总结

### 1. 配置建议
- 根据实际业务规模调整批处理大小
- 在非关键路径启用异步处理
- 设置合理的深度限制
- 配置充足的数据库连接池

### 2. 监控建议
- 定期查看性能统计报告
- 关注慢操作的频率和原因
- 监控系统资源使用情况
- 建立性能基线和告警机制

### 3. 优化建议
- 优先优化频繁执行的操作
- 考虑引入缓存机制
- 定期清理无用的权限数据
- 在业务低峰期执行大批量权限更新

通过这些优化措施，ACL权限系统可以在复杂场景下保持良好的性能表现。
