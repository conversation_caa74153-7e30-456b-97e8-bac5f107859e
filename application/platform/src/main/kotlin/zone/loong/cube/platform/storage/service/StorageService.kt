package zone.loong.cube.platform.storage.service

import jakarta.servlet.http.HttpServletResponse
import org.dromara.x.file.storage.core.Downloader
import org.dromara.x.file.storage.core.FileInfo
import org.springframework.web.multipart.MultipartFile
import zone.loong.cube.framework.storage.kernel.model.FileDetail
import java.net.URL

interface StorageService {
    fun getFileById(idList: List<String>): List<FileDetail>
    fun upload(multipartFile: MultipartFile): String
    fun upload(fileUrl: URL): String
    fun uploadInfo(multipartFile: MultipartFile): FileInfo
    fun download(id: String, httpServletResponse: HttpServletResponse)
    fun downloadByName(id: String, httpServletResponse: HttpServletResponse)
    fun download(id: String, token: String, httpServletResponse: HttpServletResponse)
    fun download(id: String): Downloader
}