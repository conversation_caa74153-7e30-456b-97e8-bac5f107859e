export zone.loong.cube.platform.auth.model.Role
    -> package zone.loong.cube.platform.auth.model.dto


specification RoleSpecification{
    code
    like(name)
}
input RoleSave{
    id?
    code
    name
}

input RolePermission{
    id
    code
    name
    permissions{
        id
        code
        name
        type
        href
        status
        meta
        description
        children*
        parent{
            id
        }
    }
}

input RoleAuthorization{
    id
    permissions?{
        id
    }
    accounts?{
        id
    }
}

input RoleAccount{
    id
    accounts{
        id
    }
}