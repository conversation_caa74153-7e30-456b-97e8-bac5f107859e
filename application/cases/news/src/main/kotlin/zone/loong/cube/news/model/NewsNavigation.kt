package zone.loong.cube.news.model

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.JoinColumn
import org.babyfish.jimmer.sql.Key
import org.babyfish.jimmer.sql.LogicalDeleted
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OneToMany
import org.babyfish.jimmer.sql.OneToOne
import org.babyfish.jimmer.sql.OrderedProp
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail

/**
 * 栏目管理
 */
@Entity
interface NewsNavigation : AuditAware {

    /**
     * 唯一标识
     */
    @Key
    val code: String

    /**
     * 栏目名称
     */
    val title: String?

    /**
     * 父级id
     */
    @ManyToOne
    val parent: NewsNavigation?

    /**
     * 说明
     */
    val remark: String?

    /**
     * 序列
     */
    val sequence: Integer?

    /**
     * 模块图片
     */
    @OneToOne
    @JoinColumn(name = "IMAGE_ID")
    val image: FileDetail?

    /**
     * 状态
     */
    val status: NewsStatus

    /** 模块 */
    @OneToMany(
        mappedBy = "navigation",
        orderedProps = [OrderedProp("sequence", desc = false)]
    )
    val blocks: List<NewsBlock>

    /** banner */
    @OneToMany(mappedBy = "navigation")
    val banners: List<NewsBanner>

    /**
     * 删除状态
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int

    /**
     * 子节点
     */
    @OneToMany(mappedBy = "parent")
    val children: List<NewsNavigation>
}

enum class NewsStatus() {
    ENABLED,
    DISABLED
}