package zone.loong.cube.platform.auth.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.auth.security.model.Permission
import zone.loong.cube.framework.auth.security.model.PermissionStatus
import zone.loong.cube.framework.auth.security.model.PermissionType
import zone.loong.cube.framework.data.jimmer.model.AuditAware

@Entity
@Table(name = "AUTH_PERMISSION")
interface Permission : Permission, AuditAware {
    @Key
    override val code: String
    override val description: String
    override val name: String

    val  sequence: Int

    //权限类型
    override val type: PermissionType

    override val status: PermissionStatus

    //访问路径
    override val href: String?

    //子权限
    @OneToMany(mappedBy = "parent", orderedProps = [OrderedProp(value = "sequence", desc = false)])
    override val children: List<zone.loong.cube.platform.auth.model.Permission>

    @ManyToOne
    override val parent: zone.loong.cube.platform.auth.model.Permission?


    @ManyToMany
    @JoinTable(name = "AUTH_ROLE_PERMISSION_MAPPING")
    override val roles: List<Role>

    @Serialized
    val meta: PermissionMeta?

    @ManyToMany
    @JoinTable(name = "AUTH_GROUP_PERMISSION_MAPPING")
    val groups: List<Group>

}

class PermissionMeta : Meta() {
    //是否隐藏
    var hidden: Boolean = false

    //图标
    var icon: String = "setting"
}