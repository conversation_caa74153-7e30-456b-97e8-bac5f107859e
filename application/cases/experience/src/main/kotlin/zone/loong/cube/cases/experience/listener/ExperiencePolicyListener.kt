package zone.loong.cube.cases.experience.listener

import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.cases.experience.constants.ExperienceConstants
import zone.loong.cube.cases.experience.service.ExperienceESCasesService
import zone.loong.cube.framework.data.jimmer.event.ResourcePolicyUpdate

@Component
class ExperiencePolicyListener(private val esCasesService: ExperienceESCasesService) {
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onPolicyChange(event: ResourcePolicyUpdate) {

        if (event.resourceType == ExperienceConstants.CASE_RESOURCE){
            esCasesService.updateAcl(event.resourceId)
        }

    }
}