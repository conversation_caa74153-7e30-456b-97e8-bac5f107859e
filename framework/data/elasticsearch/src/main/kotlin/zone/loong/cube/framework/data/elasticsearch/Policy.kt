package zone.loong.cube.framework.data.elasticsearch

import org.springframework.data.elasticsearch.annotations.Field
import org.springframework.data.elasticsearch.annotations.FieldType
import zone.loong.cube.framework.data.kernel.model.Policy
import zone.loong.cube.framework.data.kernel.model.PolicyType
import zone.loong.cube.framework.data.kernel.model.StrategyType

class Policy : Policy {
    @Field(type = FieldType.Keyword)
    override var policyType: PolicyType = PolicyType.USER
    override var strategyType: StrategyType = StrategyType.ROOT

    @Field(type = FieldType.Keyword)
    override var value: String = ""
}