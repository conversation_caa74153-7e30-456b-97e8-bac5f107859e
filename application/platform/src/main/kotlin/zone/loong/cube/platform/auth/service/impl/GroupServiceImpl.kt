package zone.loong.cube.platform.auth.service.impl

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.dict.ErrorEnum
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.*
import zone.loong.cube.platform.auth.model.dto.AccountWithUserInfo
import zone.loong.cube.platform.auth.model.dto.GroupAccount
import zone.loong.cube.platform.auth.model.dto.GroupAuthorization
import zone.loong.cube.platform.auth.model.dto.GroupSpecification
import zone.loong.cube.platform.auth.service.GroupService

@Service
class GroupServiceImpl(private val kSqlClient: KSqlClient) : GroupService {
    override fun fetchGroup(pageQuery: PageQuery<GroupSpecification>): Page<Group> {
        return kSqlClient.page(pageQuery)
    }

    override fun fetchAccounts(groupId: String): List<AccountWithUserInfo> {
        return kSqlClient.createQuery(Account::class) {
            where(table.groups {
                id eq groupId
            })
            select(table.fetch(AccountWithUserInfo::class))
        }.execute()
    }

    override fun fetchPermissions(groupId: String): List<String> {
        return kSqlClient.createQuery(Permission::class) {
            where(table.groups {
                id eq groupId
            })
            select(table.id)
        }.execute()
    }

    override fun fetchTerms(groupId: String): Terms? {
      return  kSqlClient.createQuery(GroupTerm::class) {
            where(table.groupId eq groupId)
            select(table.terms)
        }.fetchOneOrNull()
    }

    @Transactional
    override fun authorize(groupAuthorization: GroupAuthorization) {
        kSqlClient.entities.save(groupAuthorization)
    }

    override fun saveGroup(group: Group): Group {
        return kSqlClient.entities.save(group).modifiedEntity
    }

    override fun groupAccount(groupAccount: GroupAccount): Group {
        return kSqlClient.entities.save(groupAccount).modifiedEntity
    }

    override fun getGroupById(id: String): Group {
        return kSqlClient.findById(Group::class, id) ?: throw Exception(ErrorEnum.DATE_NOT_FOUND.message)
    }
}