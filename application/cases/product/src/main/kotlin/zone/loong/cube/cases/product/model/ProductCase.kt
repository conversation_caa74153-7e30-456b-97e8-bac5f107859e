package zone.loong.cube.cases.product.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.cases.product.constants.ProductConstants
import zone.loong.cube.framework.data.jimmer.model.ACLAware
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.data.kernel.annotations.ACL

@Entity
@Table(name = "CASES_PRODUCT")
@ACL(
    type = ProductConstants.CASE_RESOURCE,
    name = ProductConstants.CASE_RESOURCE_NAME,
    parentResourceId = "#classify?.id"
)
interface ProductCase : ACLAware, AuditAware {
    //标题
    val title: String

    //标签
    @Serialized
    val tags: List<String>

    //案例状态
    val status: CaseStatus

    //附件
    @OneToMany(mappedBy = "product" )
    val attachments: List<ProductAttachment>

    @ManyToOne
    val classify: ProductClassify?

}

enum class CaseStatus {
    //上架
    ON_SHELVES,

    //下架
    OFF_SHELVES

}

enum class CaseType {
    //案例
    CASE,

    //产品
    PRODUCT

}