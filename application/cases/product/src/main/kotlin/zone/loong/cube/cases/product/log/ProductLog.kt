package zone.loong.cube.cases.product.log

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.stereotype.Component
import zone.loong.cube.cases.product.model.*
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.storage.kernel.model.originalFilename
import zone.loong.cube.platform.annotation.ModuleResourceFetch
import zone.loong.cube.platform.annotation.Resource

@Component
class ProductCaseFetch(private val kSqlClient: KSqlClient) : ModuleResourceFetch {
    override fun fetchResource(identity: String): Resource {
        val resourceName = kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .createQuery(ProductCase::class) {
                where(table.id eq identity)
                select(table.title)
            }.fetchOne()
        return Resource().apply {
            name = resourceName
        }
    }
}

@Component
class ProductAttachmentLogFetch(private val kSqlClient: KSqlClient) : ModuleResourceFetch {
    override fun fetchResource(identity: String): Resource {
        val resource = kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .createQuery(ProductAttachment::class) {
                where(table.id eq identity)
                select(table.product.title, table.file.originalFilename)
            }.fetchOne()
        return Resource().apply {
            name = "${resource._1}_${resource._2}"
        }
    }
}

