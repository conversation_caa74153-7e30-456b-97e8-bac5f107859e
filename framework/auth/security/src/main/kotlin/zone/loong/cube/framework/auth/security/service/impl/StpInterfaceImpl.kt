package zone.loong.cube.framework.auth.security.service.impl

import cn.dev33.satoken.stp.StpInterface
import org.springframework.stereotype.Service
import zone.loong.cube.framework.auth.security.model.PermissionType
import zone.loong.cube.framework.auth.security.service.AccountService

@Service
class StpInterfaceImpl(private val accountService: AccountService) : StpInterface {
    //只过滤ACTION类型的权限
    override fun getPermissionList(loginId: Any, loginType: String): MutableList<String> {
        loginId as String
        return accountService.fetchAccount(loginId).let { account ->
            account.roles.flatMap { it.permissions.filter { it.type == PermissionType.ACTION }.map { it.code } } +
                    account.groups.flatMap {
                        it.permissions.filter { it.type == PermissionType.ACTION }.map { it.code }
                    }
        }.toMutableList()
    }

    //获取角色，包含角色和用户组
    override fun getRoleList(loginId: Any?, loginType: String?): MutableList<String> {
        loginId as String
        val roles = accountService.fetchAccountRoles(loginId)
        val groupos = accountService.fetchAccountGroups(loginId)
        return (roles.map { "R-$it.uppercase()" } + groupos.map { "G-${it.uppercase()}" }).toMutableList()
    }
}