package zone.loong.cube.framework.http.forest.autoconfigure

import com.dtflys.forest.springboot.ForestAutoConfiguration
import jakarta.annotation.PostConstruct
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Import
import zone.loong.cube.framework.http.forest.RestProperties
import zone.loong.cube.framework.http.forest.configure.ForestConfigure
import zone.loong.cube.framework.kernel.log.annotation.Log
import zone.loong.cube.framework.kernel.log.annotation.Log.Companion.log

@Log
@AutoConfiguration
@Import(ForestAutoConfiguration::class, ForestConfigure::class)
@EnableConfigurationProperties(RestProperties::class)
class ForestAutoconfigure {

    @PostConstruct
    fun load() {
        log.info("[CUBE FRAMEWORK] MODULE [HTTP-FOREST] LOAD [SUCCESS].")
    }
}