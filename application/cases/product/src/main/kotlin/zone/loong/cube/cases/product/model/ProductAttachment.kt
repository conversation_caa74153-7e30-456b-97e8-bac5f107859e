package zone.loong.cube.cases.product.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.IDAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail

@Entity
@Table(name = "cases_product_attachment")
interface ProductAttachment : IDAware {
    @OneToOne
    @Key
    val file: FileDetail

    @ManyToOne
    @Key
    @OnDissociate(DissociateAction.DELETE)
    val product: ProductCase?

    //下载控制
    @ManyToMany(mappedBy = "attachments")
    val limitRecords: List<LimitRecord>

}