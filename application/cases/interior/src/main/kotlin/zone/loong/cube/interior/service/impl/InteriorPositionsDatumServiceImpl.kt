package zone.loong.cube.interior.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.asc
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.interior.model.*
import zone.loong.cube.interior.model.dto.InteriorPositionsDatumSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorPositionsDatum
import zone.loong.cube.interior.service.InteriorPositionsDatumService

@Service
class InteriorPositionsDatumServiceImpl(
    private val kSqlClient: KSqlClient
) : InteriorPositionsDatumService {

    override fun fetchPositionsDatum(pageQuery: PageQuery<InteriorPositionsDatumSpecification>): Page<InteriorPositionsDatum> {
        return kSqlClient.page(pageQuery) {
            pageQuery.specification.parentId?.let {
                where(table.parentId eq it)
            } ?: where(table.parentId eq null)
            orderBy(table.type.asc())
            select(table.fetchBy {
                allScalarFields()
                parent ()
                positions()
                file {
                    originalFilename()
                    metadata()
                }
            })
        }
    }

    override fun save(saveInteriorPositionsDatum: SaveInteriorPositionsDatum): InteriorPositionsDatum {
        return saveInteriorPositionsDatum.id?.let {
            kSqlClient.save(saveInteriorPositionsDatum, SaveMode.UPSERT).modifiedEntity
        } ?: kSqlClient.save(saveInteriorPositionsDatum, SaveMode.INSERT_ONLY).modifiedEntity
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(InteriorProjectDatum::class, id)
    }

    override fun detail(id: String): InteriorPositionsDatum {
        return kSqlClient.findById(newFetcher(InteriorPositionsDatum::class).by {
            allScalarFields()
            parent ()
            positions()
            file {
                originalFilename()
                metadata()
            }
        }, id) ?: throw Exception("未找到数据")
    }

}