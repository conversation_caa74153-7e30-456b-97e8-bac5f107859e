package zone.loong.cube.cases.share.task

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import zone.loong.cube.platform.auth.model.Account
import zone.loong.cube.platform.auth.model.Group

@Component
class ShareGroupSyncTask(
    private val dingTalkSyncClient: DingTalkSyncClient,
    private val kSqlClient: KSqlClient
) {
    //每5分钟执行一次
    @Scheduled(cron = "0 0 0 * * ?")
    fun syncShareGroupUser() {
        val shareGroupUser = dingTalkSyncClient.fetchShareGroupUser()
        if (shareGroupUser.success() && shareGroupUser.data.isNotEmpty()) {
            val group = Group {
                code = "GROUP_SHARE"
                accounts = shareGroupUser.data.map { accountCode ->
                    Account {
                        userName = accountCode
                    }
                }
            }

            kSqlClient.entities.save(group)


        }
    }
}