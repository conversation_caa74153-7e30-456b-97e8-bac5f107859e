package zone.loong.cube.interior.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Service
import zone.loong.cube.interior.model.InteriorRegime
import zone.loong.cube.interior.model.dto.InteriorRegimeSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorRegime
import zone.loong.cube.interior.service.InteriorRegimeService
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

@Service
class InteriorRegimeServiceImpl(
    private val kSqlClient: KSqlClient
): InteriorRegimeService {
    override fun fetchRegimes(pageQuery: PageQuery<InteriorRegimeSpecification>): Page<InteriorRegime> {
        return kSqlClient.page(pageQuery){
            select(table)
        }
    }

    override fun save(saveInteriorRegime: SaveInteriorRegime): InteriorRegime {
        return (saveInteriorRegime.id?.let {
            kSqlClient.entities.saveCommand(saveInteriorRegime, SaveMode.UPSERT).execute().modifiedEntity
        }?:let{
            kSqlClient.entities.saveCommand(saveInteriorRegime, SaveMode.INSERT_ONLY).execute().modifiedEntity
        })
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(InteriorRegime::class, id)
    }

    override fun detail(id: String): InteriorRegime {
        return kSqlClient.findById(InteriorRegime::class, id) ?: throw Exception("找不到制度数据")
    }
}