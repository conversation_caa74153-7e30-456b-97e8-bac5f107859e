package zone.loong.cube.platform.organization.task

import cn.dev33.satoken.secure.BCrypt
import org.babyfish.jimmer.sql.ast.mutation.AssociatedSaveMode
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import zone.loong.cube.platform.auth.model.Account
import zone.loong.cube.platform.auth.model.Role
import zone.loong.cube.platform.organization.model.Org
import zone.loong.cube.platform.organization.model.OrgType
import zone.loong.cube.platform.organization.model.User
import zone.loong.cube.platform.organization.service.sync.Company
import zone.loong.cube.platform.organization.service.sync.OrganizationClient
import zone.loong.cube.platform.organization.service.sync.Response
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter
import java.util.Collections.synchronizedList

@Component
class OrganizationTask(
    private val organizationClient: OrganizationClient,
    private val kSqlClient: KSqlClient
) {


    // 公司同步任务：每天0点
    @Scheduled(cron = "0 0 0 * * ?")
    fun syncCompanyTask() {
        val ts = getTodayZero()
        val syncCompany = organizationClient.fetchCompany(ts)
        val companyList = syncCompany.data.map {
            Org {
                type = OrgType.COMPANY
                id = it.id
                code = it.code
                name = it.name
                parentId = it.parent
                enable = warpEnable(it.enable)
            }
        }.toList()
        kSqlClient.entities.saveEntities(companyList) {
            setMode(SaveMode.UPSERT)
        }
    }

    // 部门同步任务：每天1点
    @Scheduled(cron = "0 0 1 * * ?")
    fun syncDepartmentTask() {
        val ts = getTodayZero()
        val syncCompany = fetchAllCompany(ts)
        val departmentList = synchronizedList(mutableListOf<Org>())
        syncCompany.data.parallelStream().forEach {
            val syncDepartment = organizationClient.fetchDepartment(it.id, ts).data.map {
                Org {
                    type = OrgType.DEPARTMENT
                    id = it.id
                    code = it.code
                    name = it.name
                    parentId = it.parent ?: it.companyId
                    enable = warpEnable(it.enable)
                }
            }.toList()
            departmentList.addAll(syncDepartment)
        }
        kSqlClient.entities.saveEntities(departmentList) {
            setMode(SaveMode.UPSERT)
        }
    }

    // 岗位同步任务：每天1点半
    @Scheduled(cron = "0 30 1 * * ?")
    fun syncPositionTask() {
        val ts = getTodayZero()
        val syncCompany = fetchAllCompany(ts)
        val positionList = synchronizedList(mutableListOf<Org>())
        syncCompany.data.parallelStream().forEach {
            val positions = organizationClient.fetchPosition(it.id, ts).data.map {
                Org {
                    type = OrgType.POSITION
                    id = it.id
                    code = it.id
                    name = it.name
                    parentId = it.deptId
                    enable = it.enable
                }
            }.toList()
            positionList.addAll(positions)
        }
        kSqlClient.entities.saveEntities(positionList) {
            setMode(SaveMode.UPSERT)
        }
    }

    // 人员同步任务：每天2点
    @Scheduled(cron = "0 0 2 * * ?")
    fun syncUserTask() {
        val ts = getTodayZero()
        val syncCompany = fetchAllCompany(ts)
        val userList = synchronizedList(mutableListOf<User>())
        syncCompany.data.parallelStream().forEach { it ->
            val users = organizationClient.fetchUser(it.id + "_GX", ts)
                .data
                .filter {
                    warpEnable(it.enable)
                }
                .map {
                    User {
                        id = it.id
                        code = it.code
                        name = it.name
                        email = it.email
                        phone = it.phone
                        sex = it.sex
                        levelName = it.levelname
                        enable = warpEnable(it.enable)
                        orgs = it.getPostIds().map { postId ->
                            Org {
                                id = postId
                            }
                        }
                        if (warpEnable(it.manager) && warpEnable(it.enable)) {
                            accounts = listOf(
                                Account {
                                    userName = it.code
                                    password = encryptionPassword("P@ssW0rd")
                                    effectiveFrom = LocalDateTime.now()
                                    effectiveUntil = LocalDateTime.now().withYear(9999)
                                    roles = listOf(Role {
                                        id = "01J9TJBNKA1WQZVSS2976WVGT0"
                                    })
                                }
                            )
                        }
                    }
                }
            userList.addAll(users)
        }
        kSqlClient.entities.saveEntities(userList) {
            //防止更新已有数据
            setAssociatedModeAll(AssociatedSaveMode.APPEND_IF_ABSENT)
            setMode(SaveMode.UPSERT)
        }
    }

    private fun warpEnable(enable: Any?): Boolean {
        return when (enable) {
            is Boolean -> enable
            is Number -> enable.toInt() == 1
            is String -> enable == "1" || enable.equals("true", true) || enable.equals("Y", true)
            else -> false
        }
    }

    // 获取当天00:00:00字符串
    private fun getTodayZero(): String {
//        return  "1999-01-01 00:00:00"
        return LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
    }

    private fun encryptionPassword(password: String): String {
        val salt = BCrypt.gensalt(8)
        return BCrypt.hashpw(password, salt)
    }

    private fun fetchAllCompany(ts: String): Response<Company> {
        val ts = "1999-01-01 00:00:00"
        return organizationClient.fetchCompany(ts)
    }

}