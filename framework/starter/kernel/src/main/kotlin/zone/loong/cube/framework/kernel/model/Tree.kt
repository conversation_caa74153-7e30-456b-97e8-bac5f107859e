package zone.loong.cube.framework.kernel.model

class Tree {

    var id: String = ""
    var value: String = ""
    var label: String = ""
    var pid: String = ""
    var children = mutableListOf<Tree>()

    companion object {
        fun <T> convertToTree(data: T, action: (data: T) -> Tree): Tree {
            return action(data)
        }

        fun transformPid(trees: List<Tree>): List<Tree> {
            val result = mutableListOf<Tree>()
            val map = trees.associateBy { it.id }.toMutableMap()

            trees.forEach { tree ->
                val parent = map[tree.pid]
                if (parent != null) {
                    parent.children.add(tree)
                } else {
                    result.add(tree)
                }
            }
            return result
        }
    }
}