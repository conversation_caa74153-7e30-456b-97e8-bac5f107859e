# 正确的ACL架构设计

## Jimmer保存流程理解

### 保存顺序
```
前置关联 → 本体(interceptor) → 后置关联
```

### 拦截器限制
1. **不能修改前置关联**：前置关联已经保存完成
2. **不能在拦截器中保存其他实体**：会导致重入问题
3. **只能修改本体属性**：如设置ID、状态等简单属性

## 正确的ACL初始化架构

### 1. 拦截器职责 (ImprovedACLDraftInterceptor)
```kotlin
@Component
class ImprovedACLDraftInterceptor : DraftInterceptor<ACLAware, ACLAwareDraft> {
    
    override fun beforeSave(draft: ACLAwareDraft, original: ACLAware?) {
        when {
            // 新建资源：只设置aclId，发布事件
            original == null && !isLoaded(draft, ACLAwareDraft::acl) -> {
                val aclId = UlidCreator.getUlid().toString()
                draft.aclId = aclId  // 只修改本体属性
                
                // 通过事件机制处理ACL初始化
                TransactionSync.runBeforeCommit("发布资源创建事件") {
                    publishResourceCreatedEvent(...)
                }
            }
            
            // 更新资源：检测变更，发布事件
            original != null -> {
                detectChangesAndPublishEvents(draft, original)
            }
        }
    }
}
```

**关键点：**
- ✅ 只设置 `draft.aclId`，不进行数据库保存
- ✅ 使用 `TransactionSync.runBeforeCommit` 发布事件
- ✅ 避免在拦截器中调用其他保存操作

### 2. 事件监听器职责 (ImprovedResourceListener)
```kotlin
@Component
class ImprovedResourceListener {
    
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onResourceCreated(event: ResourceCreated) {
        // 在这里进行实际的ACL初始化
        aclManagementService.createACLRecord(
            aclId = event.aclId,
            resourceId = event.resourceId,
            resourceType = event.resourceType,
            resourceName = event.resourceName,
            parentResourceId = event.parentResourceId
        )
    }
}
```

**关键点：**
- ✅ 在事件监听器中进行实际的数据库操作
- ✅ 使用 `MANDATORY` 传播级别，确保在同一事务中
- ✅ 职责单一，只负责ACL的CRUD操作

### 3. 管理服务职责 (ACLManagementService)
```kotlin
@Service
class ACLManagementService {
    
    // 创建ACL记录（由事件监听器调用）
    fun createACLRecord(...): ACL
    
    // 处理父级迁移
    fun handleParentMigration(...)
    
    // 批量权限操作
    fun batchAuthorization(...)
    
    // 性能优化的权限刷新
    fun refreshChildrenPolicies(...)
}
```

## 完整的执行流程

### 新建资源流程
```mermaid
sequenceDiagram
    participant Client
    participant Jimmer
    participant Interceptor
    participant EventListener
    participant ACLService
    participant Database

    Client->>Jimmer: 保存实体
    Jimmer->>Interceptor: beforeSave()
    Interceptor->>Interceptor: 设置aclId
    Interceptor->>Interceptor: 注册事务回调
    Jimmer->>Database: 保存实体
    Jimmer->>EventListener: 触发ResourceCreated事件
    EventListener->>ACLService: createACLRecord()
    ACLService->>Database: 保存ACL记录
    ACLService->>Database: 保存权限策略
```

### 父级迁移流程
```mermaid
sequenceDiagram
    participant Client
    participant Interceptor
    participant EventListener
    participant ACLService
    participant Database

    Client->>Interceptor: 更新实体父级
    Interceptor->>Interceptor: 检测父级变更
    Interceptor->>EventListener: 发布ResourceParentChanged事件
    EventListener->>ACLService: handleParentMigration()
    ACLService->>Database: 更新ACL父级关系
    ACLService->>ACLService: 异步刷新子节点权限
    ACLService->>Database: 批量更新权限策略
```

## 性能优化策略

### 1. 事件驱动架构
- 拦截器只负责检测变更，不执行重操作
- 通过事件机制解耦业务逻辑
- 支持异步处理复杂的权限传播

### 2. 批量操作优化
```kotlin
// 广度优先遍历，避免深度递归
private fun refreshChildrenPoliciesBFS(parentResourceId: String): Int {
    val queue = mutableListOf(parentResourceId)
    var currentDepth = 0
    
    while (queue.isNotEmpty() && currentDepth < maxDepth) {
        val currentBatch = queue.toList()
        queue.clear()
        currentDepth++
        
        // 分批处理，避免单次处理过多数据
        currentBatch.chunked(batchSize).forEach { batch ->
            processBatch(batch, queue, processed)
        }
    }
}
```

### 3. 异步处理
```kotlin
@Async
fun handleChildrenRefreshAsync(parentResourceId: String): CompletableFuture<Void> {
    return CompletableFuture.runAsync({
        refreshChildrenPolicies(parentResourceId)
    }, executor).orTimeout(timeoutSeconds, TimeUnit.SECONDS)
}
```

## 配置和监控

### 1. 性能配置
```yaml
cube:
  acl:
    performance:
      batch-size: 100        # 批处理大小
      async-enabled: true    # 启用异步处理
      max-depth: 10         # 最大递归深度
      timeout-seconds: 30   # 异步超时时间
```

### 2. 性能监控
```kotlin
@Component
class ACLPerformanceMonitor {
    fun startOperation(operationType: String, resourceId: String): OperationContext
    fun endOperation(context: OperationContext, nodeCount: Int = 0)
    fun getPerformanceStats(): ACLPerformanceStats
    fun getOptimizationSuggestions(): List<String>
}
```

## 最佳实践

### 1. 拦截器设计原则
- ❌ 不要在拦截器中进行数据库保存操作
- ❌ 不要在拦截器中调用其他服务的保存方法
- ✅ 只修改当前实体的属性
- ✅ 使用事件机制处理复杂逻辑

### 2. 事务管理
- 使用 `TransactionSync.runBeforeCommit` 确保在同一事务中
- 事件监听器使用 `MANDATORY` 传播级别
- 异步操作使用独立的事务

### 3. 性能优化
- 使用批量操作减少数据库交互
- 实现深度限制防止无限递归
- 提供异步处理选项
- 添加性能监控和告警

### 4. 错误处理
- 在关键节点添加日志记录
- 实现优雅的错误恢复机制
- 提供详细的错误信息

## 迁移指南

### 1. 替换拦截器
```kotlin
// 旧的拦截器（有问题）
class ACLDraftInterceptor {
    override fun beforeSave(draft, original) {
        // ❌ 在拦截器中保存ACL
        saveACL(...)
    }
}

// 新的拦截器（正确）
class ImprovedACLDraftInterceptor {
    override fun beforeSave(draft, original) {
        // ✅ 只设置属性，发布事件
        draft.aclId = generateId()
        publishEvent(...)
    }
}
```

### 2. 配置切换
```yaml
cube:
  acl:
    improved:
      enabled: true  # 启用新架构
```

这个架构完全符合Jimmer的设计理念，避免了拦截器中的重入问题，同时提供了优秀的性能和可维护性。
