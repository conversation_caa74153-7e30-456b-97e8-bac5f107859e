package zone.loong.cube.framework.data.elasticsearch.service

import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.kernel.model.PageQuery
import kotlin.reflect.KClass

interface SearchService {
    fun <E : Any> page(pageQuery: PageQuery<ESSpecification>, kClass: KClass<E>,disableFilter: Boolean = false): ESPage<E>
    fun <E : Any> save(data: E): E
    fun <E : Any> save(data: List<E>): List<E>
    fun <E : Any> deleteById(id: String, kClass: KClass<E>)
}

inline fun <reified E : Any> SearchService.page(pageQuery: PageQuery<ESSpecification>,disableFilter: Boolean = false): ESPage<E> {
    return page(pageQuery, E::class,disableFilter)
}