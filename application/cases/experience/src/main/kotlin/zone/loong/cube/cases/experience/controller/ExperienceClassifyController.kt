package zone.loong.cube.cases.experience.controller

import org.babyfish.jimmer.client.meta.Api
import org.springframework.web.bind.annotation.*
import zone.loong.cube.cases.experience.model.ExperienceClassify
import zone.loong.cube.cases.experience.model.dto.ClassifySpecification
import zone.loong.cube.cases.experience.model.dto.SaveClassify
import zone.loong.cube.cases.experience.service.ExperienceClassifyService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.model.Tree

@RestController
@RequestMapping("/experience/classify")
@Api
class ExperienceClassifyController(private val experienceClassifyService: ExperienceClassifyService) {
    @PostMapping("/page")
    fun fetchClassify(@RequestBody query: PageQuery<ClassifySpecification>): Page<ExperienceClassify> {
        return experienceClassifyService.fetchClassify(query)
    }

    @GetMapping("/tree")
    fun fetchClassify(@RequestParam pid: String?): List<ExperienceClassify> {
        return experienceClassifyService.fetchClassify(pid)
    }


    @GetMapping("/filter")
    fun filterClassify(@RequestParam keyword: String): List<Tree> {
        return experienceClassifyService.filterClassify(keyword)
    }


    @PostMapping("/save")
    fun save(@RequestBody saveClassify: SaveClassify): ExperienceClassify {
        return experienceClassifyService.save(saveClassify)
    }

    @GetMapping("/detail/{classifyId}")
    fun detail(@PathVariable("classifyId") id: String): ExperienceClassify {
        return experienceClassifyService.detail(id)
    }

    @GetMapping("/delete/{classifyId}")
    fun delete(@PathVariable("classifyId") classify: ExperienceClassify) {
        return experienceClassifyService.delete(classify.id)
    }
}