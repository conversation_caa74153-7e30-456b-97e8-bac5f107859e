package zone.loong.cube.framework.data.jimmer.listener

import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.event.ResourceCreated
import zone.loong.cube.framework.data.jimmer.event.ResourceParentChanged
import zone.loong.cube.framework.data.jimmer.event.ResourceDeleted
import zone.loong.cube.framework.data.jimmer.service.ACLManagementService

/**
 * 改进的资源监听器
 * 使用统一的ACL管理服务处理各种资源事件
 */
@Component
class ImprovedResourceListener(
    private val aclManagementService: ACLManagementService
) {

    /**
     * 处理资源创建事件
     * 注意：如果使用了ImprovedACLDraftInterceptor，这个监听器可能不再需要
     * 因为拦截器已经在保存时处理了ACL初始化
     */
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onResourceCreated(event: ResourceCreated) {
        // 如果需要额外的处理逻辑，可以在这里添加
        // 例如：特殊的权限规则、审计日志等
    }

    /**
     * 处理资源父级变更事件
     */
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onResourceParentChanged(event: ResourceParentChanged) {
        aclManagementService.handleParentMigration(
            event.resourceId,
            event.oldParentId,
            event.newParentId
        )
    }

    /**
     * 处理资源删除事件
     */
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onResourceDeleted(event: ResourceDeleted) {
        // 清理相关的ACL数据
        // 这里可以添加ACL清理逻辑
    }
}
