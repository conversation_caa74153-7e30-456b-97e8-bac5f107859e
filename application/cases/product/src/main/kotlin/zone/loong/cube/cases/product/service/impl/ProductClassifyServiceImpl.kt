package zone.loong.cube.cases.product.service.impl

import com.alicp.jetcache.anno.CacheInvalidate
import com.alicp.jetcache.anno.CacheType
import com.alicp.jetcache.anno.Cached
import org.babyfish.jimmer.kt.isLoaded
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.desc
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.like
import org.babyfish.jimmer.sql.kt.ast.expression.or
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.cases.product.model.*
import zone.loong.cube.cases.product.model.dto.ClassifySpecification
import zone.loong.cube.cases.product.model.dto.SaveClassify
import zone.loong.cube.cases.product.service.ProductClassifyService
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.framework.kernel.model.Tree

@Service
class ProductClassifyServiceImpl(
    private val kSqlClient: KSqlClient,
) : ProductClassifyService {
    @Cached(name = "product-classify-cache-", key = "#pid==null?'root':pid", expire = 3600, cacheType = CacheType.BOTH, cacheNullValue = true)
    override fun fetchClassify(pid: String?): List<ProductClassify> {
        return kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .createQuery(ProductClassify::class) {
                pid?.takeIf { it.isNotEmpty() }?.let {
                    where(table.parentId eq pid)
                } ?: where(table.parentId eq null)
                orderBy(table.createdTime)
                select(table.fetchBy {
                    allTableFields()
                    parent()
                    `children*`()
                })
            }.execute()
    }

    @CacheInvalidate(name = "product-classify-cache-", key = "#pid==null?'root':pid")
    override fun clearProductClassifyCache(pid: String?) {
    }
    override fun fetchClassify(pageQuery: PageQuery<ClassifySpecification>): Page<ProductClassify> {
        return kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .page(pageQuery) {
                orderBy(table.parentId.desc())
                select(table)
            }
    }

    override fun filterClassify(keyword: String): List<Tree> {
        val classifies = kSqlClient.createQuery(ProductClassify::class) {
            where(or(table.name like keyword, table.code eq keyword))
            select(table.fetchBy {
                code()
                name()
                parent {
                    code()
                    name()
                }
                `parent*`()
            })
        }.execute()

        //展平树
        val flatten = classifies.flatMap { it.flattenParent() + it }.distinctBy { it.id }
        val tree = flatten.map {
            Tree().apply {
                id = it.id
                label = it.name
                value = it.code
                if (isLoaded(it, ProductClassify::parent)) {
                    pid = it.parent?.id ?: ""
                }
                children = mutableListOf()
            }
        }
        return Tree.transformPid(tree)
    }

    override fun detail(id: String): ProductClassify {
        return kSqlClient.findById(ProductClassify::class, id) ?: throw BizException("该目录不存在或者已经被删除")
    }

    @Transactional
    override fun save(saveClassify: SaveClassify): ProductClassify {
        return kSqlClient.entities.save(saveClassify).modifiedEntity
    }


    override fun delete(id: String) {
        //当前分类下有子分类不允许删除
        val existsChildren = kSqlClient.createQuery(ProductClassify::class) {
            where(table.parentId eq id)
            select(table.id)
        }.exists()
        if (existsChildren) {
            throw BizException("当前分类下有子分类不允许删除!")
        }
        //当前分类下有案例不允许删除
        val existsCase = kSqlClient.createQuery(ProductCase::class) {
            where(table.classifyId eq id)
            select(table.id)
        }.exists()
        if (existsCase) {
            throw BizException("当前分类下有案例，不允许删除!")
        }

        kSqlClient.deleteById(ProductClassify::class, id).also {
            clearProductClassifyCache()
        }
    }


}
