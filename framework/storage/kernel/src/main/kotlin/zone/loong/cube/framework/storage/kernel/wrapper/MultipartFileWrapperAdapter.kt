package zone.loong.cube.framework.storage.kernel.wrapper

import org.dromara.x.file.storage.core.file.FileWrapper
import org.dromara.x.file.storage.core.file.FileWrapperAdapter
import org.springframework.web.multipart.MultipartFile


/**
 * MultipartFile 文件包装适配器
 */
class MultipartFileWrapperAdapter : FileWrapperAdapter {
    override fun isSupport(source: Any): Boolean {
        return source is MultipartFile || source is MultipartFileWrapper
    }

    override fun getFileWrapper(source: Any, name: String?, contentType: String?, size: Long?): FileWrapper {
        if (source is MultipartFileWrapper) {
            return updateFileWrapper(source, name, contentType, size)
        } else {
            val file = source as MultipartFile
            return MultipartFileWrapper(file, name?:file.originalFilename, contentType?:file.contentType, size?:file.size)
        }
    }
}
