export zone.loong.cube.news.model.NewsResourceManager
    -> package zone.loong.cube.news.model.dto

specification NewsResourceManagerSpecification {
    like(title)
    like(author)
    eq(issueTime)
    eq(issueDept)
    eq(newsType)
    flat(newsArticles) {
        flat(newsBlock) {
            associatedIdEq(navigation)
            eq(id) as newsBlockId
        }
    }
    flat(newspaperBoards) {
        eq(id) as newspaperBoardId
        eq(title) as newspaperBoardTitle
        valueIn(id) as newspaperBoardIds
        associatedIdIn(newspaper) as newspaperIds
    }
}

specification PageSpecification  {
    like(title)
    like(author)
    eq(issueTime)
    eq(issueDept)
    eq(newsType)
}

input SaveNewsResourceManager {
    id?
    fdId
    title
    tags
    author
    issueDept
    issueTime
    issuer
    issueUnit
    contentBrief
    content
    contentType
    newsType
    readingNum
    id(contentFile)
    attachments?{
        id(file)
    }
//    newsBlocks?{
//        id
//    }
    newspaperBoards?{
        id
    }
}



