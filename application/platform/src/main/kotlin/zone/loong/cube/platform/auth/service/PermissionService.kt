package zone.loong.cube.platform.auth.service

import zone.loong.cube.platform.auth.model.Permission
import zone.loong.cube.platform.auth.model.dto.PermissionSave
import zone.loong.cube.platform.auth.model.dto.PermissionTree

interface PermissionService {
    fun fetchPermission(): List<PermissionTree>
    fun fetchPermission(id: String): Permission
    fun savePermission(savePermission: PermissionSave): Permission
    fun fetchRolePermission(groupId: String): List<Permission>
    fun fetchGroupPermission(roleId: String): List<Permission>
}