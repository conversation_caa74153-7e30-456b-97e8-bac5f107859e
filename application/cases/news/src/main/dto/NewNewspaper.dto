export zone.loong.cube.news.model.NewsNewspaper
    -> package zone.loong.cube.news.model.dto

specification NewsNewsPaperSpecification {
    like(title)
    eq(code)
    flat(newsBlock) {
        eq(id) as newsBlockId
        associatedIdEq(navigation)
    }
}

input SaveNewsNewsPaper {
    id?
    title
    code
    tags
    periods
    pageNum
    sponsor
    publishDate
    issueTime
    issuer
    issueUnit
    status
    id(newsBlock)
    id(file)
}

