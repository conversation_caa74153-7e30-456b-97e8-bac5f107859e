package zone.loong.cube.news.controller

import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.news.model.NewsBlock
import zone.loong.cube.news.model.dto.NewsBlockSpecification
import zone.loong.cube.news.model.dto.SaveNewsBlock
import zone.loong.cube.news.service.NewsBlockService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

@RestController
@RequestMapping("/news/block")
class NewsBlockController(
    private val newsBlockService: NewsBlockService
) {

    @PostMapping("/page")
    fun fetch(@RequestBody pageQuery: PageQuery<NewsBlockSpecification>): Page<NewsBlock> {
        return newsBlockService.fetchBlocks(pageQuery)
    }

    @GetMapping("/getBlockSelect")
    fun selectList(): List<NewsBlock> {
        return newsBlockService.fetchSelectList()
    }

    @PostMapping("/save")
    fun save(@RequestBody saveNewsBlock: SaveNewsBlock): NewsBlock {
        return newsBlockService.save(saveNewsBlock)
    }

    @DeleteMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        newsBlockService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): NewsBlock {
        return newsBlockService.detail(id)
    }





}