package zone.loong.cube.framework.tika.configure

import org.apache.tika.Tika
import org.apache.tika.config.TikaConfig
import org.apache.tika.parser.AutoDetectParser
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.core.io.ResourceLoader
import org.springframework.stereotype.Component
import zone.loong.cube.framework.tika.TikaProperties
import zone.loong.cube.framework.tika.service.TikaService
import zone.loong.cube.framework.tika.service.impl.TikaServiceImpl

@Component
@EnableConfigurationProperties(TikaProperties::class)
class TikaConfigure(private val resourceLoader: ResourceLoader, private val properties: TikaProperties) {
    @Bean
    fun tika(): Tika {
        val resource = resourceLoader.getResource("classpath:tika-config.xml")
        resource.inputStream.use { input ->
            val tikaConfig = TikaConfig(input)
            val detector = tikaConfig.detector
            val parser = AutoDetectParser(detector)
            return Tika(detector, parser)
        }
    }

    @Bean
    fun tikaService(tika :Tika): TikaService {
        return TikaServiceImpl(tika, properties)
    }

}