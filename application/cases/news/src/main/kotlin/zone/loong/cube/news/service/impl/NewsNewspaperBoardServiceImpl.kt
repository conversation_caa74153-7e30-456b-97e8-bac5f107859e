package zone.loong.cube.news.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsNewspaperBoard
import zone.loong.cube.news.model.dto.NewsNewspaperBoardSpecification
import zone.loong.cube.news.model.dto.SaveNewsNewspaperBoard
import zone.loong.cube.news.model.fetchBy
import zone.loong.cube.news.service.NewsNewspaperBoardService

@Service
class NewsNewspaperBoardServiceImpl(
    private val kSqlClient: KSqlClient
) : NewsNewspaperBoardService {

    override fun fetchResourceManagers(pageQuery: PageQuery<NewsNewspaperBoardSpecification>): Page<NewsNewspaperBoard> {
        return kSqlClient.page(pageQuery){
            select(table.fetchBy {
                allScalarFields()
                newspaper {
                    title()
                }
                image {
                    url()
                    originalFilename()
                }
            })
        }
    }

    override fun save(saveNewsNewspaperBoard: SaveNewsNewspaperBoard): NewsNewspaperBoard {
        return (saveNewsNewspaperBoard.id?.let {
            kSqlClient.entities.saveCommand(saveNewsNewspaperBoard, SaveMode.UPSERT).execute().modifiedEntity
        }?:let{
            kSqlClient.entities.saveCommand(saveNewsNewspaperBoard, SaveMode.INSERT_ONLY).execute().modifiedEntity
        })
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(NewsNewspaperBoard::class, id)
    }

    override fun detail(id: String): NewsNewspaperBoard {
        return kSqlClient.findById(NewsNewspaperBoard::class, id) ?: throw Exception("该模块未找到")
    }

}