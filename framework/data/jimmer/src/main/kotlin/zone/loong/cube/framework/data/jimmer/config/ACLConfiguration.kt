package zone.loong.cube.framework.data.jimmer.config

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import zone.loong.cube.framework.data.jimmer.interceptor.ACLDraftInterceptor
import zone.loong.cube.framework.data.jimmer.interceptor.ImprovedACLDraftInterceptor
import zone.loong.cube.framework.data.jimmer.listener.ResourceListener
import zone.loong.cube.framework.data.jimmer.listener.ImprovedResourceListener

/**
 * ACL配置类
 * 支持在新旧实现之间切换
 */
@Configuration
class ACLConfiguration {

    /**
     * 启用改进的ACL实现
     */
    @Bean
    @Primary
    @ConditionalOnProperty(
        name = ["cube.acl.improved.enabled"],
        havingValue = "true",
        matchIfMissing = true
    )
    fun improvedACLDraftInterceptor(
        improvedACLDraftInterceptor: ImprovedACLDraftInterceptor
    ): ImprovedACLDraftInterceptor {
        return improvedACLDraftInterceptor
    }

    /**
     * 保留原有的ACL实现作为备选
     */
    @Bean
    @ConditionalOnProperty(
        name = ["cube.acl.improved.enabled"],
        havingValue = "false"
    )
    fun legacyACLDraftInterceptor(
        aclDraftInterceptor: ACLDraftInterceptor
    ): ACLDraftInterceptor {
        return aclDraftInterceptor
    }
}
