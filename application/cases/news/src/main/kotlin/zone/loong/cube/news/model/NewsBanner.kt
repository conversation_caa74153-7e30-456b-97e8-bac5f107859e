package zone.loong.cube.news.model

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.JoinColumn
import org.babyfish.jimmer.sql.LogicalDeleted
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OneToOne
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail
import java.time.LocalDate

/**
 * banner管理
 */
@Entity
interface NewsBanner : AuditAware {

    /**
     * 唯一标识
     */
    val code: String?

    /**
     * 标题
     */
    val title: String?

    /**
     * 封面
     */
    @OneToOne
    @JoinColumn(name = "IMAGE_ID")
    val image: FileDetail?

    /**
     * 栏目id
     */
    @ManyToOne
    @JoinColumn(name = "NAVIGATION_ID")
    val navigation: NewsNavigation?

    /**
     * 发布日期
     */
    val issueDate: LocalDate?

    /**
     * 发布人
     */
    val issuer: String?

    /**
     * 文章链接
     */
    val articleUrl: String?

    /**
     * 序列
     */
    val sequence: Integer?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}