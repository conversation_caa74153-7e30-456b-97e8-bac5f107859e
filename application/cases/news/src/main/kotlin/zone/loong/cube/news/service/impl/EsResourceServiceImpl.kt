package zone.loong.cube.news.service.impl

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.springframework.data.elasticsearch.core.ElasticsearchOperations
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.elasticsearch.service.SearchService
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.framework.tika.service.TikaService
import zone.loong.cube.news.model.ContentType
import zone.loong.cube.news.model.NewsResourceManager
import zone.loong.cube.news.model.elasticsearch.ESNewsDispersion
import zone.loong.cube.news.model.fetchBy
import zone.loong.cube.news.model.id
import zone.loong.cube.news.service.EsResourceService
import zone.loong.cube.platform.storage.service.StorageService

@Service
class EsResourceServiceImpl(
    private val searchService: SearchService,
    private val kSqlClient: KSqlClient,
    private val storageService: StorageService,
    private val elasticsearchOperations: ElasticsearchOperations,
    private val tikaService: TikaService
) : EsResourceService {
    override fun sync(resourceId: String) {
        val resource = kSqlClient.createQuery(NewsResourceManager::class) {
            where(table.id eq resourceId)
            select(table.fetchBy {
                title()
                tags()
                author()
                issueTime()
                content()
                contentBrief()
                contentType()
                contentFile()
                attachments {
                    file {
                        url()
                    }
                }
            })
        }.fetchOneOrNull() ?: throw BizException("新闻不存在或被删除")
        val esResource = ESNewsDispersion.buildFromResource(resource).apply {
            content = extractContent(resource)
        }
        searchService.save(esResource)
    }

    private fun extractContent(resource: NewsResourceManager): String {
        return when (resource.contentType) {
            ContentType.FILE -> {
                var content = ""
                storageService.download(resource.contentFile!!.id).inputStream { inputStream ->
                    content = tikaService.getText(inputStream)
                }
                return cleanRichText(content)
            }

            ContentType.CONTENT -> {
                cleanRichText(resource.content ?: "")
            }

        }
    }

    private fun cleanRichText(text: String): String {
        // 先处理特殊标签（style/script）
        val specialTagsRegex = "(?s)<(style|script)[^>]*>.*?</\\1>".toRegex()
        val step1 = text.replace(specialTagsRegex, "")

        val regex = "<.*?>".toRegex()
        return step1.replace(regex, "")

    }

    override fun delete(resourceId: String) {
        searchService.deleteById(resourceId, ESNewsDispersion::class)
    }

    override fun syncWithFile(resourceIds: List<String>) {
        kSqlClient.createQuery(NewsResourceManager::class) {
            where(table.id valueIn resourceIds)
            select(table.fetchBy {
                title()
                tags()
                author()
                issueTime()
                content()
                contentBrief()
                contentType()
                contentFile()
                attachments {
                    file {
                        url()
                    }
                }
            })
        }.map {
            ESNewsDispersion.buildFromResource(it).apply {
                content = extractContent(it)
            }
        }.forEach {
            searchService.save(it)
        }
    }
}