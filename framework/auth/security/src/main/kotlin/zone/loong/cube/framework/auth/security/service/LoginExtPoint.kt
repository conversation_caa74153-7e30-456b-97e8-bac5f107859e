package zone.loong.cube.framework.auth.security.service

import zone.loong.cube.framework.auth.security.exception.AuthException
import zone.loong.cube.framework.kernel.extension.ExtensionPointI

interface LoginExtPoint : ExtensionPointI {
    /**
     * @param accountIdentity 账号
     * 错误请抛出AuthException
     */
    @Throws(AuthException::class)
    fun valid(accountIdentity: String, password: String): String
}