package zone.loong.cube.platform.login

import com.dtflys.forest.annotation.GetRequest
import com.dtflys.forest.annotation.Header
import com.dtflys.forest.annotation.JSONBody
import com.dtflys.forest.annotation.PostRequest
import com.fasterxml.jackson.annotation.JsonProperty
import org.springframework.stereotype.Service
import zone.loong.cube.framework.http.forest.annotation.Rest

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2024/11/14
 */

class Response<T> {
    val errmsg: String? = null
    val success: Boolean = false
    val code: String? = null
    val data: T? = null
}

class tokenResponse {
    @JsonProperty("access_token")
    val accessToken: String = ""
}

class appTokenRequest {
    @JsonProperty("appKey")
    var appKey: String = ""

    @JsonProperty("appSecret")
    var appSecret: String = ""
}

class appTokenResponse {
    @JsonProperty("app_token")
    val appToken: String = ""

    @JsonProperty("expires_in")
    val expiresIn: String = ""
}

class userInfoResponse {
    @JsonProperty("user_name")
    val userName: String = ""
}

@Service
@Rest(code = "login-by-code")
interface LoginService {
    @PostRequest("/auth_center/api/app_token")
    fun getAppToken(
        @JSONBody("app_key") appKey: String,
        @JSONBody("app_secret") appSecret: String
    ): Response<appTokenResponse>

    @GetRequest("/auth_center/api/sso_login?code={0}")
    fun doLogin(
        appSecret: String, @Header("X-APP-TOKEN") appToken: String
    ): Response<tokenResponse>

    @GetRequest("/auth_center/api/user/me")
    fun getUserInfo(
        @Header("Authorization") token: String, @Header("X-APP-TOKEN") appToken: String
    ): Response<userInfoResponse>
}