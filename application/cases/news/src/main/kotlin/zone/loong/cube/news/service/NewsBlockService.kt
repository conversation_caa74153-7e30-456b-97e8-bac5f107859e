package zone.loong.cube.news.service

import zone.loong.cube.news.model.NewsBlock
import zone.loong.cube.news.model.dto.NewsBlockSpecification
import zone.loong.cube.news.model.dto.SaveNewsBlock
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

interface NewsBlockService {
    fun fetchBlocks(pageQuery: PageQuery<NewsBlockSpecification>): Page<NewsBlock>
    fun save(saveNewsBlock: SaveNewsBlock): NewsBlock
    fun delete(id: String)
    fun detail(id: String): NewsBlock
    fun fetchSelectList(): List<NewsBlock>
}