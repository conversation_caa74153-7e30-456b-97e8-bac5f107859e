package zone.loong.cube.cases.product.controller

import org.babyfish.jimmer.client.meta.Api
import org.springframework.web.bind.annotation.*
import zone.loong.cube.cases.product.model.ProductClassify
import zone.loong.cube.cases.product.model.dto.ClassifySpecification
import zone.loong.cube.cases.product.model.dto.SaveClassify
import zone.loong.cube.cases.product.service.ProductClassifyService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.model.Tree

@RestController
@RequestMapping("/product/classify")
@Api
class ProductClassifyController(private val classifyService: ProductClassifyService) {
    @PostMapping("/page")
    fun fetchClassify(@RequestBody query: PageQuery<ClassifySpecification>): Page<ProductClassify> {
        return classifyService.fetchClassify(query)
    }

    @GetMapping("/tree")
    fun fetchClassify(@RequestParam pid: String?): List<ProductClassify> {
        return classifyService.fetchClassify(pid)
    }


    @GetMapping("/filter")
    fun filterClassify(@RequestParam keyword: String): List<Tree> {
        return classifyService.filterClassify(keyword)
    }


    @PostMapping("/save")
    fun save(@RequestBody saveClassify: SaveClassify): ProductClassify {

        return classifyService.save(saveClassify).also {
            classifyService.clearProductClassifyCache()
        }

    }

//    @PostMapping("/init")
//    fun init(@RequestBody initClassify: InitClassify): ProductClassify {
////        return classifyService.save(initClassify)
//    }

    @GetMapping("/detail/{classifyId}")
    fun detail(@PathVariable("classifyId") id: String): ProductClassify {
        return classifyService.detail(id)
    }

    @GetMapping("/delete/{classifyId}")
    fun delete(@PathVariable("classifyId") classify: ProductClassify) {
        return classifyService.delete(classify.id)
    }

}