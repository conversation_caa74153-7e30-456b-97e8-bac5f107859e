server:
  port: 9099
spring:
  datasource:
    url: ***********************************************************************************************
    username: jhjc_kc
    password: V=e0EYASpiL3DKaB
    driver-class-name: org.postgresql.Driver
  data:
    redis:
      password: redis_f8JzCy
      host: *************
      repositories:
        enabled: false
      database: 10
  elasticsearch:
    password: 2$8c%SH%Sq2ViA55
    uris: http://*************:9200
    username: elastic
cube:
  security:
    expire-time: 14400
  rest:
    hosts:
      - code: organization-sync
        url: http://*************
        port: 8848
      - code: ding-talk-sync
        url: http://www.jianhuabm.com
        port: 6001
      - code: login-by-code
        url: http://*************
        port: 9005
      - code: file-view
        url: http://*************
        port: 5678
      - code: news-official-accounts-oa
        url: http://oa.jianhuabm.com
        port: 80
  cache:
    jetcache:
      broadcast-channel: cube-cache-jetcache
      limit: 200
      stat-interval-minutes: 15
  storage:
    local:
      storage-path: /data/storage/file/
      domain: http://***********:81/api/storage/file/
    preview:
      kk-file-view-url: http://***************:5678/
  app:
    appKey: 54579b9a5017e5d31403da9bab0a945022abb5f41eb69f242b57c26e6586ac7c
    appSecret: 9a927dfc97792b82a04205c26d1017994cbfd2e783095a1e1576399177ff78ac