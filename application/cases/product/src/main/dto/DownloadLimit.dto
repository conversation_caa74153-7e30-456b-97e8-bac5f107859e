export zone.loong.cube.cases.product.model.DownloadLimit
    -> package zone.loong.cube.cases.product.model.dto

specification DownLoadLimitSpecification{
    tag
    flat(groups) {
        as(^ -> group) {
            name
        }
    }
}

input SaveDownLoadLimit{
    id?
    tag
    groups{
        id
    }
    limits
}


 DownLoadLimitView{
    id
    tag
    groups{
        id
        name
    }
    limits
}