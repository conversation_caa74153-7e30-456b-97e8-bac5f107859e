package zone.loong.cube.interior.controller

import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.interior.model.InteriorProject
import zone.loong.cube.interior.model.dto.InteriorProjectSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorProject
import zone.loong.cube.interior.service.InteriorProjectService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

@RestController
@RequestMapping("/interior/project")
class InteriorProjectController(
    private val interiorProjectService: InteriorProjectService
) {

    @PostMapping("/page")
    fun fetch(@RequestBody pageQuery: PageQuery<InteriorProjectSpecification>): Page<InteriorProject> {
        return interiorProjectService.fetchProjects(pageQuery)
    }

    @PostMapping("/save")
    fun save(@RequestBody saveInteriorProject: SaveInteriorProject): InteriorProject {
        return interiorProjectService.save(saveInteriorProject)
    }

    @DeleteMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        interiorProjectService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): InteriorProject {
        return interiorProjectService.detail(id)
    }
}