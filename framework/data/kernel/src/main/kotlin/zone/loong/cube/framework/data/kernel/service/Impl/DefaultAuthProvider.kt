package zone.loong.cube.framework.data.kernel.service.Impl

import zone.loong.cube.framework.data.kernel.service.AuthProvider

/**
 * 默认实现，当没有获取auditUserService的时候，默认给定System
 */
class DefaultAuthProvider : AuthProvider {
    override fun getUserIdentity(): String {
        return "system"
    }


    override fun getUserRoles(): List<String> {
        return emptyList()
    }

    override fun getUserGroups(): List<String> {
        return emptyList()
    }

    override fun getUserOrganizationIds(): List<String> {
        return emptyList()
    }
}