package zone.loong.cube.news.controller

import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsNewspaperBoard
import zone.loong.cube.news.model.dto.NewsNewspaperBoardSpecification
import zone.loong.cube.news.model.dto.SaveNewsNewspaperBoard
import zone.loong.cube.news.service.NewsNewspaperBoardService

@RestController
@RequestMapping("/news/newspaperBoard")
class NewsNewspaperBoardController(
    private val newsNewspaperBoardService: NewsNewspaperBoardService
) {

    @PostMapping("/page")
    fun fetch(@RequestBody pageQuery: PageQuery<NewsNewspaperBoardSpecification>): Page<NewsNewspaperBoard> {
        return newsNewspaperBoardService.fetchResourceManagers(pageQuery)
    }

    @PostMapping("/save")
    fun save(@RequestBody saveNewsNewspaperBoard: SaveNewsNewspaperBoard): NewsNewspaperBoard {
        return newsNewspaperBoardService.save(saveNewsNewspaperBoard)
    }

    @DeleteMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        newsNewspaperBoardService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): NewsNewspaperBoard {
        return newsNewspaperBoardService.detail(id)
    }





}