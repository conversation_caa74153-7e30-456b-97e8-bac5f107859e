package zone.loong.cube.platform.auth.model

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.Key
import org.babyfish.jimmer.sql.ManyToMany
import org.babyfish.jimmer.sql.Table
import zone.loong.cube.framework.auth.security.model.Role
import zone.loong.cube.framework.data.jimmer.model.AuditAware

@Entity
@Table(name = "AUTH_ROLE")
interface Role : Role, AuditAware {
    @Key
    override val code: String


    // 角色名称
    override val name: String

    // 角色描述
    override val description: String?

    @ManyToMany(mappedBy = "roles")
    override val accounts: List<Account>

    @ManyToMany(mappedBy = "roles")
    override val permissions: List<Permission>
}