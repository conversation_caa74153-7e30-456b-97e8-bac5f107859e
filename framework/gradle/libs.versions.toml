[versions]
springboot = "3.4.4"
sa-token = "1.42.0"
jimmer = "0.9.106"
postgresql = "42.6.0"
ulid = "5.2.0"
ksp = "2.1.20-2.0.0"
caffeine = "3.1.8"
storage = "2.2.1"
forest="1.6.4"
redisson ="3.38.0"
jetcache ="2.7.7"
tika = "3.1.0"

[libraries]
boot-dependencies = { module = "org.springframework.boot:spring-boot-dependencies", version.ref = "springboot" }
boot-configuration-processor = { module = "org.springframework.boot:spring-boot-configuration-processor", version.ref = "springboot" }
boot-web = { module = "org.springframework.boot:spring-boot-starter-web", version.ref = "springboot" }
redisson= {module="org.redisson:redisson-spring-boot-starter",version.ref="redisson"}
sa-token = { module = "cn.dev33:sa-token-spring-boot3-starter", version.ref = "sa-token" }
sa-token-redisson = { module = "cn.dev33:sa-token-redisson-spring-boot-starter", version.ref = "sa-token" }
jimmer = { module = "org.babyfish.jimmer:jimmer-spring-boot-starter", version.ref = "jimmer" }
jimmer-ksp = { module = "org.babyfish.jimmer:jimmer-ksp", version.ref = "jimmer" }
ulid = { module = "com.github.f4b6a3:ulid-creator", version.ref = "ulid" }
caffeine = { module = "com.github.ben-manes.caffeine:caffeine", version.ref = "caffeine" }
storage = { module = "org.dromara.x-file-storage:x-file-storage-core", version.ref = "storage" }
jetcache-core = {module="com.alicp.jetcache:jetcache-anno",version.ref="jetcache"}
jetcache-redission = {module="com.alicp.jetcache:jetcache-redisson",version.ref="jetcache"}
forest = {module="com.dtflys.forest:forest-spring-boot3-starter",version.ref="forest"}
tika-core = {module="org.apache.tika:tika-core",version.ref="tika"}
tika-parsar = {module="org.apache.tika:tika-parsers-standard-package",version.ref="tika"}

[bundles]
sa-token = [
    "sa-token",
    "sa-token-redisson"
]
jetcache=[
    "jetcache-core",
    "jetcache-redission"
]
tika = [
    "tika-core",
    "tika-parsar"
]

[plugins]
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }


