package zone.loong.cube.framework.tika

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "cube.tika")
class TikaProperties {
    companion object {
        const val PHONE_REGEX: String = "(\\d{3}-?\\d{3}-?\\d{4})|((\\d{11})|(\\d{3})\\d{7})"
        const val ID_CARD_REGEX: String = "(\\d{17}[\\dXx]|\\d{15})"
        const val CREDIT_CARD_REGEX: String = "(\\d{4}-?\\d{4}-?\\d{4}-?\\d{4})"
    }

    var sensitives: List<TikaSensitive> = listOf(
        TikaSensitive(PHONE_REGEX, "手机号"),
        TikaSensitive(ID_CARD_REGEX, "身份证号"),
        TikaSensitive(CREDIT_CARD_REGEX, "信用卡号")
    )
}

class TikaSensitive(var regex: String, var description: String)