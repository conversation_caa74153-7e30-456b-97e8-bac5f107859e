plugins {
    id("pub.ihub.plugin")
    id("pub.ihub.plugin.ihub-git-hooks") apply false
    id("pub.ihub.plugin.ihub-kotlin") apply (false)
    id("pub.ihub.plugin.ihub-test") apply (false)
    id("pub.ihub.plugin.ihub-verification") apply (false)
    id("pub.ihub.plugin.ihub-publish") apply (false)
    id("org.jetbrains.kotlin.plugin.spring") version ("2.0.0") apply false
}
val springBootDependencies = libs.boot.dependencies

subprojects {
    repositories {
        mavenLocal()
        mavenCentral()
    }

    apply {
        plugin("pub.ihub.plugin.ihub-kotlin")
        plugin("pub.ihub.plugin.ihub-test")
        plugin("pub.ihub.plugin.ihub-verification")
        plugin("pub.ihub.plugin.ihub-publish")
        plugin("org.jetbrains.kotlin.plugin.spring")
    }
    dependencies {
        "implementation"(platform(springBootDependencies))
    }
    iHubBom {
        importBoms {
            group("org.springframework.boot").module("spring-boot-dependencies").version("3.4.4")
        }
    }
}


repositories {
    mavenCentral()
}
