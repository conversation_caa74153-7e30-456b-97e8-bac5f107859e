package zone.loong.cube.news.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Service
import zone.loong.cube.news.model.NewsBlock
import zone.loong.cube.news.model.dto.NewsBlockSpecification
import zone.loong.cube.news.model.dto.SaveNewsBlock
import zone.loong.cube.news.service.NewsBlockService
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.fetchBy
import zone.loong.cube.news.model.sequence

@Service
class NewsBlockServiceImpl(
    private val kSqlClient: KSqlClient
) : NewsBlockService {

    override fun fetchBlocks(pageQuery: PageQuery<NewsBlockSpecification>): Page<NewsBlock> {
        return kSqlClient.page(pageQuery){
            orderBy(table.sequence)
            select(table.fetchBy {
                allScalarFields()
                image {
                    originalFilename()
                    metadata()
                    url()
                }
                navigation {
                    allScalarFields()
                }
            })
        }
    }

    override fun save(saveNewsBlock: SaveNewsBlock): NewsBlock {
        return (saveNewsBlock.id?.let {
            kSqlClient.entities.saveCommand(saveNewsBlock, SaveMode.UPSERT).execute().modifiedEntity
        }?:let{
            kSqlClient.entities.saveCommand(saveNewsBlock, SaveMode.INSERT_ONLY).execute().modifiedEntity
        })
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(NewsBlock::class, id)
    }

    override fun detail(id: String): NewsBlock {
        return kSqlClient.findById(NewsBlock::class, id) ?: throw Exception("该模块未找到")
    }

    override fun fetchSelectList(): List<NewsBlock> {
        return kSqlClient.createQuery(NewsBlock::class){
            select(table)
        }.execute()
    }


}