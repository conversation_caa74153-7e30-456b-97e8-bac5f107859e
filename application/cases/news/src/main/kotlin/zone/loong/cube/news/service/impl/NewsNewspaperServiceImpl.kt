package zone.loong.cube.news.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.desc
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsNewspaper
import zone.loong.cube.news.model.dto.NewsNewsPaperSpecification
import zone.loong.cube.news.model.dto.SaveNewsNewsPaper
import zone.loong.cube.news.model.fetchBy
import zone.loong.cube.news.model.periods
import zone.loong.cube.news.service.NewsNewspaperService

@Service
class NewsNewspaperServiceImpl(
    private val kSqlClient: KSqlClient
) : NewsNewspaperService {

    override fun fetchResourceManagers(pageQuery: PageQuery<NewsNewsPaperSpecification>): Page<NewsNewspaper> {
        return kSqlClient.page(pageQuery){
            orderBy(table.periods.desc())
            select(table.fetchBy {
                allScalarFields()
                newsBlock {
                    title()
                    navigation {
                        title()
                    }
                }
                file {
                    allScalarFields()
                }
            })
        }
    }

    override fun fetchResourceManagers(): List<NewsNewspaper> {
        return kSqlClient.createQuery(NewsNewspaper::class){
            select(table.fetchBy {
                title()
                newspaperBoards {
                    title()
                }
            })
        }.execute()
    }

    override fun save(saveNewsNewspaper: SaveNewsNewsPaper): NewsNewspaper {
        return (saveNewsNewspaper.id?.let {
            kSqlClient.entities.saveCommand(saveNewsNewspaper, SaveMode.UPSERT).execute().modifiedEntity
        }?:let{
            kSqlClient.entities.saveCommand(saveNewsNewspaper, SaveMode.INSERT_ONLY).execute().modifiedEntity
        })
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(NewsNewspaper::class, id)
    }

    override fun detail(id: String): NewsNewspaper {
        return kSqlClient.findById(NewsNewspaper::class, id) ?: throw Exception("该报刊未找到")
    }

    override fun fetchNewsPaperSelectList(): List<NewsNewspaper> {
        return kSqlClient.createQuery(NewsNewspaper::class){
            select(table.fetchBy {
                title()
            })
        }.execute()
    }


}