package zone.loong.cube.framework.storage.kernel.service

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.dromara.x.file.storage.core.FileInfo
import org.dromara.x.file.storage.core.recorder.FileRecorder
import org.dromara.x.file.storage.core.upload.FilePartInfo
import zone.loong.cube.framework.storage.kernel.exception.FileException
import zone.loong.cube.framework.storage.kernel.model.*
import java.time.ZoneId
import java.util.*

class FileRecorder(private val kSqlClient: KSqlClient) : FileRecorder {
    override fun save(fileInfo: FileInfo): Boolean {
        kSqlClient.entities.save(StorageConvert.toFileDetail(fileInfo)).let {
            fileInfo.setId(it.modifiedEntity.id)
        }
        return true

    }

    override fun update(fileInfo: FileInfo) {
        kSqlClient.entities.save(StorageConvert.toFileDetail(fileInfo))
    }

    override fun getByUrl(url: String): FileInfo {
        return kSqlClient.createQuery(FileDetail::class) {
            where(table.url eq url)
            select(table)
        }.fetchOneOrNull()?.let { fileDetail ->
            StorageConvert.toFileInfo(fileDetail)
        } ?: throw FileException("文件不存在")
    }

    override fun delete(url: String): Boolean {
        kSqlClient.createDelete(FileDetail::class) {
            where(table.url eq url)
        }.execute()
        return true
    }

    override fun saveFilePart(filePartInfo: FilePartInfo) {
//        kSqlClient.insert(StorageConvert.toFilePartDetail(filePartInfo))
    }

    override fun deleteFilePartByUploadId(uploadId: String) {
//        kSqlClient.createDelete(FilePartDetail::class) {
//            where(table.uploadId eq uploadId)
//        }
    }

    fun getById(fileId: String): FileInfo {
        return kSqlClient.createQuery(FileDetail::class) {
            where(table.id eq fileId)
            select(table)
        }.fetchOneOrNull()?.let { fileDetail ->
            StorageConvert.toFileInfo(fileDetail)
        } ?: throw FileException("文件不存在")
    }
    fun getByName(filename: String): FileInfo {
        return kSqlClient.createQuery(FileDetail::class) {
            where(table.filename eq filename)
            select(table)
        }.fetchOneOrNull()?.let { fileDetail ->
            StorageConvert.toFileInfo(fileDetail)
        } ?: throw FileException("文件不存在")
    }
}

class StorageConvert {
    companion object {
        fun toFileDetail(fileInfo: FileInfo): FileDetail {
            return FileDetail {
                fileInfo.id?.let {
                    id = it
                }
                url = fileInfo.url
                size = fileInfo.size
                filename = fileInfo.filename
                originalFilename = fileInfo.originalFilename
                basePath = fileInfo.basePath
                path = fileInfo.path
                ext = fileInfo.ext
                contentType = fileInfo.contentType
                platform = fileInfo.platform
                thUrl = fileInfo.thUrl
                thFilename = fileInfo.thFilename
                thSize = fileInfo.thSize
                thContentType = fileInfo.thContentType
                objectId = fileInfo.objectId
                objectType = fileInfo.objectType
                metadata = fileInfo.metadata
                userMetadata = fileInfo.userMetadata
                thMetadata = fileInfo.thMetadata
                thUserMetadata = fileInfo.thUserMetadata
                attr = fileInfo.attr
                fileAcl = ""
                thFileAcl = ""
                hashInfo = fileInfo.hashInfo
                uploadId = fileInfo.uploadId
                uploadStatus = fileInfo.uploadStatus
                createdTime = fileInfo.createTime.toInstant().atZone(ZoneId.of("+8")).toLocalDateTime()
            }
        }

        fun toFileInfo(fileDetail: FileDetail): FileInfo {
            return FileInfo().apply {
                id = fileDetail.id
                url = fileDetail.url
                size = fileDetail.size
                filename = fileDetail.filename
                originalFilename = fileDetail.originalFilename
                basePath = fileDetail.basePath
                path = fileDetail.path
                ext = fileDetail.ext
                contentType = fileDetail.contentType
                platform = fileDetail.platform
                thUrl = fileDetail.thUrl
                thFilename = fileDetail.thFilename
                thSize = fileDetail.thSize
                thContentType = fileDetail.thContentType
                objectId = fileDetail.objectId
                objectType = fileDetail.objectType
                metadata = fileDetail.metadata
                userMetadata = fileDetail.userMetadata
                thMetadata = fileDetail.thMetadata
                thUserMetadata = fileDetail.thUserMetadata
                attr = fileDetail.attr
                fileAcl = fileDetail.fileAcl
                createTime = Date.from(fileDetail.createdTime.atZone(ZoneId.of("+8")).toInstant())
            }
        }

        fun toFilePartDetail(filePartInfo: FilePartInfo): FilePartDetail {
            return FilePartDetail {
                filePartInfo.id?.let {
                    id = it
                }
                platform = filePartInfo.platform
                uploadId = filePartInfo.uploadId
                eTag = filePartInfo.eTag
                partNumber = filePartInfo.partNumber
                partSize = filePartInfo.partSize
                hashInfo = filePartInfo.hashInfo
                createTime = filePartInfo.createTime.toInstant().atZone(ZoneId.of("+8")).toLocalDateTime()
            }
        }
    }


}



