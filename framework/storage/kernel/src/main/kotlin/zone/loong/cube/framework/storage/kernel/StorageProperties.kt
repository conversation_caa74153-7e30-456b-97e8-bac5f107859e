package zone.loong.cube.framework.storage.kernel

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "cube.storage")
class StorageProperties {
    var defaultPlatform = "local"
    var thumbnailSuffix = ".th.jpg"

    //    上传时不支持元数据时抛出异常
    var uploadNotSupportMetadataThrowException = false

    //上传时不支持 ACL 时抛出异常
    var uploadNotSupportAclThrowException: Boolean = false

    //    复制时不支持元数据时抛出异常
    var copyNotSupportMetadataThrowException = false

    //    复制时不支持 ACL 时抛出异常
    var copyNotSupportAclThrowException: Boolean = false

    //    移动时不支持元数据时抛出异常
    var moveNotSupportMetadataThrowException: Boolean = false

    //    移动时不支持 ACL 时抛出异常
    var moveNotSupportAclThrowException: Boolean = false
}