package zone.loong.cube.interior.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail

/**
 * 制度数据管理
 */
@Entity
interface InteriorRegimeDatum : AuditAware {

    /**
     * 唯一标识
     */
    @Key
    val code: String?

    /**
     * 名称
     */
    val title: String?

    /**
     * 标签
     */
    @Serialized
    val tags: List<String>

    /**
     * 类型(文件/文件夹)
     */
    val type: FileType

    /**
     * 制度id
     */
    @ManyToOne
    val regime: InteriorRegime?

    /**
     * 父级id
     */
    @ManyToOne
    @OnDissociate(DissociateAction.SET_NULL)
    val parent: InteriorRegimeDatum?

    /**
     * 备注
     */
    val remark: String?

    /**
     * 状态
     */
    val status: String?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int

    /**
     * 子节点
     */
    @OneToMany(mappedBy = "parent")
    val children: List<InteriorRegimeDatum>

    @OneToOne
    @JoinColumn(name = "FILE_ID")
    val file: FileDetail?
}

