package zone.loong.cube.platform.organization.model

import com.fasterxml.jackson.annotation.JsonValue
import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.AuditAware

@Table(name = "SYS_ORG")
@Entity
interface Org : AuditAware {
    @Key
    val type: OrgType

    @Key
    val code: String

    @Key
    val name: String

    @ManyToOne
    val parent: Org?

    val enable: Boolean

    @OneToMany(mappedBy = "parent")
    val children: List<Org>

    @ManyToMany(mappedBy = "orgs")
    val users: List<User>

    //返回铺平数据
    fun flattenChildren(): List<Org> {
        return listOf(this) + children.flatMap { it.flattenChildren() }
    }

    fun flattenParent(): List<Org> {
        return (parent?.flattenParent() ?: emptyList()) + listOf(this)
    }
}

enum class OrgType(@JsonValue val value: String) {

    //公司
    COMPANY("COMPANY"),

    //部门
    DEPARTMENT("DEPARTMENT"),

    //岗位
    POSITION("POSITION"),
}