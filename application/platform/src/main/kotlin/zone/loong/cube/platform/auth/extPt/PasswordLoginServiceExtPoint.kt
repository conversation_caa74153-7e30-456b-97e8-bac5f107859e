package zone.loong.cube.platform.auth.extPt

import cn.dev33.satoken.secure.BCrypt
import org.springframework.stereotype.Service
import zone.loong.cube.framework.auth.security.constants.SecurityConstants
import zone.loong.cube.framework.auth.security.service.AccountService
import zone.loong.cube.framework.auth.security.service.LoginExtPoint
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.framework.kernel.extension.Extension
import zone.loong.cube.modules.auth.model.LoginScenario

@Service
@Extension(
    bizId = SecurityConstants.DEFAULT_LOGIN_EXT_POINT_BIZ_ID,
    useCase = SecurityConstants.DEFAULT_LOGIN_EXT_POINT_USE_CASE,
    scenario = LoginScenario.WEB
)
class PasswordLoginServiceExtPoint(private val accountService: AccountService) : LoginExtPoint {
    override fun valid(accountIdentity: String, password: String): String {
        accountService.fetchAccount(accountIdentity)?.let { account ->
            if (!BCrypt.checkpw(password, account.password)) {
                throw BizException("密码错误")
            }
        } ?: throw BizException("用户不存在")
        return accountIdentity
    }
}