package zone.loong.cube.platform.auth.service.impl

import org.springframework.stereotype.Service
import zone.loong.cube.platform.auth.model.AccessRecord
import zone.loong.cube.platform.auth.model.BanRecord
import zone.loong.cube.platform.auth.service.RecordService

@Service
class RecordServiceImpl : RecordService {
    override fun saveBanRecord(banRecord: BanRecord) {
        TODO("Not yet implemented")
    }

    override fun saveAccessRecord(accessRecord: AccessRecord) {
        TODO("Not yet implemented")
    }
}