package zone.loong.cube.framework.storage.preview.service.impl

import cn.hutool.core.net.URLEncodeUtil
import org.apache.http.client.utils.URIBuilder
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.kernel.TransactionSync
import zone.loong.cube.framework.storage.kernel.service.FileRecorder
import zone.loong.cube.framework.storage.preview.StoragePreviewProperties
import zone.loong.cube.framework.storage.preview.service.KKFileViewClient
import zone.loong.cube.framework.storage.preview.service.PreviewService
import java.util.*

open class PreviewServiceImpl(
                        private val storagePreviewProperties: StoragePreviewProperties,
                        private val recordService: FileRecorder,
                         private val kkFileViewClient: KKFileViewClient): PreviewService {
    override fun preview(fileId: String): String {
        return   recordService.getById(fileId).let {
            val fileUrl = URIBuilder(it.url).build().normalize().toString()
            val base64String = Base64.getEncoder().encodeToString(fileUrl.toByteArray())
            val encodedURIComponent = URLEncodeUtil.encodeAll(base64String)
            "${storagePreviewProperties.kkFileViewUrl}/onlinePreview?url=$encodedURIComponent"
        }

    }
    @Transactional
    override fun addTask(fileId: String) {
        recordService.getById(fileId).run{
            val fileUrl = URIBuilder(url).build().normalize().toString()
            val base64String = Base64.getEncoder().encodeToString(fileUrl.toByteArray())
            val encodedURIComponent = URLEncodeUtil.encodeAll(base64String)
            val previewUrl= "${storagePreviewProperties.kkFileViewUrl}/onlinePreview?url=$encodedURIComponent"
            recordService.update(this.apply {
                id = fileId
                metadata =mapOf("previewUrl" to previewUrl)
            })
            TransactionSync.runAfterCommit("提交预览任务"){
                kkFileViewClient.addTask(url)
            }
        }


    }
}