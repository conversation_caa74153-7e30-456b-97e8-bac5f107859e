package zone.loong.cube.interior.service

import zone.loong.cube.interior.model.InteriorRegime
import zone.loong.cube.interior.model.dto.InteriorRegimeSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorRegime
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

interface InteriorRegimeService {
    fun fetchRegimes(pageQuery: PageQuery<InteriorRegimeSpecification>): Page<InteriorRegime>
    fun save(saveInteriorRegime: SaveInteriorRegime): InteriorRegime
    fun delete(id: String)
    fun detail(id: String): InteriorRegime
}