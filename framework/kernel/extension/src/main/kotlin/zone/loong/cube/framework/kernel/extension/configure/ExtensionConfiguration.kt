package zone.loong.cube.framework.kernel.extension.configure

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import zone.loong.cube.framework.kernel.extension.ExtensionExecutor
import zone.loong.cube.framework.kernel.extension.ExtensionPointI
import zone.loong.cube.framework.kernel.extension.ExtensionRepository
import zone.loong.cube.framework.kernel.extension.register.ExtensionBootstrap
import zone.loong.cube.framework.kernel.extension.register.ExtensionRegister


@Configuration
class ExtensionConfiguration {
    @Bean(initMethod = "init")
    @ConditionalOnMissingBean(ExtensionBootstrap::class)
    fun bootstrap(extensionRegister: ExtensionRegister, extensions: List<ExtensionPointI>): ExtensionBootstrap {
        return ExtensionBootstrap(extensionRegister, extensions)
    }

    @Bean
    @ConditionalOnMissingBean(ExtensionRepository::class)
    fun repository(): ExtensionRepository {
        return ExtensionRepository()
    }

    @Bean
    @ConditionalOnMissingBean(ExtensionExecutor::class)
    fun executor(extensionRepository: ExtensionRepository): ExtensionExecutor {
        return ExtensionExecutor(extensionRepository)
    }

    @Bean
    @ConditionalOnMissingBean(ExtensionRegister::class)
    fun register(extensionRepository: ExtensionRepository): ExtensionRegister {
        return ExtensionRegister(extensionRepository)
    }
}