package zone.loong.cube.cases.product.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.platform.auth.model.Group
import zone.loong.cube.platform.auth.model.Terms

@Entity
@Table(name = "CASES_PRODUCT_AUTHORIZATION_RULE")
interface AuthorizationRule : AuditAware {
    @Serialized
    val terms: Terms
    val expression: String
    val descriptions: String

    @ManyToMany
    @JoinTable(
        name = "CASES_PRODUCT_AUTHORIZATION_RULE_GROUP_MAPPING",
        joinColumnName = "RULE_ID",
        inverseJoinColumnName = "GROUP_ID"
    )
    val groups: List<Group>
}