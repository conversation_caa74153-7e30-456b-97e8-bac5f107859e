package zone.loong.cube.platform.dataAuth.service.impl

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.service.ACLManagementService
import zone.loong.cube.framework.data.jimmer.service.ACLAuthorizationRequest
import zone.loong.cube.framework.data.jimmer.service.PolicySave
import zone.loong.cube.platform.dataAuth.service.ACLService
import zone.loong.cube.platform.auth.model.dto.ACLSave
import zone.loong.cube.platform.auth.model.dto.ACLSpecification
import zone.loong.cube.platform.auth.model.dto.ACLView
import zone.loong.cube.platform.auth.model.dto.AccountWithUserInfo
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.Group
import zone.loong.cube.platform.auth.model.Role
import zone.loong.cube.platform.organization.model.Org
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import zone.loong.cube.framework.data.jimmer.model.ACL
import zone.loong.cube.framework.data.jimmer.model.Policy
import zone.loong.cube.framework.data.jimmer.filter.ACLFilter
import zone.loong.cube.platform.auth.model.Account
import zone.loong.cube.framework.data.kernel.model.PolicyType

/**
 * 改进的ACL服务实现
 * 使用统一的ACL管理服务，简化业务逻辑
 */
@Service
class ImprovedACLServiceImpl(
    private val aclManagementService: ACLManagementService,
    private val kSqlClient: KSqlClient
) : ACLService {

    @Transactional
    override fun authorization(aclSave: ACLSave) {
        val request = ACLAuthorizationRequest(
            resourceId = aclSave.resourceId,
            policies = aclSave.policies.map { policyDto ->
                PolicySave(
                    policyType = policyDto.policyType,
                    value = policyDto.value,
                    ruleId = policyDto.ruleId
                )
            }
        )
        
        aclManagementService.batchAuthorization(listOf(request))
    }

    @Transactional
    override fun authorization(aclSaves: List<ACLSave>) {
        val requests = aclSaves.map { aclSave ->
            ACLAuthorizationRequest(
                resourceId = aclSave.resourceId,
                policies = aclSave.policies.map { policyDto ->
                    PolicySave(
                        policyType = policyDto.policyType,
                        value = policyDto.value,
                        ruleId = policyDto.ruleId
                    )
                }
            )
        }
        
        aclManagementService.batchAuthorization(requests)
    }

    override fun fetchACL(aclSpecification: ACLSpecification, pageQuery: PageQuery): Page<ACLView> {
        return kSqlClient.createQuery(ACL::class) {
            aclSpecification.resourceId?.let { where(table.resourceId eq it) }
            aclSpecification.resourceType?.let { where(table.resourceType eq it) }
            aclSpecification.resourceName?.let { where(table.resourceName eq it) }
            
            select(table.fetch(ACLView::class))
        }.fetchPage(pageQuery.pageIndex, pageQuery.pageSize)
    }

    override fun fetchACL(resourceId: String): ACLView {
        return kSqlClient.createQuery(ACL::class) {
            where(table.resourceId eq resourceId)
            select(table.fetch(ACLView::class))
        }.fetchOneOrNull() ?: throw IllegalArgumentException("ACL not found for resource: $resourceId")
    }

    override fun fetchAccount(resourceId: String): List<AccountWithUserInfo> {
        return kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .createQuery(Account::class) {
                where(table.userName valueIn subQuery(Policy::class) {
                    where(table.policyType eq PolicyType.USER)
                    where(table.acl.resourceId eq resourceId)
                    select(table.value)
                })
                select(table.fetch(AccountWithUserInfo::class))
            }.execute()
    }

    override fun fetchRole(resourceId: String): List<Role> {
        return kSqlClient.createQuery(Role::class) {
            where(table.code valueIn subQuery(Policy::class) {
                where(table.policyType eq PolicyType.ROLE)
                where(table.acl.resourceId eq resourceId)
                select(table.value)
            })
            select(table)
        }.execute()
    }

    override fun fetchGroup(resourceId: String): List<Group> {
        return kSqlClient.createQuery(Group::class) {
            where(table.code valueIn subQuery(Policy::class) {
                where(table.policyType eq PolicyType.GROUP)
                where(table.acl.resourceId eq resourceId)
                select(table.value)
            })
            select(table)
        }.execute()
    }

    override fun fetchOrg(resourceId: String): List<Org> {
        return kSqlClient.createQuery(Org::class) {
            where(table.code valueIn subQuery(Policy::class) {
                where(table.policyType eq PolicyType.ORGANIZATION)
                where(table.acl.resourceId eq resourceId)
                select(table.value)
            })
            select(table)
        }.execute()
    }

    /**
     * 刷新资源的权限继承
     * 当树结构发生变化时调用
     */
    @Transactional
    fun refreshResourcePermissions(resourceId: String) {
        val acl = kSqlClient.createQuery(ACL::class) {
            where(table.resourceId eq resourceId)
            select(table.fetchBy {
                resourceId()
                parent {
                    resourceId()
                }
            })
        }.fetchOneOrNull()
        
        acl?.let {
            aclManagementService.refreshInheritedPolicies(
                resourceId = it.resourceId,
                parentResourceId = it.parent?.resourceId
            )
        }
    }

    /**
     * 批量刷新多个资源的权限
     */
    @Transactional
    fun batchRefreshResourcePermissions(resourceIds: List<String>) {
        resourceIds.forEach { resourceId ->
            refreshResourcePermissions(resourceId)
        }
    }
}
