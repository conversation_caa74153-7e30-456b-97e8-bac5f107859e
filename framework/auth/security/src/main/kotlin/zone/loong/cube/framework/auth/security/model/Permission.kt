package zone.loong.cube.framework.auth.security.model

import com.fasterxml.jackson.annotation.JsonValue

interface Permission {
    // 组编码
    val code: String

    // 组名称
    val name: String

    // 组描述
    val description: String


    val type: PermissionType

    val status: PermissionStatus

    val parent:Permission?

    val children: List<Permission>


    //访问路径
    val href: String?

    // 组关联的账户
    val roles: List<Role>

    fun flattenChildren():List<Permission>{
        return children + children.flatMap { it.flattenChildren() }
    }
}


enum class PermissionStatus(@JsonValue val value: String) {
    ACTIVE("ACTIVE"),
    INACTIVE("INACTIVE"),
    CONCEAL("CONCEAL"),
}


enum class PermissionType(@JsonValue val value: String) {
    WORKSPACE("WORKSPACE"),
    MENU("MENU"),
    ACTION("ACTION"),
}