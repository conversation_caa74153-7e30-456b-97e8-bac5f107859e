package zone.loong.cube.framework.data.kernel.model

import com.fasterxml.jackson.annotation.JsonValue

// 策略
interface Policy {
    val policyType: PolicyType
    val strategyType: StrategyType
    val value: String
}

enum class PolicyType(@JsonValue val value: String) {
    ROLE("ROLE"),
    USER("USER"),
    ORGANIZATION("ORGANIZATION"),
    GROUP("GROUP");
}

enum class StrategyType(@JsonValue val value: String) {
    ROOT("ROOT"),
    DRILL_DOWN("DRILL_DOWN"),
    DRILL_UP("DRILL_UP")
}