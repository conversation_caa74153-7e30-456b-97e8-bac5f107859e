package zone.loong.cube.news.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.elasticsearch.service.SearchService
import zone.loong.cube.framework.data.elasticsearch.service.page
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.news.model.NewsResourceManager
import zone.loong.cube.news.model.dto.PageSpecification
import zone.loong.cube.news.model.dto.SaveNewsResourceManager
import zone.loong.cube.news.model.elasticsearch.ESNewsDispersion
import zone.loong.cube.news.model.fetchBy
import zone.loong.cube.news.model.id
import zone.loong.cube.news.service.EsResourceService
import zone.loong.cube.news.service.NewsResourceManagerService

@Service
class NewsResourceManagerServiceImpl(
    private val kSqlClient: KSqlClient,
    private val esResourceService: EsResourceService,
    private val searchService: SearchService,
) : NewsResourceManagerService {
    override fun fetchNews(pageQuery: PageQuery<ESSpecification>): ESPage<ESNewsDispersion> {
        return searchService.page<ESNewsDispersion>(pageQuery)
    }

    override fun fetchResourceManagers(pageQuery: PageQuery<PageSpecification>): Page<NewsResourceManager> {
        val a = kSqlClient.page(pageQuery) {
            select(table.fetchBy {
                title()
                tags()
                author()
                issueDept()
                issueTime()
                issuer()
                issueUnit()
                readingNum()
                newsType()
                status()
                topStatus()
                fdId()
            })
        }
        return a
    }

    override fun save(saveNewsResourceManager: SaveNewsResourceManager): NewsResourceManager {
        return (saveNewsResourceManager.id?.let {
            kSqlClient.entities.saveCommand(saveNewsResourceManager, SaveMode.UPSERT).execute().modifiedEntity
        } ?: let {
            kSqlClient.entities.saveCommand(saveNewsResourceManager, SaveMode.INSERT_ONLY).execute().modifiedEntity
        }).also {
            esResourceService.sync(it.id)
        }
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(NewsResourceManager::class, id)
        esResourceService.delete(id)
    }

    override fun detail(id: String): NewsResourceManager {
        return kSqlClient.createQuery(NewsResourceManager::class) {
            where(table.id eq id)
            select(table.fetchBy {
                allScalarFields()
                attachments {
                    file {
                        originalFilename()
                        metadata()
                        url()
                    }
                }
                newsArticles {
                    newsBlock {
                        title()
                    }
                }
                newspaperBoards {
                    title()
                }
                contentFile {
                    originalFilename()
                    metadata()
                    url()
                }
            })
        }.fetchOneOrNull() ?: throw BizException("资源不存在或已被删除")
    }

    override fun sync() {
        val ids = kSqlClient.createQuery(NewsResourceManager::class) {
            select(table.id)
        }.execute()
        esResourceService.syncWithFile(ids)
    }


}