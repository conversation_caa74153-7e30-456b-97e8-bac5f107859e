package zone.loong.cube.framework.kernel.extension.register

import org.springframework.aop.support.AopUtils
import org.springframework.core.annotation.AnnotatedElementUtils
import org.springframework.core.annotation.AnnotationUtils
import org.springframework.stereotype.Component
import org.springframework.util.ClassUtils
import zone.loong.cube.framework.kernel.extension.*


@Component
@Suppress("UNCHECKED_CAST")
class ExtensionRegister(private val extensionRepository: ExtensionRepository) {

    fun doRegistration(extensionObject: ExtensionPointI) {
        var extensionClz: Class<ExtensionPointI> = extensionObject.javaClass
        if (AopUtils.isAopProxy(extensionObject)) {
            extensionClz = AopUtils.getTargetClass(extensionObject) as Class<ExtensionPointI>
        }
        val extensionAnn: Extension? = AnnotatedElementUtils.findMergedAnnotation(
            extensionClz,
            Extension::class.java
        )
        extensionAnn?.let {
            val bizScenario: BizScenario =
                BizScenario.valueOf(it.bizId, it.useCase, it.scenario)
            val preVal = extensionRepository.extensionRepo.put(bizScenario, extensionObject)
            if (preVal != null) {
                val errMessage = "Duplicate registration is not allowed for :$bizScenario"
                throw ExtensionException(errMessage)
            }
        }


    }

    fun doRegistrationExtensions(extensionObject: ExtensionPointI) {
        var extensionClz: Class<ExtensionPointI> = extensionObject.javaClass
        if (AopUtils.isAopProxy(extensionObject)) {
            extensionClz = ClassUtils.getUserClass(extensionObject) as Class<ExtensionPointI>
        }

        val extensionsAnnotation: Extensions? = AnnotationUtils.findAnnotation(
            extensionClz,
            Extensions::class.java
        )

        extensionsAnnotation?.let {
            val bizIds: Array<String> = extensionsAnnotation.bizId
            val useCases: Array<String> = extensionsAnnotation.useCase
            val scenarios: Array<String> = extensionsAnnotation.scenario
            for (bizId in bizIds) {
                for (useCase in useCases) {
                    for (scenario in scenarios) {
                        val bizScenario: BizScenario = BizScenario.valueOf(bizId, useCase, scenario)
                        val preVal = extensionRepository.extensionRepo.put(bizScenario, extensionObject)
                        if (preVal != null) {
                            val errMessage =
                                "Duplicate registration is not allowed for :$bizScenario"
                            throw ExtensionException(errMessage)
                        }
                    }
                }
            }
        }

        //Support multiple extensions registration

    }

    companion object {

        /**
         * 扩展点定义重复
         */
        private const val EXTENSION_DEFINE_DUPLICATE = "extension_define_duplicate"

    }
}