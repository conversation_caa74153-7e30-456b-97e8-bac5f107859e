package zone.loong.cube.framework.data.jimmer.interceptor

import org.babyfish.jimmer.sql.DraftInterceptor
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.model.ACLAware
import zone.loong.cube.framework.data.jimmer.model.ACLAwareDraft
import zone.loong.cube.framework.data.jimmer.service.ACLManagementService
import org.babyfish.jimmer.kt.isLoaded

/**
 * 改进的ACL拦截器
 * 使用统一的ACL管理服务处理权限初始化和变更
 */
@Component
class ImprovedACLDraftInterceptor(
    @Lazy private val aclManagementService: ACLManagementService
) : DraftInterceptor<ACLAware, ACLAwareDraft> {

    @Transactional
    override fun beforeSave(draft: ACLAwareDraft, original: ACLAware?) {
        when {
            // 新建资源：初始化ACL
            original == null && !isLoaded(draft, ACLAwareDraft::acl) -> {
                handleNewResource(draft)
            }
            // 现有资源：检查父级变更
            original != null -> {
                handleResourceUpdate(draft, original)
            }
        }
    }

    /**
     * 处理新资源的ACL初始化
     */
    private fun handleNewResource(draft: ACLAwareDraft) {
        // 使用ACL管理服务初始化权限
        val acl = aclManagementService.initializeACL(draft)
        draft.aclId = acl.id
    }

    /**
     * 处理资源更新，特别是父级变更
     */
    private fun handleResourceUpdate(draft: ACLAwareDraft, original: ACLAware) {
        val oldParentId = original.parentResourceId()
        val newParentId = draft.parentResourceId()
        
        // 检查父级是否发生变更
        if (oldParentId != newParentId) {
            handleParentMigration(draft.id, oldParentId, newParentId)
        }
    }

    /**
     * 处理父级迁移
     */
    private fun handleParentMigration(resourceId: String, oldParentId: String?, newParentId: String?) {
        aclManagementService.handleParentMigration(resourceId, oldParentId, newParentId)
    }
}
