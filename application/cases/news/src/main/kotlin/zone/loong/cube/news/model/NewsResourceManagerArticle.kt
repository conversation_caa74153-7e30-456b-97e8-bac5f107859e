package zone.loong.cube.news.model

import com.fasterxml.jackson.annotation.JsonValue
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.JoinColumn
import org.babyfish.jimmer.sql.Key
import org.babyfish.jimmer.sql.ManyToOne
import zone.loong.cube.framework.data.jimmer.model.IDAware

/**
 * 模块-新闻显示规则
 */
@Entity
interface NewsResourceManagerArticle : IDAware  {

    /**
     * 模块id
     */
    @Key
    @ManyToOne
    @JoinColumn(name = "BLOCK_ID")
    val newsBlock: NewsBlock

    /**
     * 新闻id
     */
    @Key
    @ManyToOne
    @JoinColumn(name = "NEWS_ID")
    val news: NewsResourceManager

    /**
     * 置顶
     */
    val topStatus: TopType

    /**
     * 排序
     */
    val sequence: Int?

    /**
     * 图片显示规则(none:全无，0:无图，1:左文右图，2:大图，3:三图并列)
     */
    val imgStatus: ImageType?
}

enum class ImageType(@JsonValue val value: String) {
    NONE("none"),
    Z<PERSON><PERSON>("0"),
    ONE("1"),
    TWO("2"),
    THREE("3");
}
enum class TopType(@JsonValue val value: String) {
    TRUE("TRUE"),
    FALSE("FALSE")
}

