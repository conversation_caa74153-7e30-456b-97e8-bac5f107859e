package zone.loong.cube.framework.cache.jetcache

import com.alicp.jetcache.support.AbstractJsonDecoder
import com.fasterxml.jackson.databind.ObjectMapper
import java.nio.charset.StandardCharsets

class JacksonValueDecoder(val objectMapper: ObjectMapper) : AbstractJsonDecoder(false) {
    override fun parseObject(
        buffer: ByteArray,
        index: Int,
        len: Int,
        clazz: Class<*>?
    ): Any? {
        val s = String(buffer, index, len, StandardCharsets.UTF_8)
        return objectMapper.readValue(s, clazz)
    }

}