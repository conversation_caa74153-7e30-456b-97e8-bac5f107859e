package zone.loong.cube.framework.data.jimmer.listener

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.PolicyTool.Companion.refPolicy
import zone.loong.cube.framework.data.jimmer.event.ResourceCreated
import zone.loong.cube.framework.data.jimmer.event.ResourcePolicyUpdate
import zone.loong.cube.framework.data.jimmer.model.*
import zone.loong.cube.framework.data.kernel.TransactionSync
import zone.loong.cube.framework.data.kernel.model.StrategyType

@Component
class ResourceListener(
    private val kSqlClient: KSqlClient,
    private val applicationEventPublisher: ApplicationEventPublisher
) {
    /**
     * 初始化授权
     */
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onResourceCreated(event: ResourceCreated) {
        TransactionSync.runBeforeCommit("初始化ACL授权") {
            saveACL(ACL {
                id = event.aclId
                resourceId = event.resourceId
                resourceType = event.resourceType
                resourceName = event.resourceName
                policies = refPolicy(event.aclId, StrategyType.DRILL_DOWN, fetchPolicies(event.parentResourceId))
                event.parentResourceId?.apply {
                    parent {
                        resourceId = event.parentResourceId
                    }
                }
            }).also {
                applicationEventPublisher.publishEvent(
                    ResourcePolicyUpdate(
                        listOf(it.resourceId),
                        it.resourceType,
                        it.resourceName,
                    )
                )
            }


        }
    }

    @Transactional
    fun saveACL(acl: ACL): ACL {
        return kSqlClient.entities.saveCommand(acl, SaveMode.INSERT_ONLY).execute().modifiedEntity

    }

    private fun fetchPolicies(resourceId: String?): List<Policy> {
        return resourceId?.let {
            kSqlClient.createQuery(Policy::class) {
                where(table.acl.resourceId eq resourceId)
                select(table.fetchBy {
                    allTableFields()
                })
            }.execute()
        } ?: emptyList()
    }
}