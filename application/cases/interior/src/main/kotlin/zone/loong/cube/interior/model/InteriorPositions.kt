package zone.loong.cube.interior.model

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.LogicalDeleted
import org.babyfish.jimmer.sql.Serialized
import zone.loong.cube.framework.data.jimmer.model.AuditAware

/**
 * 岗位标准sop管理
 */
@Entity
interface InteriorPositions : AuditAware {

    /**
     * 标题
     */
    val title: String?

    /**
     * 标签
     */
    @Serialized
    val tags: List<String>?

    /**
     * 所属小组
     */
    val affiliatedTeam: String?

    /**
     * 岗位名称
     */
    val postName: String?

    /**
     * 工作内容
     */
    val jobContent: String?

    /**
     * 状态
     */
    val status: String?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}

