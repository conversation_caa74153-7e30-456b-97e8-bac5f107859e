package zone.loong.cube.framework.data.jimmer.interceptor

import com.github.f4b6a3.ulid.UlidCreator
import org.babyfish.jimmer.kt.isLoaded
import org.babyfish.jimmer.sql.DraftInterceptor
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.context.annotation.Lazy
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.PolicyTool
import zone.loong.cube.framework.data.jimmer.model.*
import zone.loong.cube.framework.data.kernel.TransactionSync
import zone.loong.cube.framework.data.kernel.model.StrategyType

open class ACLDraftInterceptor(@Lazy private val kSqlClient: KSqlClient) : DraftInterceptor<ACLAware, ACLAwareDraft> {

    @Transactional
    override fun beforeSave(draft: ACLAwareDraft, original: ACLAware?) {
        if (original == null && !isLoaded(draft,ACLAwareDraft::acl)) {
            val aclId = UlidCreator.getUlid().toString()
            draft.aclId = aclId
            TransactionSync.runBeforeCommit("初始化ACL授权") {
                saveACL(ACL {
                    id = aclId
                    resourceId = draft.id
                    resourceType = draft.resourceType()
                    resourceName = draft.resourceName()
                    policies = PolicyTool.Companion.refPolicy(
                        aclId,
                        StrategyType.DRILL_DOWN,
                        fetchPolicies(draft.parentResourceId())
                    )
                    draft.parentResourceId()?.apply {
                        parent {
                            resourceId = draft.parentResourceId().toString()
                        }
                    }
                })
            }

        }
    }

    fun saveACL(acl: ACL): ACL {
        return kSqlClient.entities.saveCommand(acl, SaveMode.INSERT_ONLY).execute().modifiedEntity
    }


    private fun fetchPolicies(resourceId: String?): List<Policy> {
        return resourceId?.let {
            kSqlClient.createQuery(Policy::class) {
                where(table.acl.resourceId eq resourceId)
                select(table.fetchBy {
                    allTableFields()
                })
            }.execute()
        } ?: emptyList()
    }
}