package zone.loong.cube.platform.dataAuth.controller

import org.babyfish.jimmer.client.meta.Api
import org.springframework.web.bind.annotation.*
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.Group
import zone.loong.cube.platform.auth.model.Role
import zone.loong.cube.platform.auth.model.dto.ACLSave
import zone.loong.cube.platform.auth.model.dto.ACLSpecification
import zone.loong.cube.platform.auth.model.dto.ACLView
import zone.loong.cube.platform.auth.model.dto.AccountWithUserInfo
import zone.loong.cube.platform.dataAuth.service.ACLService
import zone.loong.cube.platform.organization.model.Org

@RestController
@RequestMapping("/dataAuth/acl")
@Api
class ACLController(private val aclService: ACLService) {
    @PostMapping("page")
    @Api
    fun fetchACL(@RequestBody pageQuery: PageQuery<ACLSpecification>): Page<ACLView> {

        return aclService.fetchACL(pageQuery)
    }

    @PostMapping("list")
    @Api
    fun fetchACL(@RequestBody aclSpecification: ACLSpecification): List<ACLView> {
        return aclService.fetchACL(aclSpecification)
    }

    @GetMapping("/account")
    fun fetchAccount(
        @RequestParam("resourceId") resourceId: String,
    ): List<AccountWithUserInfo> {
        return aclService.fetchAccount(resourceId)
    }

    @GetMapping("/organization")
    fun fetchOrganization(
        @RequestParam("resourceId") resourceId: String,
    ): List<Org> {
        return aclService.fetchOrganization( resourceId)
    }

    @GetMapping("/group")
    fun fetchGroups(
        @RequestParam("resourceId") resourceId: String,
    ): List<Group> {
        return aclService.fetchGroups(resourceId)
    }

    @GetMapping("/role")
    fun fetchRoles(
        @RequestParam("resourceId") resourceId: String,
    ): List<Role> {
        return aclService.fetchRoles(resourceId)
    }

    @PostMapping("/authorization")
    fun authorization(@RequestBody acl:ACLSave){
        aclService.authorization(acl)
    }
}