package zone.loong.cube.news.service

import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsBanner
import zone.loong.cube.news.model.NewsNavigation
import zone.loong.cube.news.model.NewsNewspaper
import zone.loong.cube.news.model.NewsResourceManager
import zone.loong.cube.news.model.dto.NewsResourceManagerSpecification

interface NewsWebService {
    fun getTitleBar() : List<NewsNavigation>
    fun getNewsByModule(pageQuery: PageQuery<NewsResourceManagerSpecification>): Page<NewsResourceManager>
    fun addClickIncident(id: String)
    fun getRecommendData(navigationId: String, limitNum: Int): Map<String, Any>
    fun getNewsDetail(id: String): NewsResourceManager
    fun getNewsPaperData(navigationId: String): Map<String, Any>
    fun getNewsPaperDetail(periods: Int?, issueDate: String?): NewsNewspaper
    fun getNewsPaperTitleBar(): List<Any>
    fun getNewsBanner(navigationId: String): List<NewsBanner>
    fun getPragmaticJournalData(navigationId: String): Map<String, Any>
}