//package zone.loong.cube
//
//import cn.dev33.satoken.SaManager
//import cn.dev33.satoken.secure.BCrypt
//import cn.dev33.satoken.spring.SaTokenContextForSpringInJakartaServlet
//import cn.dev33.satoken.stp.StpUtil
//import org.babyfish.jimmer.sql.ast.mutation.SaveMode
//import org.babyfish.jimmer.sql.kt.KSqlClient
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import zone.loong.cube.platform.auth.model.Account
//import zone.loong.cube.platform.auth.model.Role
//import zone.loong.cube.platform.organization.model.Org
//import zone.loong.cube.platform.organization.model.OrgType
//import zone.loong.cube.platform.organization.model.User
//import zone.loong.cube.platform.organization.service.sync.OrganizationClient
//import java.time.LocalDateTime
//import java.util.Collections.synchronizedList
//
//@SpringBootTest()
//class SyncTest(@Autowired private val organizationClient: OrganizationClient, @Autowired private val kSqlClient: KSqlClient) {
//    @Test
//    fun syncCompany() {
//        SaManager.setSaTokenContext(SaTokenContextForSpringInJakartaServlet())
//        StpUtil.login("admin")
//        val ts = "1999-01-01 00:00:00"
//        val syncCompany = organizationClient.fetchCompany(ts)
//        val companyList = syncCompany.data.map {
//            Org {
//                type = OrgType.COMPANY
//                id = it.id
//                code = it.code
//                name = it.name
//                parentId = it.parent
//                enable = warpEnable(it.enable)
//            }
//        }.toList()
//        kSqlClient.entities.saveEntities(companyList) {
//            setMode(SaveMode.UPSERT)
//        }
//    }
//
//
//    @Test
//    fun syncDepartment() {
//        SaManager.setSaTokenContext(SaTokenContextForSpringInJakartaServlet())
//        StpUtil.login("admin")
//        val ts = "1999-01-01 00:00:00"
//        val syncCompany = organizationClient.fetchCompany(ts)
//        val departmentList = synchronizedList(mutableListOf<Org>())
//
//        syncCompany.data.parallelStream().forEach {
//            val syncDepartment = organizationClient.fetchDepartment(it.id, ts).data.map {
//                Org {
//                    type = OrgType.DEPARTMENT
//                    id = it.id
//                    code = it.code
//                    name = it.name
//                    parentId = it.parent?: it.companyId
//                    enable = warpEnable(it.enable)
//                }
//            }.toList()
//            departmentList.addAll(syncDepartment)
//        }
//        kSqlClient.entities.saveEntities(departmentList) {
//            setMode(SaveMode.UPSERT)
//        }
//    }
//
//    @Test
//    fun syncPosition() {
//        SaManager.setSaTokenContext(SaTokenContextForSpringInJakartaServlet())
//        StpUtil.login("admin")
//        val ts = "1999-01-01 00:00:00"
//        val syncCompany = organizationClient.fetchCompany(ts)
//        val positionList = synchronizedList(mutableListOf<Org>())
//        syncCompany.data.parallelStream().forEach {
//            val positions = organizationClient.fetchPosition(it.id, ts).data.map {
//                Org {
//                    type = OrgType.POSITION
//                    id = it.id
//                    code = it.id
//                    name = it.name
//                    parentId = it.deptId
//                    enable = it.enable
//                }
//            }.toList()
//            positionList.addAll(positions)
//        }
//        kSqlClient.entities.saveEntities(positionList) {
//            setMode(SaveMode.UPSERT)
//        }
//    }
//
//
//
//    @Test
//    fun syncUser() {
//        SaManager.setSaTokenContext(SaTokenContextForSpringInJakartaServlet())
//        StpUtil.login("admin")
//        val ts = "1999-01-01 00:00:00"
//        val syncCompany = organizationClient.fetchCompany(ts)
//        val userList = synchronizedList(mutableListOf<User>())
//
//        syncCompany.data.parallelStream().forEach { it ->
//            val users = organizationClient.fetchUser(it.id + "_GX", ts)
//                .data
//                .filter {
//                    warpEnable(it.enable)
//                }
//                .map {
//                    User {
//                        id = it.id
//                        code = it.code
//                        name = it.name
//                        email = it.email
//                        phone = it.phone
//                        sex = it.sex
//                        enable = warpEnable(it.enable)
//                        orgs = it.getPostIds().map {
//                            Org {
//                                id = it
//                            }
//                        }
//                        if (warpNY(it.manager) && warpEnable(it.enable)) {
//                            accounts = listOf(
//                                Account {
//                                    userName = it.code
//                                    password = encryptionPassword("P@ssW0rd")
//                                    effectiveFrom = LocalDateTime.now()
//                                    effectiveUntil = LocalDateTime.now().withYear(9999)
//                                    roles = listOf(Role {
//                                        id = "01J9TJBNKA1WQZVSS2976WVGT0"
//                                    })
//                                }
//                            )
//                        }
//                    }
//                }.toList()
//            userList.addAll(users)
//        }
//        kSqlClient.entities.saveEntities(userList) {
//            setMode(SaveMode.UPSERT)
//        }
//    }
//
//    private fun warpEnable(value: Int): Boolean {
//        return value != 0
//    }
//
//    private fun warpNY(value: String): Boolean {
//        return value == "Y"
//    }
//
//    private fun encryptionPassword(password: String): String {
//        val salt = BCrypt.gensalt(8)
//        return BCrypt.hashpw(password, salt)
//    }
//
//}