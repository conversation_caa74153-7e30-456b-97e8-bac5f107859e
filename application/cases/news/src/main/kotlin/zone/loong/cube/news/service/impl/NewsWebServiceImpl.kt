package zone.loong.cube.news.service.impl

import cn.dev33.satoken.stp.StpUtil
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.*
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.news.model.NewsBanner
import zone.loong.cube.news.model.NewsBlock
import zone.loong.cube.news.model.NewsNavigation
import zone.loong.cube.news.model.NewsNewspaper
import zone.loong.cube.news.model.NewsNewspaperBoard
import zone.loong.cube.news.model.NewsResourceManager
import zone.loong.cube.news.model.NewsResourceManagerArticle
import zone.loong.cube.news.model.NewsStatus
import zone.loong.cube.news.model.NewspaperGroup
import zone.loong.cube.news.model.dto.NewsResourceManagerSpecification
import zone.loong.cube.news.model.fetchBy
import zone.loong.cube.news.model.id
import zone.loong.cube.news.model.issueTime
import zone.loong.cube.news.model.navigationId
import zone.loong.cube.news.model.news
import zone.loong.cube.news.model.newsBlock
import zone.loong.cube.news.model.newsBlockId
import zone.loong.cube.news.model.parentId
import zone.loong.cube.news.model.periods
import zone.loong.cube.news.model.readingNum
import zone.loong.cube.news.model.sequence
import zone.loong.cube.news.model.status
import zone.loong.cube.news.model.title
import zone.loong.cube.news.model.topStatus
import zone.loong.cube.news.service.NewsWebService
import zone.loong.cube.platform.organization.model.SysUserLevel
import zone.loong.cube.platform.organization.model.User
import zone.loong.cube.platform.organization.model.code
import zone.loong.cube.platform.organization.model.fetchBy
import zone.loong.cube.platform.organization.model.levelName
import java.time.LocalDate


@Service
class NewsWebServiceImpl(
    private val kSqlClient: KSqlClient,
) : NewsWebService {
    override fun getTitleBar(): List<NewsNavigation> {
        // 获取当前登录用户信息
        val tokenInfo = StpUtil.getTokenInfo()
        // 获取登录人信息
        val user = kSqlClient.createQuery(User::class){
            where(table.code eq tokenInfo.loginId.toString())
            select(table.fetchBy {
                levelName()
                accounts{
                    roles{
                        code()
                    }
                }
            })
        }.fetchOneOrNull()
        // 判断是否拥有管理员权限
        val isAdmin = user?.accounts
            ?.flatMap { it.roles }
            ?.any { it.code == "ADMIN" || it.code == "NEWSADMIN" } == true

        var navigations = kSqlClient.createQuery(NewsNavigation::class){
            where(table.status eq NewsStatus.ENABLED)
            where(table.parentId eq "0")
            orderBy(table.sequence)
            select(table.fetchBy {
                title()
                code()
                `children*`()
                parent()
                blocks {
                    title()
                    code()
                    image {
                        url()
                        originalFilename()
                    }
                }
            })
        }.execute()
        if (user != null && user.levelName != "") {
            // 获取用户职级信息
            val levelData = kSqlClient.createQuery(SysUserLevel::class) {
                where(table.levelName eq user.levelName)
                select(table.fetchBy {
                    level()
                })
            }.fetchOneOrNull()
            // 判断该用户是否为经理级
            if (levelData != null) {
                levelData.level.let {
                    // 用户非经理级及以上并且非管理员，则隐藏务实刊模块
                    if (levelData.level < 3 && !isAdmin) {
                        navigations = navigations.filter { it.title != "务实刊" }
                    }
                }
            }
        }
        return navigations
    }

    override fun getNewsByModule(pageQuery: PageQuery<NewsResourceManagerSpecification>): Page<NewsResourceManager> {
        val a = kSqlClient.page(pageQuery){  // 按子表字段排序
            pageQuery.specification.newsBlockId?.takeIf { it.isNotEmpty() }?.let {
                orderBy(
                    subQuery(NewsResourceManagerArticle::class) {
                        where(table.news eq parentTable, table.newsBlockId eq pageQuery.specification.newsBlockId)
                        select(table.topStatus)
                    }.desc(),
                    subQuery(NewsResourceManagerArticle::class) {
                        where(table.news eq parentTable, table.newsBlockId eq pageQuery.specification.newsBlockId)
                        select(table.sequence)
                    }.asc()
                )
            }
            orderBy(table.issueTime.desc())
            select(table.fetchBy {
                title()
                author()
                issueDept()
                issueTime()
                readingNum()
                contentType()
                contentBrief()
                attachments {
                    file {
                        filename()
                        originalFilename()
                        url()
                    }
                }
                newspaperBoards {
                    title()
                    newspaper {
                        periods()
                    }
                }
            })
        }
        return a
    }

    override fun getNewsDetail(id: String): NewsResourceManager {
        // web调用详情直接阅读量+1
        addClickIncident(id)
        return kSqlClient.createQuery(NewsResourceManager::class) {
            where(table.id eq id)
            select(table.fetchBy {
                allScalarFields()
                attachments {
                    file {
                        originalFilename()
                        metadata()
                        url()
                    }
                }
                contentFile {
                    originalFilename()
                    metadata()
                    url()
                }
            })
        }.fetchOneOrNull() ?: throw BizException("资源不存在或已被删除")
    }

    override fun addClickIncident(id: String) {
        kSqlClient.createUpdate(NewsResourceManager::class) {
            set(table.readingNum, table.readingNum + 1)
            where(table.id eq id)
        }.execute()
    }

    override fun getRecommendData(navigationId: String, limitNum: Int): Map<String, Any> {
        // 获取栏目下的模块、code、及模块下的新闻数据
        val blocks = kSqlClient.createQuery(NewsBlock::class){
            orderBy(table.sequence)
            where(table.navigationId eq navigationId)
            select(table.fetchBy {
                title()
                code()
                newsArticle ({
                    limit(limitNum)
                }){
                    news {
                        title()
                        author()
                        issueUnit()
                        issueTime()
                        readingNum()
                        attachments {
                            file {
                                filename()
                                originalFilename()
                                url()
                            }
                        }
                    }
                }
            })
        }.execute()
        // 获取最新动态数据（整个新闻列表发布时间排行前五的数据）
        val recentNews = kSqlClient.createQuery(NewsResourceManager::class) {
            // 过滤掉存在芳草地关联的新闻
            where(
                table.id valueNotIn subQuery(NewsNewspaperBoard::class) {
                    where(table.title eq "芳草地")
                    select(table.id)
                }
            )
            orderBy(table.issueTime.desc())
            select(table.fetchBy {
                title()
                author()
                issueUnit()
                issueTime()
                readingNum()
                attachments {
                    file {
                        filename()
                        originalFilename()
                        url()
                    }
                }
            })
        }.limit(9).execute()
        // 获取建华热榜数据（阅读量排行前三的数据列表）
        val topSearch = kSqlClient.createQuery(NewsResourceManager::class) {
            orderBy(table.readingNum.desc())
            select(table.fetchBy {
                title()
                author()
                issueUnit()
                issueTime()
                readingNum()
                attachments {
                    file {
                        filename()
                        originalFilename()
                        url()
                    }
                }
            })
        }.limit(limitNum).execute()
        return mutableMapOf<String, Any>().apply {
            put("blocks", blocks)
            put("recentNews", recentNews)
            put("topSearch", topSearch)
        }
    }

    override fun getNewsPaperData(navigationId: String): Map<String, Any> {
        // 获取轮播列表
        val banners = kSqlClient.createQuery(NewsBanner::class) {
            orderBy(table.sequence)
            where(table.navigationId eq navigationId)
            select(table.fetchBy {
                title()
                articleUrl()
                sequence()
                image {
                    url()
                    originalFilename()
                }
            })
        }.execute()
        // 获取报纸数据
        val newsPaper = kSqlClient.createQuery(NewsNewspaper::class) {
            orderBy(table.periods.desc())
            where(table.newsBlock.navigationId eq navigationId)
            select(table.fetchBy {
                title()
                periods()
                pageNum()
                sponsor()
                publishDate()
                file {
                    url()
                    metadata()
                }
                newspaperBoards{
                    title()
                    unfold()
                    sequence()
                    image {
                        url()
                    }
                    newsResourceManager{
                        title()
                        author()
                        issueUnit()
                        issueTime()
                        readingNum()
                        attachments {
                            file {
                                filename()
                                originalFilename()
                                url()
                            }
                        }
                    }
                }

            })
        }.limit(1).execute()
        // 查看往期
        val historyNewsPaper = kSqlClient.createQuery(NewsNewspaper::class) {
            orderBy(table.periods.desc())
            select(table.fetchBy {
                title()
                periods()
                pageNum()
                sponsor()
                publishDate()
                file {
                    url()
                    metadata()
                }
                newspaperBoards{
                    title()
                    unfold()
                    sequence()
                    image {
                        url()
                    }
                    newsResourceManager{
                        title()
                        author()
                        issueUnit()
                        issueTime()
                        readingNum()
                        attachments {
                            file {
                                filename()
                                originalFilename()
                                url()
                            }
                        }
                    }
                }
            })
        }.offset(1).limit(5).execute()
        return mutableMapOf<String, Any>().apply {
            put("banners", banners)
            put("newsPaper", newsPaper)
            put("historyNewsPapers", historyNewsPaper)
        }
    }

    override fun getNewsPaperDetail(periods: Int?, issueDate: String?): NewsNewspaper {
        // 将查询时间转为日期型
        var date = LocalDate.now()
        if (issueDate != null && issueDate != "") {
            date = LocalDate.parse("$issueDate-28")
        }
        // 获取报纸数据
        return kSqlClient.createQuery(NewsNewspaper::class) {
            orderBy(table.periods.desc())
            issueDate?.takeIf { it.isNotBlank() }?.let {
                where(table.issueTime lt date)
            }
            periods?.let {
                where(table.periods eq it)
            }
            select(table.fetchBy {
                title()
                periods()
                pageNum()
                sponsor()
                publishDate()
                file {
                    url()
                    metadata()
                }
                newspaperBoards{
                    title()
                    unfold()
                    sequence()
                    image {
                        url()
                    }
                    newsResourceManager{
                        title()
                        author()
                        issueUnit()
                        issueTime()
                        readingNum()
                        attachments {
                            file {
                                filename()
                                originalFilename()
                                url()
                            }
                        }
                    }
                }

            })
        }.limit(1).execute().firstOrNull() ?: throw BizException("选择资源不存在，请前往云展网查看")
    }


    override fun getNewsPaperTitleBar(): List<Any>  {
        val newspapers = kSqlClient.createQuery(NewsNewspaper::class) {
            orderBy(table.periods.desc())
            select(table.fetchBy {
                periods()
            })
        }.execute()
        // 2. 按每6期分组
        val chunkedNewspapers = newspapers.chunked(6)
        // 封装
        val newspaperGroups = chunkedNewspapers.map { group ->
            // 获取第一期和第六期的标题
            val firstTitle = group.first().periods
            val lastTitle = group.last().periods
            // 生成区间名称
            val title = "$firstTitle-$lastTitle" + "期"
            // 获取所有期数对应的id集合
            val ids = group.map { it.id }
            // 封装每一组的结果
            NewspaperGroup(title, ids)
        }
        return newspaperGroups
    }

    // 根据栏目id获取banner数据
    override fun getNewsBanner(navigationId: String): List<NewsBanner> {
        return kSqlClient.createQuery(NewsBanner::class) {
            orderBy(table.sequence)
            where(table.navigationId eq navigationId)
            select(table.fetchBy {
                title()
                articleUrl()
                sequence()
                image {
                    url()
                    originalFilename()
                }
            })
        }.execute()
    }

    // 务实刊首页接口
    override fun getPragmaticJournalData(navigationId: String): Map<String, Any> {
        // 获取栏目下的模块及新闻数据，并进行置顶排序
        val blocks = kSqlClient.createQuery(NewsBlock::class) {
            orderBy(table.sequence)
            where(table.navigationId eq navigationId)
            select(table.fetchBy {
                title()
                newsArticle ({
                    limit(10)
                }){
                    topStatus()
                    sequence()
                    news {
                        title()
                        author()
                        issueUnit()
                        issueTime()
                        readingNum()
                        attachments {
                            file {
                                filename()
                                originalFilename()
                                url()
                            }
                        }
                    }
                }
            })
        }.execute()

        // 获取子栏目数据及模块新闻数据
        val navigations = kSqlClient.createQuery(NewsNavigation::class) {
            where(table.parentId eq navigationId)
            select(table.fetchBy {
                title()
                image {
                    url()
                }
                blocks {
                    title()
                    image {
                        url()
                    }
                    newsArticle ({
                        limit(5)
                    }){
                        topStatus()
                        sequence()
                        news {
                            title()
                            author()
                            issueUnit()
                            issueTime()
                            readingNum()
                            attachments {
                                file {
                                    filename()
                                    url()
                                    originalFilename()
                                }
                            }
                        }
                    }
                }
            })
        }.execute()
        return mutableMapOf<String, Any>().apply {
            put("blocks", blocks)
            put("navigations", navigations)
        }
    }
}