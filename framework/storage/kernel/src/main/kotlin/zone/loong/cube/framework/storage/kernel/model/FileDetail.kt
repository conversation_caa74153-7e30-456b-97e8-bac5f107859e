package zone.loong.cube.framework.storage.kernel.model

import cn.hutool.core.lang.Dict
import org.babyfish.jimmer.sql.*
import org.dromara.x.file.storage.core.hash.HashInfo
import zone.loong.cube.framework.data.jimmer.model.AuditAware

/**
 * 文件上传记录
 */
@Entity
@Table(name = "file_detail")
interface FileDetail : AuditAware {


    /**
     * 文件访问地址
     */
    @Key
    val url: String

    /**
     * 文件大小，单位字节
     */
    val size: Long?

    /**
     * 文件名称
     */
    val filename: String?

    /**
     * 原始文件名
     */
    val originalFilename: String?

    /**
     * 基础存储路径
     */
    val basePath: String?

    /**
     * 存储路径
     */
    val path: String?

    /**
     * 文件扩展名
     */
    val ext: String?

    /**
     * MIME类型
     */
    val contentType: String?

    /**
     * 存储平台
     */
    val platform: String?

    /**
     * 缩略图访问路径
     */
    val thUrl: String?

    /**
     * 缩略图名称
     */
    val thFilename: String?

    /**
     * 缩略图大小，单位字节
     */
    val thSize: Long?

    /**
     * 缩略图MIME类型
     */
    val thContentType: String?

    /**
     * 文件所属对象id
     */
    val objectId: String?

    /**
     * 文件所属对象类型，例如用户头像，评价图片
     */
    val objectType: String?

    /**
     * 文件元数据
     */
    @Serialized
    val metadata: Map<String, String>?

    /**
     * 文件用户元数据
     */
    @Serialized
    val userMetadata: Map<String, String>?

    /**
     * 缩略图元数据
     */
    @Serialized
    val thMetadata: Map<String, String>?

    /**
     * 缩略图用户元数据
     */
    @Serialized
    val thUserMetadata: Map<String, String>?

    /**
     * 附加属性
     */
    @Serialized
    val attr: Dict?

    /**
     * 文件ACL
     */
    val fileAcl: String?

    /**
     * 缩略图文件ACL
     */
    val thFileAcl: String?

    /**
     * 哈希信息
     */
    @Serialized
    val hashInfo: HashInfo?

    /**
     * 上传ID，仅在手动分片上传时使用
     */
    val uploadId: String?

    /**
     * 上传状态，仅在手动分片上传时使用，1初始化完成，2上传完成
     */
    val uploadStatus: Int?

    @Transient
    val status: String?
}

