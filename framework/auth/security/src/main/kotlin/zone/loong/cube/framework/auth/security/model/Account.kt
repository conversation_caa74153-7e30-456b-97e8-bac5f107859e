package zone.loong.cube.framework.auth.security.model

import java.time.LocalDateTime

interface Account {
    val userName: String

    val password: String

    //开始生效日期
    val effectiveFrom: LocalDateTime

    //结束日期
    val effectiveUntil: LocalDateTime

    //封禁记录
    val banRecords: List<BanRecord>

    // 访问记录
    val accessRecords: List<AccessRecord>

    //角色
    val roles: List<Role>

    val groups: List<Group>

    //  账号是否有效
    val effective: Boolean
        get() {
            val now = LocalDateTime.now()
            // 账号是否有效
            val effect = effectiveFrom.isBefore(now) && effectiveUntil.isAfter(now) && banRecords.isEmpty()
            return effect
        }
}