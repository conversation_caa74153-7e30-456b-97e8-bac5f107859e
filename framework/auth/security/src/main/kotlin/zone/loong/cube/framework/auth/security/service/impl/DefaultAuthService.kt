package zone.loong.cube.framework.auth.security.service.impl

import cn.dev33.satoken.stp.StpUtil
import zone.loong.cube.framework.auth.security.constants.SecurityConstants
import zone.loong.cube.framework.auth.security.exception.AuthException
import zone.loong.cube.framework.auth.security.model.Token
import zone.loong.cube.framework.auth.security.service.AuthService
import zone.loong.cube.framework.auth.security.service.LoginExtPoint
import zone.loong.cube.framework.kernel.extension.BizScenario
import zone.loong.cube.framework.kernel.extension.ExtensionExecutor

class DefaultAuthService(
    private val extensionExecutor: ExtensionExecutor,
) : AuthService {

    override fun login(
        accountIdentity: String,
        password: String,
        scenario: String
    ): Token {
        extensionExecutor.execute<LoginExtPoint>(
            BizScenario.valueOf(
                bizId = SecurityConstants.DEFAULT_LOGIN_EXT_POINT_BIZ_ID,
                useCase = SecurityConstants.DEFAULT_LOGIN_EXT_POINT_USE_CASE,
                scenario = scenario
            )
        ) { point ->
            val identity = try {
                point.valid(accountIdentity, password)
            } catch (e: AuthException) {
                throw e
            }
            StpUtil.login(identity)
        }

        return token()
    }

    override fun token(): Token {
        val tokenInfo = StpUtil.getTokenInfo()
        return Token().apply {
            username = tokenInfo.loginId as String
            token = tokenInfo.tokenValue
            expireTime = tokenInfo.tokenTimeout
            roles = StpUtil.getRoleList()
            permissions = StpUtil.getPermissionList()
        }
    }

    override fun accountIdentity(): String {
        return StpUtil.getLoginIdAsString()
    }

    override fun logout() {
        StpUtil.logout()
    }

    override fun banAccount(accountIdentity: String, level: Int, second: Long) {
        //踢下线
        StpUtil.kickout(accountIdentity)
        //封禁
        StpUtil.disableLevel(accountIdentity, level, second)

    }

    override fun unBanAccount(accountIdentity: String) {
        StpUtil.untieDisable(accountIdentity)
    }
}