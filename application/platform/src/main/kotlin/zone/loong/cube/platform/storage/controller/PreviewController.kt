package zone.loong.cube.platform.storage.controller

import cn.dev33.satoken.annotation.SaIgnore
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.framework.storage.preview.service.PreviewService

@RestController
@RequestMapping("/file")
class PreviewController(private val previewService: PreviewService) {

    @GetMapping("/preview")
    @SaIgnore
    fun preview(@RequestParam("fileId") fileId: String) : String{
        return  previewService.preview(fileId)
    }

    @GetMapping("/addTask")
    @SaIgnore
    fun addTask(@RequestParam("fileId") fileId: String){
        previewService.addTask(fileId)
    }
}