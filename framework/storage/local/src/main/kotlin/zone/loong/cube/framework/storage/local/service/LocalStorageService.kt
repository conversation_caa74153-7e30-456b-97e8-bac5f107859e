package zone.loong.cube.framework.storage.local.service

import org.dromara.x.file.storage.core.FileStorageService
import zone.loong.cube.framework.storage.kernel.service.FileRecorder
import zone.loong.cube.framework.storage.kernel.service.StorageService

class LocalStorageService(
    override val platform: String ,
    override val fileStorageService: FileStorageService,
    override val fileRecorder: FileRecorder
) : StorageService