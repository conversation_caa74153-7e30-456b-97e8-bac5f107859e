package zone.loong.cube.framework.data.jimmer.interceptor

import com.github.f4b6a3.ulid.UlidCreator
import org.babyfish.jimmer.sql.DraftInterceptor
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.model.ACLAware
import zone.loong.cube.framework.data.jimmer.model.ACLAwareDraft
import zone.loong.cube.framework.data.jimmer.event.ResourceCreated
import zone.loong.cube.framework.data.jimmer.event.ResourceParentChanged
import zone.loong.cube.framework.data.kernel.TransactionSync
import org.babyfish.jimmer.kt.isLoaded

/**
 * 符合Jimmer架构的ACL拦截器
 *
 * 职责：
 * 1. 在拦截器中只设置aclId，不进行ACL保存操作
 * 2. 通过事件机制在事务提交前处理ACL初始化
 * 3. 检测父级变更并发布事件
 */
@Component
class ImprovedACLDraftInterceptor(
    @Lazy private val applicationEventPublisher: ApplicationEventPublisher
) : DraftInterceptor<ACLAware, ACLAwareDraft> {

    @Transactional
    override fun beforeSave(draft: ACLAwareDraft, original: ACLAware?) {
        when {
            // 新建资源：生成aclId并发布创建事件
            original == null && !isLoaded(draft, ACLAwareDraft::acl) -> {
                handleNewResource(draft)
            }
            // 现有资源：检查父级变更
            original != null -> {
                handleResourceUpdate(draft, original)
            }
        }
    }

    /**
     * 处理新资源
     * 只设置aclId，通过事件机制处理ACL初始化
     */
    private fun handleNewResource(draft: ACLAwareDraft) {
        val aclId = UlidCreator.getUlid().toString()
        draft.aclId = aclId

        // 在事务提交前发布资源创建事件
        TransactionSync.runBeforeCommit("发布资源创建事件") {
            applicationEventPublisher.publishEvent(
                ResourceCreated(
                    aclId = aclId,
                    resourceId = draft.id,
                    resourceType = draft.resourceType(),
                    resourceName = draft.resourceName(),
                    parentResourceId = draft.parentResourceId()
                )
            )
        }
    }

    /**
     * 处理资源更新
     * 检测父级变更并发布事件
     */
    private fun handleResourceUpdate(draft: ACLAwareDraft, original: ACLAware) {
        val oldParentId = original.parentResourceId()
        val newParentId = draft.parentResourceId()

        // 检查父级是否发生变更
        if (oldParentId != newParentId) {
            // 在事务提交前发布父级变更事件
            TransactionSync.runBeforeCommit("发布父级变更事件") {
                applicationEventPublisher.publishEvent(
                    ResourceParentChanged(
                        resourceId = draft.id,
                        oldParentId = oldParentId,
                        newParentId = newParentId,
                        resourceType = draft.resourceType(),
                        resourceName = draft.resourceName()
                    )
                )
            }
        }
    }
}
