package zone.loong.cube.cases.product.service


import com.alicp.jetcache.anno.CacheInvalidate
import zone.loong.cube.cases.product.model.ProductClassify
import zone.loong.cube.cases.product.model.dto.ClassifySpecification
import zone.loong.cube.cases.product.model.dto.SaveClassify
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.model.Tree

interface ProductClassifyService {
    fun fetchClassify(pid: String?): List<ProductClassify>
    fun fetchClassify(pageQuery: PageQuery<ClassifySpecification>): Page<zone.loong.cube.cases.product.model.ProductClassify>
    fun filterClassify(keyword: String): List<Tree>
    fun detail(id: String): zone.loong.cube.cases.product.model.ProductClassify
    fun delete(id: String)
    fun save(saveClassify: SaveClassify): zone.loong.cube.cases.product.model.ProductClassify

    @CacheInvalidate(name = "product-classify-cache", key = "#pid==null?'root':pid")
    fun clearProductClassifyCache(pid: String? = null)
}