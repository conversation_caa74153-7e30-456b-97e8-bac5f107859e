package zone.loong.cube.framework.auth.security.service.impl

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import zone.loong.cube.framework.auth.security.CubeSecurityProperties
import zone.loong.cube.framework.auth.security.constants.SecurityConstants
import zone.loong.cube.framework.auth.security.exception.AuthException
import zone.loong.cube.framework.auth.security.service.LoginExtPoint
import zone.loong.cube.framework.kernel.extension.Extension
import zone.loong.cube.framework.kernel.log.annotation.Log

@Log
@Extension(
    bizId = SecurityConstants.DEFAULT_LOGIN_EXT_POINT_BIZ_ID,
    useCase = SecurityConstants.DEFAULT_LOGIN_EXT_POINT_USE_CASE,
    scenario = "default"
)
@ConditionalOnMissingBean(LoginExtPoint::class)
class DefaultLoginExtPoint(private val cubeSecurityProperties: CubeSecurityProperties) :
    LoginExtPoint {
    override fun valid(accountIdentity: String, password: String): String {
        if (accountIdentity.isNotEmpty() && accountIdentity == cubeSecurityProperties.account) {
            if (password.isNotEmpty() && password == cubeSecurityProperties.password) {
                throw AuthException("密码错误")
            }
        }
        throw AuthException("用户不存在")
    }
}