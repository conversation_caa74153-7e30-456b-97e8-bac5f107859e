package zone.loong.cube.cases.product.service.impl

import cn.dev33.satoken.stp.StpUtil
import com.github.f4b6a3.ulid.UlidCreator
import jakarta.servlet.http.HttpServletResponse
import org.babyfish.jimmer.sql.ast.mutation.AssociatedSaveMode
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.expression.EvaluationException
import org.springframework.expression.Expression
import org.springframework.expression.ExpressionParser
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.cases.product.constants.ProductConstants
import zone.loong.cube.cases.product.model.*
import zone.loong.cube.cases.product.model.dto.CaseViewWithName
import zone.loong.cube.cases.product.model.dto.SaveCase
import zone.loong.cube.cases.product.model.elasticsearch.ESProductCase
import zone.loong.cube.cases.product.service.ESProductCasesService
import zone.loong.cube.cases.product.service.ProductCasesService
import zone.loong.cube.framework.auth.security.service.AccountService
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.elasticsearch.service.SearchService
import zone.loong.cube.framework.data.elasticsearch.service.page
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.data.jimmer.model.ACL
import zone.loong.cube.framework.data.jimmer.model.Policy
import zone.loong.cube.framework.data.jimmer.model.resourceId
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.data.kernel.model.PolicyType
import zone.loong.cube.framework.data.kernel.model.StrategyType
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.platform.auth.model.code
import zone.loong.cube.platform.auth.model.dto.ACLSave
import zone.loong.cube.platform.auth.model.userName
import zone.loong.cube.platform.dataAuth.service.ACLService
import zone.loong.cube.platform.storage.service.StorageService

@Service
class ProductCasesServiceImpl(
    private val kSqlClient: KSqlClient,
    private val searchService: SearchService,
    private val esProductCasesService: ESProductCasesService,
    private val accountService: AccountService,
    private val storageService: StorageService,
    private val aclService: ACLService
) : ProductCasesService {

    override fun fetchCases(pageQuery: PageQuery<ESSpecification>): ESPage<ESProductCase> {
        return searchService.page<ESProductCase>(pageQuery)
    }

    override fun detail(id: String): CaseViewWithName {
        return kSqlClient.findById(CaseViewWithName::class, id) ?: throw BizException("该目录不存在或者已经被删除")
    }


    @Transactional
    override fun save(saveCase: SaveCase): ProductCase {
        val classifyId = saveCase.classifyId
        classifyId ?: throw BizException("分类ID不能为空")
        val case = saveCase.id?.let {
            kSqlClient.entities.saveCommand(saveCase, SaveMode.UPSERT).execute().modifiedEntity
        } ?: let {
            val caseId = UlidCreator.getUlid().toString()
            kSqlClient.entities.saveCommand(saveCase.toEntity {
                id = caseId
                acl = ACL {
                    id = UlidCreator.getUlid().toString()
                    resourceName = ProductConstants.CASE_RESOURCE_NAME
                    resourceType = ProductConstants.CASE_RESOURCE
                    parentId = classifyId
                    resourceId = caseId
                }
            }, SaveMode.INSERT_ONLY).execute().modifiedEntity
        }

        //设置自动授权
        autoAuthorization(case.id)

        esProductCasesService.sync(case.id, true)
        return case


    }

    /**
     * 自动授权
     * 匹配到对应规则的进行授权，同时会删除对应规则产生的旧策略
     */

    private fun autoAuthorization(caseId: String) {

        val authorizationRules = kSqlClient.createQuery(AuthorizationRule::class) {
            select(table.fetchBy {
                expression()
                groups {
                    code()
                }
            })
        }.execute()



        if (authorizationRules.isNotEmpty()) {
            val case = kSqlClient.entities.findById(newFetcher(ProductCase::class).by {
                tags()
                acl {
                    policies {
                        acl()
                        policyType()
                        strategyType()
                        value()
                        ruleId()
                    }
                }
            }, caseId) ?: throw BizException("自动授权失败，资源被删除或不存在")


            val matchRules = authorizationRules.filter {  evaluateCondition(it.expression, case) }
            val matchRuleIds = matchRules.map { rule -> rule.id }
            val currentPolicy =
                case.acl?.policies
                    ?.filter { policy ->
                        !matchRuleIds.contains(policy.ruleId)
                    }
                    ?: emptyList()
            val rulePolicies: List<Policy> = matchRules.flatMap { rule ->
                rule.groups.map { group ->
                    Policy {
                        aclId = case.acl?.id
                        policyType = PolicyType.GROUP
                        strategyType = StrategyType.ROOT
                        value = group.code
                        ruleId = rule.id
                    }
                }
            }
            if (rulePolicies.isNotEmpty()) {
                aclService.authorization(
                    ACLSave(
                        ACL {
                            resourceId = case.id
                            policies = rulePolicies + currentPolicy
                        }
                    ))
            }
        }

    }


    @Transactional
    override fun delete(id: String) {
        kSqlClient.deleteById(ProductCase::class, id)
        kSqlClient.createDelete(ACL::class) {
            where(table.resourceId eq id)
        }.execute()
        esProductCasesService.delete(id)
    }

    override fun download(attachmentId: String, response: HttpServletResponse) {

        val identity = StpUtil.getLoginIdAsString()
        val groups = accountService.fetchAccountGroups(identity)
        val limitRecords: MutableList<ProductAttachment> = mutableListOf()

        //先查询tag
        val attachment = kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .createQuery(ProductAttachment::class) {
                where(table.id eq attachmentId)
                select(table.fetchBy {
                    product {
                        tags()
                    }
                    file()
                })
            }.fetchOne()

        val tags = attachment.product?.tags ?: emptyList()

        //查询下载记录
        val tagRecord = kSqlClient.createQuery(LimitRecord::class) {
            where(
                table.tag valueIn tags,
                table.account.userName eq identity
            )
            select(table)
        }.execute().associateBy { it.tag }

        //        查询下载限制配置
        val tagLimitMap = kSqlClient.createQuery(DownloadLimit::class) {
            where(table.tag valueIn tags, table.groups {
                code valueIn groups
            })
            select(table)
        }.execute().associateBy { it.tag }



        if (tagLimitMap.isNotEmpty()) {
            tagLimitMap.forEach {
                val key = it.key
                val record = tagRecord[key]
                val limit = it.value.limits
                record?.let {
                    if (record.counts >= limit && limit != 0) {
                        throw BizException(
                            "标签[$key]超过下载次数$limit,如需重置下载次数，请在OA发起《\n" +
                                    "研发成果资料库超量申请》"
                        )
                    } else {
                        limitRecords.add(ProductAttachment {
                            id = attachmentId
                            limitRecords().addBy {
                                id = record.id
                                counts = record.counts + 1
                            }
                        })
                    }
                } ?: run {
                    limitRecords.add(ProductAttachment {
                        id = attachmentId
                        limitRecords().addBy {
                            tag = key
                            counts = 1
                            account {
                                userName = identity
                            }
                        }
                    })
                }
            }
        } else {
            storageService.download(attachment.file.id, response)
        }

        storageService.download(attachment.file.id, response)

        //有过下载记录
        kSqlClient.entities.saveEntitiesCommand(limitRecords) {
            setAssociatedModeAll(AssociatedSaveMode.MERGE)
        }.execute()


    }

    override fun sync() {
        val casesIds = kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .entities.findAll(ProductCase::class).map { it.id }
        esProductCasesService.syncWithFile(casesIds)
    }


    private fun evaluateCondition(spelExpression: String, rootObject: Any): Boolean {
        val parser: ExpressionParser = SpelExpressionParser()
        val expression: Expression = parser.parseExpression(spelExpression)
        val context = StandardEvaluationContext(rootObject)
        try {
            return expression.getValue(context, Boolean::class.java) ?: false
        } catch (_: EvaluationException) {

        }
        return false
    }

}