package zone.loong.cube.framework.auth.security.service

import zone.loong.cube.framework.auth.security.exception.AuthException
import zone.loong.cube.framework.auth.security.model.Token

interface AuthService {
    @Throws(AuthException::class)
    fun login(
        accountIdentity: String,
        password: String,
        scenario: String
    ): Token

    fun token(): Token
    fun accountIdentity(): String
    fun banAccount(accountIdentity: String, level: Int, second: Long)
    fun unBanAccount(accountIdentity: String)
    fun logout()
}