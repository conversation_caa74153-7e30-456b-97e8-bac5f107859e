plugins {
    id("pub.ihub.plugin")
    id("pub.ihub.plugin.ihub-git-hooks") apply false
    id("pub.ihub.plugin.ihub-kotlin")
    id("pub.ihub.plugin.ihub-test") apply (false)
    id("pub.ihub.plugin.ihub-verification") apply (false)
    id("pub.ihub.plugin.ihub-publish") apply (false)
    id("pub.ihub.plugin.ihub-boot") apply (false)
    id("org.jetbrains.kotlin.plugin.spring") version ("2.0.0") apply false
}


repositories {
    mavenCentral()
}

subprojects {
    apply {
        plugin("pub.ihub.plugin")
        plugin("pub.ihub.plugin.ihub-kotlin")
        plugin("pub.ihub.plugin.ihub-test")
        plugin("pub.ihub.plugin.ihub-verification")
        plugin("pub.ihub.plugin.ihub-publish")
        plugin("org.jetbrains.kotlin.plugin.spring")
    }
    iHubBom {
        importBoms {
            group("org.springframework.boot").module("spring-boot-dependencies").version("3.4.4")
            dependencyVersions {
                group("com.esotericsoftware").modules("kryo").version("5.6.1")
            }

        }
    }
}