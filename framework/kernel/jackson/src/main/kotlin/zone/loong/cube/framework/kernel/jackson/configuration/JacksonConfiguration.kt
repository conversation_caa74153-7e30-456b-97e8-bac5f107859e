package zone.loong.cube.framework.kernel.jackson.configuration

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer
import com.fasterxml.jackson.datatype.jsr310.deser.LocalTimeDeserializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer
import com.fasterxml.jackson.datatype.jsr310.ser.LocalTimeSerializer
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.format.DateTimeFormatter

@Configuration
class JacksonConfiguration {
    companion object {
        private const val STANDARD_PATTERN = "yyyy-MM-dd HH:mm:ss"

        private const val DATE_PATTERN = "yyyy-MM-dd"

        private const val TIME_PATTERN = "HH:mm:ss"
    }

    /***
     * 统一处理时间类修改
     */

    @Bean
    fun javaDateTimeModule(): JavaTimeModule {
        return JavaTimeModule().apply {
            //处理DateTi
            //处理LocalDateTime
            val dateTimeFormatter = DateTimeFormatter.ofPattern(STANDARD_PATTERN)
            addSerializer(LocalDateTime::class.java, LocalDateTimeSerializer(dateTimeFormatter))
            addDeserializer(LocalDateTime::class.java, LocalDateTimeDeserializer(dateTimeFormatter))
            //处理LocalDate
            val dateFormatter = DateTimeFormatter.ofPattern(DATE_PATTERN)
            addSerializer(LocalDate::class.java, LocalDateSerializer(dateFormatter))
            addDeserializer(LocalDate::class.java, LocalDateDeserializer(dateFormatter))
            //处理localTime
            val timeFormatter = DateTimeFormatter.ofPattern(TIME_PATTERN)
            addSerializer(LocalTime::class.java, LocalTimeSerializer(timeFormatter))
            addDeserializer(LocalTime::class.java, LocalTimeDeserializer(timeFormatter))
        }
    }

    @Bean
    fun jackson2ObjectMapperBuilder(): Jackson2ObjectMapperBuilderCustomizer {
        return Jackson2ObjectMapperBuilderCustomizer { jacksonObjectMapperBuilder ->
            jacksonObjectMapperBuilder.failOnEmptyBeans(false).failOnUnknownProperties(false)
                .featuresToEnable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN)
                .featuresToDisable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
                .featuresToEnable(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL)
        }

    }
}