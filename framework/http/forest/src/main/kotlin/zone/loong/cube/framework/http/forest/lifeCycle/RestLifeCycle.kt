package zone.loong.cube.framework.http.forest.lifeCycle

import com.dtflys.forest.http.ForestRequest
import com.dtflys.forest.lifecycles.MethodAnnotationLifeCycle
import com.dtflys.forest.reflection.ForestMethod
import org.springframework.stereotype.Service
import zone.loong.cube.framework.http.forest.RestProperties
import zone.loong.cube.framework.http.forest.annotation.Rest

@Service
class RestLifeCycle(private val restProperties: RestProperties) : MethodAnnotationLifeCycle<Rest, Any> {
    override fun onMethodInitialized(method: ForestMethod<*>, annotation: Rest) {
        restProperties.getHost(annotation.code)
    }

    override fun beforeExecute(request: ForestRequest<*>): Bo<PERSON>an {
        val code: String = getAttribute(request, "code") as String
        val host = restProperties.getHost(code)
        request.url = host.url
        request.port = host.port
        return super.beforeExecute(request)
    }
}