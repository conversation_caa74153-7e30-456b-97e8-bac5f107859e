package zone.loong.cube.framework.tika.service

import org.springframework.web.multipart.MultipartFile
import zone.loong.cube.framework.tika.TikaSensitive
import zone.loong.cube.framework.tika.model.SensitiveResult
import java.io.File
import java.io.InputStream
import java.nio.file.Path

interface TikaService {
    fun getText(path: Path): String
    fun getText(inputStream: InputStream): String
    fun getText(file: File): String
    fun getText(file: MultipartFile): String

    fun checkSensitiveInfo(inputStream: InputStream): SensitiveResult
    fun checkSensitiveInfo(file: File): SensitiveResult
    fun checkSensitiveInfo(file: MultipartFile): SensitiveResult

    fun checkSensitiveInfo(inputStream: InputStream,sensitives: List<TikaSensitive>): SensitiveResult
    fun checkSensitiveInfo(file: File,sensitives: List<TikaSensitive>): SensitiveResult
    fun checkSensitiveInfo(file: MultipartFile,sensitives: List<TikaSensitive>): SensitiveResult

    fun getMime(path: Path): String
    fun getMimeType(inputStream: InputStream): String
    fun getMimeType(file: File): String
    fun getMimeType(mimeType: String): String

}