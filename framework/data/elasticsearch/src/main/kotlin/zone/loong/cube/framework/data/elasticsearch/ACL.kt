package zone.loong.cube.framework.data.elasticsearch

import org.springframework.data.elasticsearch.annotations.Field
import org.springframework.data.elasticsearch.annotations.FieldType
import org.springframework.data.elasticsearch.annotations.Mapping
import zone.loong.cube.framework.data.kernel.model.ACL

open class ACL : ACL, Audit() {

    @Field(type = FieldType.Keyword)
    override var resourceId: String = ""

    @Field(type = FieldType.Keyword)
    override val resourceType: String= ""

    @Field(type = FieldType.Keyword)
    override var resourceName: String = ""

    @Field(type = FieldType.Nested)
    @Mapping
    override var policies: List<Policy> = emptyList()
}