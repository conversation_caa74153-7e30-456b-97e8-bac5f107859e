server:
  port: 8848
spring:
  datasource:
    url: ************************************************************************
    username: case-manage
    password: W8SQACtnQCPNt7Sj
    driver-class-name: org.postgresql.Driver
  data:
    redis:
      password: redis_KYpdNz
      host: dev.loong.zone
      repositories:
        enabled: false
  elasticsearch:
    password: 2$8c%SH%Sq2ViA55
    uris: http://dev.loong.zone:9200
    username: elastic
cube:
  rest:
    hosts:
      - code: organization-sync
        url: http://************
        port: 8155
      - code: login-by-code
        url: http://*************
        port: 9005
      - code: file-view
        url: http://*************
        port: 5678
      - code: news-official-accounts-oa
        url: http://*************
        port: 8085
  cache:
    jetcache:
      broadcast-channel: cube-cache-jetcache
      limit: 200
      stat-interval-minutes: 15
  storage:
    local:
      storage-path: /Users/<USER>/workspace/platform/cube/data/file/
#      storage-path: /Users/<USER>/Documents/data/
      domain: http://**************:81/api/storage/file/
    preview:
      kk-file-view-url: http://*************:5678/