package zone.loong.cube.news.controller

import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.news.model.NewsBanner
import zone.loong.cube.news.model.dto.NewsBannerSpecification
import zone.loong.cube.news.model.dto.SaveNewsBanner
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.service.NewsBannerService

@RestController
@RequestMapping("/news/banner")
class NewsBannerController(
    private val newsBannerService: NewsBannerService
) {

    @PostMapping("/page")
    fun fetch(@RequestBody pageQuery: PageQuery<NewsBannerSpecification>): Page<NewsBanner> {
        return newsBannerService.fetchBanners(pageQuery)
    }

    @PostMapping("/save")
    fun save(@RequestBody saveNewsBanner: SaveNewsBanner): NewsBanner {
        return newsBannerService.save(saveNewsBanner)
    }

    @DeleteMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        newsBannerService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): NewsBanner {
        return newsBannerService.detail(id)
    }
}