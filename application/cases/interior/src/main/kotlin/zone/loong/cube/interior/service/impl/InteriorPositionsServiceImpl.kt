package zone.loong.cube.interior.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.interior.model.InteriorPositions
import zone.loong.cube.interior.model.dto.InteriorPositionsSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorPositions
import zone.loong.cube.interior.service.InteriorPositionsService

@Service
class InteriorPositionsServiceImpl(
    private val kSqlClient: KSqlClient
) : InteriorPositionsService {

    override fun fetchPositions(pageQuery: PageQuery<InteriorPositionsSpecification>): Page<InteriorPositions> {
        return kSqlClient.page(pageQuery){
            select(table)
        }
    }

    override fun save(saveInteriorPositions: SaveInteriorPositions): InteriorPositions {
      return  saveInteriorPositions.id?.let {
          kSqlClient.save(saveInteriorPositions, SaveMode.UPSERT).modifiedEntity
        }?:kSqlClient.save(saveInteriorPositions, SaveMode.INSERT_ONLY).modifiedEntity
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(InteriorPositions::class, id)
    }

    override fun detail(id: String): InteriorPositions {
        return kSqlClient.findById(InteriorPositions::class, id) ?: throw Exception("找不到该岗位")
    }

}