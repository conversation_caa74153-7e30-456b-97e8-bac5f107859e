# 知识库权限传递完整演示

## 演示场景设置

### 初始数据
```sql
-- 创建知识库结构
INSERT INTO access_acl (id, resource_id, resource_type, resource_name, parent_resource_id) VALUES
('acl_cat_tech', 'category_tech_docs', 'KNOWLEDGE_CATEGORY', '技术文档', NULL),
('acl_cat_business', 'category_business', 'KNOWLEDGE_CATEGORY', '业务文档', NULL),
('acl_case_spring', 'case_spring_boot', 'KNOWLEDGE_CASE', 'Spring Boot教程', 'category_tech_docs'),
('acl_case_db', 'case_database_design', 'KNOWLEDGE_CASE', '数据库设计', 'category_tech_docs'),
('acl_case_process', 'case_business_process', 'KNOWLEDGE_CASE', '业务流程', 'category_business');
```

## 演示1：分类权限向下传递

### 步骤1：给技术文档分类授权
```kotlin
// 给用户alice授予技术文档分类的READ权限
knowledgeBasePermissionService.grantCategoryPermission(
    categoryId = "category_tech_docs",
    userId = "alice", 
    permission = "READ"
)
```

### 执行后的数据库状态
```sql
-- 查看生成的权限记录
SELECT 
    a.resource_id,
    a.resource_name,
    p.strategy_type,
    p.rule_id,
    p.ref_policy_id
FROM access_policies p
JOIN access_acl a ON p.acl_id = a.id  
WHERE p.value = 'alice'
ORDER BY p.strategy_type, a.resource_id;
```

### 预期结果
```
resource_id         | resource_name    | strategy_type | rule_id | ref_policy_id
--------------------|------------------|---------------|---------|---------------
category_tech_docs  | 技术文档         | ROOT          | READ    | NULL
case_spring_boot    | Spring Boot教程  | DRILL_DOWN    | READ    | policy_xxx
case_database_design| 数据库设计       | DRILL_DOWN    | READ    | policy_xxx
```

### 权限检查验证
```kotlin
// Alice现在可以访问技术文档分类和所有技术案例
println(hasPermission("alice", "category_tech_docs", "READ"))      // true
println(hasPermission("alice", "case_spring_boot", "READ"))        // true  
println(hasPermission("alice", "case_database_design", "READ"))    // true

// 但不能访问业务相关内容
println(hasPermission("alice", "category_business", "READ"))       // false
println(hasPermission("alice", "case_business_process", "READ"))   // false
```

## 演示2：案例权限向上传递

### 步骤1：只给特定案例授权
```kotlin
// 给用户bob只授予Spring Boot案例的READ权限
knowledgeBasePermissionService.grantCasePermission(
    caseId = "case_spring_boot",
    userId = "bob",
    permission = "READ"
)
```

### 执行后的数据库状态
```sql
-- 查看bob的权限
SELECT 
    a.resource_id,
    a.resource_name, 
    p.strategy_type,
    p.rule_id,
    p.ref_policy_id
FROM access_policies p
JOIN access_acl a ON p.acl_id = a.id
WHERE p.value = 'bob'
ORDER BY p.strategy_type, a.resource_id;
```

### 预期结果
```
resource_id         | resource_name    | strategy_type | rule_id | ref_policy_id
--------------------|------------------|---------------|---------|---------------
case_spring_boot    | Spring Boot教程  | ROOT          | READ    | NULL
category_tech_docs  | 技术文档         | DRILL_UP      | VISIBLE | policy_yyy
```

### 权限检查验证
```kotlin
// Bob可以完整访问Spring Boot案例
println(hasPermission("bob", "case_spring_boot", "READ"))          // true

// Bob可以看到技术文档分类（导航需要），但不能读取分类内容
println(hasPermission("bob", "category_tech_docs", "VISIBLE"))     // true
println(hasPermission("bob", "category_tech_docs", "READ"))        // false

// Bob看不到其他案例
println(hasPermission("bob", "case_database_design", "READ"))      // false
println(hasPermission("bob", "case_business_process", "READ"))     // false
```

## 演示3：复杂权限组合

### 场景：用户Charlie的复杂权限
```kotlin
// 1. Charlie有技术文档分类的READ权限（会向下传递）
knowledgeBasePermissionService.grantCategoryPermission(
    categoryId = "category_tech_docs",
    userId = "charlie",
    permission = "READ"
)

// 2. Charlie还被单独授予了业务流程案例的WRITE权限
knowledgeBasePermissionService.grantCasePermission(
    caseId = "case_business_process", 
    userId = "charlie",
    permission = "WRITE"
)
```

### Charlie的最终权限状态
```sql
-- Charlie的所有权限
SELECT 
    a.resource_id,
    a.resource_name,
    p.strategy_type,
    p.rule_id,
    CASE 
        WHEN p.strategy_type = 'ROOT' THEN '直接授权'
        WHEN p.strategy_type = 'DRILL_DOWN' THEN '从分类继承'  
        WHEN p.strategy_type = 'DRILL_UP' THEN '从案例传递'
    END as permission_source
FROM access_policies p
JOIN access_acl a ON p.acl_id = a.id
WHERE p.value = 'charlie'
ORDER BY a.resource_type, p.strategy_type;
```

### 预期结果
```
resource_id         | resource_name    | strategy_type | rule_id | permission_source
--------------------|------------------|---------------|---------|------------------
category_tech_docs  | 技术文档         | ROOT          | READ    | 直接授权
category_business   | 业务文档         | DRILL_UP      | VISIBLE | 从案例传递
case_spring_boot    | Spring Boot教程  | DRILL_DOWN    | READ    | 从分类继承
case_database_design| 数据库设计       | DRILL_DOWN    | READ    | 从分类继承  
case_business_process| 业务流程        | ROOT          | WRITE   | 直接授权
```

### Charlie的权限验证
```kotlin
// 技术文档相关：完整访问权限
println(hasPermission("charlie", "category_tech_docs", "READ"))      // true
println(hasPermission("charlie", "case_spring_boot", "READ"))        // true
println(hasPermission("charlie", "case_database_design", "READ"))    // true

// 业务文档相关：混合权限
println(hasPermission("charlie", "category_business", "VISIBLE"))    // true (向上传递)
println(hasPermission("charlie", "category_business", "READ"))       // false
println(hasPermission("charlie", "case_business_process", "WRITE"))  // true (直接授权)
println(hasPermission("charlie", "case_business_process", "READ"))   // true (WRITE包含READ)
```

## 演示4：权限撤销

### 撤销Alice的分类权限
```kotlin
// 撤销Alice对技术文档分类的权限
knowledgeBasePermissionService.revokePermission(
    resourceId = "category_tech_docs",
    userId = "alice", 
    permission = "READ"
)
```

### 撤销后的效果
```kotlin
// 所有相关权限都被撤销
println(hasPermission("alice", "category_tech_docs", "READ"))      // false
println(hasPermission("alice", "case_spring_boot", "READ"))        // false
println(hasPermission("alice", "case_database_design", "READ"))    // false
```

## 实际应用场景

### 前端导航菜单构建
```kotlin
@RestController
class KnowledgeNavigationController {
    
    @GetMapping("/api/knowledge/navigation/{userId}")
    fun getNavigationMenu(@PathVariable userId: String): NavigationMenu {
        val permissions = knowledgeBasePermissionService.getUserKnowledgePermissions(userId)
        
        return buildNavigationMenu(permissions)
    }
    
    private fun buildNavigationMenu(permissions: List<UserPermissionView>): NavigationMenu {
        val categories = permissions.filter { it.resourceType == "KNOWLEDGE_CATEGORY" }
        val cases = permissions.filter { it.resourceType == "KNOWLEDGE_CASE" }
        
        return NavigationMenu(
            categories = categories.map { category ->
                CategoryNode(
                    id = category.resourceId,
                    name = category.resourceName,
                    canRead = category.permission == "READ",
                    canWrite = category.permission == "WRITE",
                    cases = cases.filter { it.resourceId.startsWith(category.resourceId) }
                        .map { case ->
                            CaseNode(
                                id = case.resourceId,
                                name = case.resourceName,
                                canRead = case.permission in listOf("READ", "WRITE"),
                                canWrite = case.permission == "WRITE"
                            )
                        }
                )
            }
        )
    }
}
```

### 权限检查拦截器
```kotlin
@Component
class KnowledgePermissionInterceptor : HandlerInterceptor {
    
    override fun preHandle(
        request: HttpServletRequest,
        response: HttpServletResponse, 
        handler: Any
    ): Boolean {
        val userId = getCurrentUserId(request)
        val resourceId = extractResourceId(request)
        val action = extractAction(request)
        
        val hasPermission = knowledgeBasePermissionService.hasPermission(userId, resourceId, action)
        
        if (!hasPermission) {
            response.status = HttpStatus.FORBIDDEN.value()
            return false
        }
        
        return true
    }
}
```

## 总结

这个知识库权限传递机制的核心特点：

1. **向下传递**：分类权限自动传递到所有子案例，确保用户能访问完整内容
2. **向上传递**：案例权限向上传递可见性，确保用户能看到导航路径
3. **权限隔离**：只授权特定案例时，其他案例保持隔离
4. **级联撤销**：撤销根权限时，所有传递的权限也会被清理
5. **灵活组合**：支持用户同时拥有多种来源的权限

这样的设计既满足了业务需求，又保持了权限系统的简洁性和高效性。
