package zone.loong.cube.framework.data.jimmer.service

import org.springframework.stereotype.Component
import org.slf4j.LoggerFactory
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.atomic.AtomicLong
import java.time.LocalDateTime
import java.time.Duration

/**
 * ACL性能监控组件
 * 监控权限刷新的性能指标
 */
@Component
class ACLPerformanceMonitor {
    
    private val logger = LoggerFactory.getLogger(ACLPerformanceMonitor::class.java)
    
    // 性能统计
    private val operationCounts = ConcurrentHashMap<String, AtomicLong>()
    private val operationTimes = ConcurrentHashMap<String, AtomicLong>()
    private val slowOperations = ConcurrentHashMap<String, MutableList<SlowOperation>>()
    
    // 阈值配置
    private val slowOperationThreshold = 5000L // 5秒
    private val maxSlowOperationRecords = 100
    
    /**
     * 记录操作开始
     */
    fun startOperation(operationType: String, resourceId: String): OperationContext {
        return OperationContext(operationType, resourceId, System.currentTimeMillis())
    }
    
    /**
     * 记录操作结束
     */
    fun endOperation(context: OperationContext, nodeCount: Int = 0) {
        val duration = System.currentTimeMillis() - context.startTime
        val operationType = context.operationType
        
        // 更新统计
        operationCounts.computeIfAbsent(operationType) { AtomicLong(0) }.incrementAndGet()
        operationTimes.computeIfAbsent(operationType) { AtomicLong(0) }.addAndGet(duration)
        
        // 记录慢操作
        if (duration > slowOperationThreshold) {
            recordSlowOperation(context, duration, nodeCount)
        }
        
        logger.debug("ACL操作完成: {} - {}, 耗时: {}ms, 节点数: {}", 
            operationType, context.resourceId, duration, nodeCount)
    }
    
    /**
     * 记录慢操作
     */
    private fun recordSlowOperation(context: OperationContext, duration: Long, nodeCount: Int) {
        val slowOp = SlowOperation(
            operationType = context.operationType,
            resourceId = context.resourceId,
            duration = duration,
            nodeCount = nodeCount,
            timestamp = LocalDateTime.now()
        )
        
        slowOperations.computeIfAbsent(context.operationType) { mutableListOf() }.apply {
            add(slowOp)
            // 保持记录数量在限制内
            if (size > maxSlowOperationRecords) {
                removeAt(0)
            }
        }
        
        logger.warn("检测到慢ACL操作: {} - {}, 耗时: {}ms, 节点数: {}", 
            context.operationType, context.resourceId, duration, nodeCount)
    }
    
    /**
     * 获取性能统计
     */
    fun getPerformanceStats(): ACLPerformanceStats {
        val stats = mutableMapOf<String, OperationStats>()
        
        operationCounts.forEach { (operationType, count) ->
            val totalTime = operationTimes[operationType]?.get() ?: 0L
            val avgTime = if (count.get() > 0) totalTime / count.get() else 0L
            
            stats[operationType] = OperationStats(
                operationType = operationType,
                totalCount = count.get(),
                totalTime = totalTime,
                averageTime = avgTime,
                slowOperationCount = slowOperations[operationType]?.size ?: 0
            )
        }
        
        return ACLPerformanceStats(
            operationStats = stats,
            slowOperations = slowOperations.toMap()
        )
    }
    
    /**
     * 重置统计数据
     */
    fun resetStats() {
        operationCounts.clear()
        operationTimes.clear()
        slowOperations.clear()
        logger.info("ACL性能统计数据已重置")
    }
    
    /**
     * 获取慢操作建议
     */
    fun getOptimizationSuggestions(): List<String> {
        val suggestions = mutableListOf<String>()
        val stats = getPerformanceStats()
        
        stats.operationStats.forEach { (operationType, stat) ->
            when {
                stat.averageTime > 3000 -> {
                    suggestions.add("$operationType 操作平均耗时过长(${stat.averageTime}ms)，建议增加批处理大小或启用异步处理")
                }
                stat.slowOperationCount > 10 -> {
                    suggestions.add("$operationType 操作频繁出现慢查询(${stat.slowOperationCount}次)，建议检查数据库索引或优化查询")
                }
                stat.totalCount > 1000 && stat.averageTime > 1000 -> {
                    suggestions.add("$operationType 操作量大且耗时较长，建议考虑缓存策略")
                }
            }
        }
        
        return suggestions
    }
}

/**
 * 操作上下文
 */
data class OperationContext(
    val operationType: String,
    val resourceId: String,
    val startTime: Long
)

/**
 * 慢操作记录
 */
data class SlowOperation(
    val operationType: String,
    val resourceId: String,
    val duration: Long,
    val nodeCount: Int,
    val timestamp: LocalDateTime
)

/**
 * 操作统计
 */
data class OperationStats(
    val operationType: String,
    val totalCount: Long,
    val totalTime: Long,
    val averageTime: Long,
    val slowOperationCount: Int
)

/**
 * ACL性能统计
 */
data class ACLPerformanceStats(
    val operationStats: Map<String, OperationStats>,
    val slowOperations: Map<String, List<SlowOperation>>
)

/**
 * 性能监控注解
 */
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class MonitorACLPerformance(val operationType: String = "")

/**
 * 性能监控切面
 */
@Component
class ACLPerformanceAspect(
    private val performanceMonitor: ACLPerformanceMonitor
) {
    
    // 这里可以添加AOP切面逻辑来自动监控带有@MonitorACLPerformance注解的方法
}
