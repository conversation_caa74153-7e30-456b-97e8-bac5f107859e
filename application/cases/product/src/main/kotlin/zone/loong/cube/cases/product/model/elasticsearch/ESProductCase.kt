package zone.loong.cube.cases.product.model.elasticsearch

import org.babyfish.jimmer.kt.isLoaded
import org.springframework.data.elasticsearch.annotations.Document
import org.springframework.data.elasticsearch.annotations.Field
import org.springframework.data.elasticsearch.annotations.FieldType
import zone.loong.cube.cases.product.model.CaseStatus
import zone.loong.cube.cases.product.model.ProductCase
import zone.loong.cube.framework.data.elasticsearch.ACL
import zone.loong.cube.framework.data.elasticsearch.Policy


@Document(indexName = "cases_product")
class ESProductCase : ACL() {
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private val keywords: String = ""

    //关联分类
    @Field(type = FieldType.Keyword)
    var classifyId: String = ""

    @Field(type = FieldType.Keyword)
    var path: List<String> = emptyList()
    @Field(type = FieldType.Keyword)
    var pathName: String = ""

    @Field(type = FieldType.Text)
    var attachment: List<String> = emptyList()

    @Field(type = FieldType.Keyword)
    //案例状态
    var status: CaseStatus = CaseStatus.ON_SHELVES

    @Field(type = FieldType.Text, analyzer = "ik_max_word", copyTo = ["keywords"])
    var title: String = ""

    @Field(type = FieldType.Keyword, copyTo = ["keywords"])
    var tags: List<String> = emptyList()

    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart", copyTo = ["keywords"])
    var content: MutableList<String> = mutableListOf()

    companion object {
        fun buildFromCase(case: ProductCase): ESProductCase {
            return ESProductCase().apply {
                id = case.id
                classifyId = case.classify?.id ?: ""
                path = (case.classify?.flattenParent()?.map { it.id }?:emptyList()) +listOf(classifyId)
                pathName= ((case.classify?.flattenParent()?.map { it.name }?:emptyList()) +listOf(classifyId)).joinToString("/")
                attachment = case.attachments.map { it.file.id }
                status = case.status
                title = case.title
                tags = case.tags
                createdBy = case.createdBy
                createdTime = case.createdTime
                modifiedBy = case.modifiedBy
                modifiedTime = case.modifiedTime
                //复制权限
                if (isLoaded(case, ProductCase::acl)) {
                    case.acl?.let { acl ->
                        policies = acl.policies.map { policy ->
                            Policy().apply {
                                policyType = policy.policyType
                                strategyType = policy.strategyType
                                value = policy.value
                            }
                        }
                    }
                }
            }
        }
    }
}