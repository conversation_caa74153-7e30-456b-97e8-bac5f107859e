package zone.loong.cube.interior.controller

import org.springframework.web.bind.annotation.*
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.interior.model.InteriorPositions
import zone.loong.cube.interior.model.dto.InteriorPositionsSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorPositions
import zone.loong.cube.interior.service.InteriorPositionsService

@RestController
@RequestMapping("/interior/positions")
class InteriorPositionsController(
    private val interiorPositionsService: InteriorPositionsService
) {

    @PostMapping("/page")
    fun fetchCases(@RequestBody pageQuery: PageQuery<InteriorPositionsSpecification>): Page<InteriorPositions> {
        return interiorPositionsService.fetchPositions(pageQuery)
    }

    @PostMapping("/save")
    fun save(@RequestBody saveInteriorPositions: SaveInteriorPositions): InteriorPositions {
        return interiorPositionsService.save(saveInteriorPositions)
    }

    @GetMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        interiorPositionsService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): InteriorPositions {
        return interiorPositionsService.detail(id)
    }
}