package zone.loong.cube.news.model

import com.fasterxml.jackson.annotation.JsonValue
import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.DissociateAction
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.JoinColumn
import org.babyfish.jimmer.sql.JoinTable
import org.babyfish.jimmer.sql.Key
import org.babyfish.jimmer.sql.LogicalDeleted
import org.babyfish.jimmer.sql.ManyToMany
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OnDissociate
import org.babyfish.jimmer.sql.OneToMany
import org.babyfish.jimmer.sql.OneToOne
import org.babyfish.jimmer.sql.OrderedProp
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail

/**
 * 模块管理
 */
@Entity
interface NewsBlock : AuditAware {

    /**
     * 唯一标识
     */
    @Key
    val code: String

    /**
     * 模块名称
     */
    @Key
    val title: String

    /**
     * 模块图片
     */
    @OneToOne
    @JoinColumn(name = "IMAGE_ID")
    val image: FileDetail?

    @ManyToOne
    @JoinColumn(name = "NAVIGATION_ID")
    @OnDissociate(DissociateAction.DELETE)
    val navigation: NewsNavigation

    /**
     * 报刊
     */
    @OneToMany(mappedBy = "newsBlock")
    val newsPapers: List<NewsNewspaper>

    /**
     * 报刊
     */
    @OneToMany(mappedBy = "newsBlock",
        orderedProps = [
            OrderedProp("topStatus", desc = true),
            OrderedProp("sequence", desc = false)
        ])
    val newsArticle: List<NewsResourceManagerArticle>

    /**
     * 报刊
     */
//    @ManyToMany(
//        orderedProps = [
//            OrderedProp("topStatus", desc = true),
//            OrderedProp("issueTime", desc = true)
//        ]
//    )
//    @JoinTable(
//        name = "NEWS_RESOURCE_MANAGER_BLOCK",
//        joinColumnName = "BLOCK_ID",
//        inverseJoinColumnName = "NEWS_ID"
//    )
//    val newsResourceManager: List<NewsResourceManager>

    /**
     * 说明
     */
    val remark: String?

    /**
     * 序列
     */
    val sequence: Integer?

    /**
     * 状态
     */
    val status: BlockType?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}

enum class BlockType(@JsonValue val value: String) {
    ENABLED("ENABLED"),
    DISABLED("DISABLED");
}