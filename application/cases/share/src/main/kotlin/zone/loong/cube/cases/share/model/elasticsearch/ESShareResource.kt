package zone.loong.cube.cases.share.model.elasticsearch

import com.fasterxml.jackson.annotation.JsonInclude
import org.babyfish.jimmer.kt.isLoaded
import org.dromara.x.file.storage.core.FileInfo
import org.springframework.data.elasticsearch.annotations.Document
import org.springframework.data.elasticsearch.annotations.Field
import org.springframework.data.elasticsearch.annotations.FieldType
import org.springframework.data.elasticsearch.annotations.Mapping
import zone.loong.cube.cases.share.model.ShareResource
import zone.loong.cube.cases.share.model.ShareResourceType
import zone.loong.cube.framework.data.elasticsearch.ACL
import zone.loong.cube.framework.data.elasticsearch.Policy
import zone.loong.cube.framework.storage.kernel.service.StorageConvert.Companion.toFileInfo


@Document(indexName = "cases_share")
@JsonInclude(JsonInclude.Include.NON_EMPTY)
class ESShareResource : ACL() {
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private val keywords: String = ""

    @Field(type = FieldType.Text, copyTo = ["keywords"])
    var name: String = ""

    //上级id
    @Field(type = FieldType.Keyword)
    var parentId: String = ""

    //上级路径
    @Field(type = FieldType.Nested)
    @Mapping
    var path: List<PathInfo> = emptyList()


    //类型
    @Field(type = FieldType.Keyword)
    var type: ShareResourceType = ShareResourceType.DIRECTORY

    @Field(type = FieldType.Nested)
    @Mapping
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    var file: File? = null

    @Field(type = FieldType.Keyword, copyTo = ["keywords"])
    var tags: List<String> = emptyList()

    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart", copyTo = ["keywords"])
    var content: MutableList<String> = mutableListOf()

    companion object {
        fun buildFromResource(resource: ShareResource): ESShareResource {
            return ESShareResource().apply {
                id = resource.id
                name = resource.name
                type = resource.type
                parentId = resource.parent?.id ?: ""
                path = resource.getAllPath().map { PathInfo(it.id, it.name) }.toMutableList()
                file = resource.file?.let { toFile(toFileInfo(it))}
                tags = resource.tags
                createdBy = resource.createdBy
                createdTime = resource.createdTime
                modifiedBy = resource.modifiedBy
                modifiedTime = resource.modifiedTime
                //复制权限
                if (isLoaded(resource, ShareResource::acl)) {
                    resource.acl?.let { acl ->
                        policies = acl.policies.map { policy ->
                            Policy().apply {
                                policyType = policy.policyType
                                strategyType = policy.strategyType
                                value = policy.value
                            }
                        }
                    }
                }
            }
        }

        private fun toFile(fileInfo: FileInfo): File{
          return  File().apply {
                id = fileInfo.id
                url = fileInfo.url
                size = fileInfo.size
                filename = fileInfo.filename
                originalFilename = fileInfo.originalFilename
                basePath = fileInfo.basePath
                path = fileInfo.path
                ext = fileInfo.ext
                contentType = fileInfo.contentType
                platform = fileInfo.platform
                thUrl = fileInfo.thUrl
                thFilename = fileInfo.thFilename
                thSize = fileInfo.thSize
                thContentType = fileInfo.thContentType
                objectId = fileInfo.objectId
                objectType = fileInfo.objectType
                metadata = fileInfo.metadata
                userMetadata = fileInfo.userMetadata
                thMetadata = fileInfo.thMetadata
                thUserMetadata = fileInfo.thUserMetadata
                attr = fileInfo.attr
                fileAcl = fileInfo.fileAcl
                createTime =fileInfo.createTime
            }
        }
    }
}

data class PathInfo(
    @Field(type = FieldType.Keyword)
    val id: String,

    @Field(type = FieldType.Keyword)
    val name: String
)
@JsonInclude(JsonInclude.Include.NON_EMPTY)
class File : FileInfo()
