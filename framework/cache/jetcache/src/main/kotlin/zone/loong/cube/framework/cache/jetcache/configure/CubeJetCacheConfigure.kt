package zone.loong.cube.framework.cache.jetcache.configure

import com.alicp.jetcache.anno.CacheConsts
import com.alicp.jetcache.anno.config.EnableMethodCache
import com.alicp.jetcache.anno.support.GlobalCacheConfig
import com.alicp.jetcache.anno.support.JetCacheBaseBeans
import com.alicp.jetcache.embedded.CaffeineCacheBuilder.createCaffeineCacheBuilder
import com.alicp.jetcache.redisson.RedissonCacheBuilder.createBuilder
import com.alicp.jetcache.support.JacksonKeyConvertor
import com.fasterxml.jackson.databind.ObjectMapper
import org.redisson.api.RedissonClient
import org.redisson.spring.starter.RedissonAutoConfiguration
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import zone.loong.cube.framework.cache.jetcache.CubeJetCacheProperties
import zone.loong.cube.framework.cache.jetcache.JacksonValueDecoder
import zone.loong.cube.framework.cache.jetcache.JacksonValueEncoder


@Configuration
@EnableMethodCache(basePackages = ["zone.loong.cube"])
@Import(JetCacheBaseBeans::class,RedissonAutoConfiguration::class)
@EnableConfigurationProperties(CubeJetCacheProperties::class)
class CubeJetCacheConfigure(
    private val cubeJetCacheProperties: CubeJetCacheProperties,
    private val redisson: RedissonClient,
    private val objectMapper: ObjectMapper
) {
    @Bean
    fun jacksonValueDecoder(objectMapper: ObjectMapper): JacksonValueDecoder {
        return JacksonValueDecoder(objectMapper)
    }

    @Bean
    fun jacksonValueEncoder(objectMapper: ObjectMapper): JacksonValueEncoder {
        return JacksonValueEncoder(objectMapper)
    }

    @Bean
    fun globalCacheConfig(jacksonValueDecoder :JacksonValueDecoder,jacksonValueEncoder:JacksonValueEncoder): GlobalCacheConfig {

        val globalCacheConfig = GlobalCacheConfig().apply {
            statIntervalMinutes = cubeJetCacheProperties.statIntervalMinutes
            //本地缓存配置
            localCacheBuilders = mapOf(CacheConsts.DEFAULT_AREA to createCaffeineCacheBuilder().apply {
                keyConvertor(JacksonKeyConvertor.INSTANCE)
                limit(cubeJetCacheProperties.limit)
            })
            //远程缓存配置
            remoteCacheBuilders = mapOf(CacheConsts.DEFAULT_AREA to createBuilder().apply {
                keyConvertor(JacksonKeyConvertor.INSTANCE)
                valueEncoder(jacksonValueEncoder)
                valueDecoder(jacksonValueDecoder)
                broadcastChannel(cubeJetCacheProperties.broadcastChannel)
                redissonClient(redisson)
            })
        }

        return globalCacheConfig
    }


}