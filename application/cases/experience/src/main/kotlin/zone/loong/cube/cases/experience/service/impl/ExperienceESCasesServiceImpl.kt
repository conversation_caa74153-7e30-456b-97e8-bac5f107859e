package zone.loong.cube.cases.experience.service.impl

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.data.elasticsearch.core.ElasticsearchOperations
import org.springframework.data.elasticsearch.core.document.Document
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates
import org.springframework.data.elasticsearch.core.query.UpdateQuery
import org.springframework.stereotype.Service
import zone.loong.cube.cases.experience.model.ExperienceCase
import zone.loong.cube.cases.experience.model.by
import zone.loong.cube.cases.experience.model.elasticsearch.ESExperienceCase
import zone.loong.cube.cases.experience.model.fetchBy
import zone.loong.cube.cases.experience.model.id
import zone.loong.cube.cases.experience.service.ExperienceESCasesService
import zone.loong.cube.framework.data.elasticsearch.service.SearchService
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.framework.tika.service.TikaService
import zone.loong.cube.platform.storage.service.StorageService

@Service
class ExperienceESCasesServiceImpl(
    private val searchService: SearchService,
    private val kSqlClient: KSqlClient,
    private val storageService: StorageService,
    private val elasticsearchOperations: ElasticsearchOperations,
    private val tikaService: TikaService
) : ExperienceESCasesService {
    override fun sync(caseId: String, extractFile: Boolean) {
        val case = kSqlClient.findById(newFetcher(ExperienceCase::class).by {
            allTableFields()
            classify {
                `parent*`()
            }
            acl {
                allScalarFields()
                policies {
                    allScalarFields()
                }
            }
        }, caseId) ?: throw BizException("案例被删除")
        val esExperienceCase = ESExperienceCase.buildFromCase(case).apply {
            if (extractFile) {
                storageService.download(fileId).inputStream { inputStream ->
                    content = tikaService.getText(inputStream)
                }
            }
        }
        searchService.save(esExperienceCase)
    }


    override fun updateAcl(caseIds: List<String>) {
        if (caseIds.isEmpty()) {
            return
        }
        kSqlClient.createQuery(ExperienceCase::class) {
            where(table.id valueIn caseIds)
            select(table.fetchBy {
                allTableFields()
                classify {
                    `parent*`()
                }
                acl {
                    policies {
                        allScalarFields()
                    }
                }
            })
        }.let {
            val updateQueries = it.map { case ->
                val document = Document.create()
                document["policies"] = case.acl?.policies?.map { policy ->
                    mapOf(
                        "policyType" to policy.policyType,
                        "strategyType" to policy.strategyType,
                        "value" to policy.value
                    )
                }

                UpdateQuery.builder(case.id)
                    .withDocument(document)
                    .build()
            }
            if (updateQueries.isNotEmpty()){
                elasticsearchOperations.bulkUpdate(updateQueries, IndexCoordinates.of("cases_experiences"))
            }

        }

    }

    override fun syncWithFile(caseIds: List<String>){
        kSqlClient.filters {
            disableByTypes(ACLFilter::class)
        }.createQuery(ExperienceCase::class) {
            where(table.id valueIn caseIds)
            select(table.fetchBy {
                allTableFields()
                classify {
                    `parent*`()
                }
                acl {
                    allScalarFields()
                    policies {
                        allScalarFields()
                    }
                }
            })
        }.map {
            ESExperienceCase.buildFromCase(it).apply {
                storageService.download(fileId).inputStream { inputStream ->
                    content = tikaService.getText(inputStream)
                }
            }
        }.forEach {
            searchService.save(it)
        }
    }

    override fun delete(caseId: String) {
        searchService.deleteById(caseId, ESExperienceCase::class)
    }
}