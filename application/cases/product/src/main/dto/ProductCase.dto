export zone.loong.cube.cases.product.model.ProductCase
    -> package zone.loong.cube.cases.product.model.dto



input SaveCase{
    #allScalars(this)
    id?
    id(classify)
    attachments{
        id(file)
    }
}

CaseViewWithName{
    #allScalars(this)
    id
    id(classify)
    attachments{
        id
        file{
            id
            originalFilename as fileName
            ext
            size
            metadata
        }
    }
}