# 增强的ACL权限设计方案

## 当前设计的局限性

### 1. 权限模型单一
- 只支持树形继承，缺乏灵活性
- 没有考虑跨部门、临时授权等场景
- 权限粒度不够细化

### 2. 缺乏权限表达式
- 无法支持复杂的权限条件
- 缺乏动态权限计算能力

### 3. 性能瓶颈
- 权限检查时需要遍历整个继承链
- 缺乏有效的权限缓存机制

## 增强设计方案

### 1. 多维度权限模型

```kotlin
// 权限维度枚举
enum class PermissionDimension {
    ORGANIZATION,  // 组织维度
    ROLE,         // 角色维度  
    PROJECT,      // 项目维度
    TEMPORAL,     // 时间维度
    CUSTOM        // 自定义维度
}

// 增强的权限策略
interface EnhancedPolicy {
    val dimension: PermissionDimension
    val scope: String              // 权限范围
    val actions: Set<String>       // 允许的操作
    val conditions: String?        // 权限条件表达式
    val priority: Int              // 权限优先级
    val effectiveTime: TimeRange?  // 生效时间
}
```

### 2. 权限表达式引擎

```kotlin
// 权限表达式示例
val expressions = listOf(
    "user.department == resource.department",
    "user.level >= resource.securityLevel",
    "current_time between resource.startTime and resource.endTime",
    "user.projects contains resource.projectId"
)

@Component
class PermissionExpressionEngine {
    
    fun evaluate(expression: String, context: PermissionContext): Boolean {
        val parser = SpelExpressionParser()
        val spelContext = StandardEvaluationContext().apply {
            setVariable("user", context.user)
            setVariable("resource", context.resource)
            setVariable("current_time", LocalDateTime.now())
        }
        
        return try {
            parser.parseExpression(expression).getValue(spelContext, Boolean::class.java) ?: false
        } catch (e: Exception) {
            logger.warn("权限表达式执行失败: $expression", e)
            false
        }
    }
}
```

### 3. 分层权限缓存

```kotlin
@Component
class LayeredPermissionCache {
    
    // L1: 本地缓存 - 用户会话级别
    private val sessionCache = ConcurrentHashMap<String, UserPermissions>()
    
    // L2: Redis缓存 - 应用级别
    @Cacheable("user-permissions", key = "#userId")
    fun getUserPermissions(userId: String): UserPermissions {
        return computeUserPermissions(userId)
    }
    
    // L3: 预计算缓存 - 定期刷新
    @Scheduled(fixedRate = 300000) // 5分钟
    fun precomputeCommonPermissions() {
        // 预计算常用权限组合
    }
    
    // 权限失效策略
    @EventListener
    fun onPermissionChanged(event: PermissionChangedEvent) {
        // 精确失效相关缓存
        invalidateRelatedCache(event.affectedUsers)
    }
}
```

### 4. 权限决策引擎

```kotlin
@Component
class PermissionDecisionEngine {
    
    fun hasPermission(
        userId: String, 
        resourceId: String, 
        action: String
    ): PermissionDecision {
        
        val context = buildPermissionContext(userId, resourceId, action)
        val policies = collectApplicablePolicies(context)
        
        return evaluatePermissions(policies, context)
    }
    
    private fun evaluatePermissions(
        policies: List<EnhancedPolicy>, 
        context: PermissionContext
    ): PermissionDecision {
        
        // 按优先级排序
        val sortedPolicies = policies.sortedByDescending { it.priority }
        
        var decision = PermissionDecision.DENY
        val reasons = mutableListOf<String>()
        
        for (policy in sortedPolicies) {
            val result = evaluatePolicy(policy, context)
            
            when (result.effect) {
                PolicyEffect.ALLOW -> {
                    decision = PermissionDecision.ALLOW
                    reasons.add("允许: ${result.reason}")
                    break // 第一个允许的策略生效
                }
                PolicyEffect.DENY -> {
                    decision = PermissionDecision.DENY
                    reasons.add("拒绝: ${result.reason}")
                    break // 明确拒绝
                }
                PolicyEffect.ABSTAIN -> {
                    reasons.add("弃权: ${result.reason}")
                    // 继续评估下一个策略
                }
            }
        }
        
        return PermissionDecision(
            effect = decision,
            reasons = reasons,
            evaluatedPolicies = sortedPolicies.size
        )
    }
}
```

### 5. 权限审计和监控

```kotlin
@Component
class PermissionAuditService {
    
    @EventListener
    @Async
    fun auditPermissionCheck(event: PermissionCheckEvent) {
        val auditRecord = PermissionAuditRecord(
            userId = event.userId,
            resourceId = event.resourceId,
            action = event.action,
            decision = event.decision,
            reasons = event.reasons,
            timestamp = LocalDateTime.now(),
            sessionId = event.sessionId,
            ipAddress = event.ipAddress
        )
        
        // 异步保存审计记录
        auditRepository.save(auditRecord)
        
        // 检测异常权限访问
        detectAnomalousAccess(auditRecord)
    }
    
    private fun detectAnomalousAccess(record: PermissionAuditRecord) {
        // 检测可疑的权限访问模式
        // 例如：频繁的权限拒绝、异常时间访问等
    }
}
```

## 高级特性

### 1. 动态权限计算

```kotlin
// 支持基于上下文的动态权限
@Component
class DynamicPermissionProvider {
    
    fun getDynamicPermissions(context: PermissionContext): Set<String> {
        val permissions = mutableSetOf<String>()
        
        // 基于时间的权限
        if (isWorkingHours()) {
            permissions.add("WORK_TIME_ACCESS")
        }
        
        // 基于位置的权限
        if (isInOffice(context.userLocation)) {
            permissions.add("OFFICE_ACCESS")
        }
        
        // 基于设备的权限
        if (isTrustedDevice(context.deviceId)) {
            permissions.add("TRUSTED_DEVICE_ACCESS")
        }
        
        return permissions
    }
}
```

### 2. 权限委托机制

```kotlin
@Entity
interface PermissionDelegation {
    val delegatorId: String      // 委托人
    val delegateeId: String      // 被委托人
    val permissions: Set<String> // 委托的权限
    val startTime: LocalDateTime // 开始时间
    val endTime: LocalDateTime   // 结束时间
    val conditions: String?      // 委托条件
}

@Component
class PermissionDelegationService {
    
    fun delegatePermissions(
        delegatorId: String,
        delegateeId: String, 
        permissions: Set<String>,
        duration: Duration,
        conditions: String? = null
    ) {
        // 验证委托人是否有权限进行委托
        validateDelegationAuthority(delegatorId, permissions)
        
        // 创建委托记录
        val delegation = PermissionDelegation {
            this.delegatorId = delegatorId
            this.delegateeId = delegateeId
            this.permissions = permissions
            this.startTime = LocalDateTime.now()
            this.endTime = LocalDateTime.now().plus(duration)
            this.conditions = conditions
        }
        
        saveDelegation(delegation)
        
        // 清除相关权限缓存
        permissionCache.evictUserPermissions(delegateeId)
    }
}
```

### 3. 权限模板和继承

```kotlin
@Entity
interface PermissionTemplate {
    val name: String
    val description: String
    val permissions: Set<String>
    val inheritsFrom: List<PermissionTemplate>
}

@Component
class PermissionTemplateService {
    
    fun applyTemplate(userId: String, templateName: String) {
        val template = getTemplate(templateName)
        val expandedPermissions = expandTemplate(template)
        
        // 应用权限模板
        userPermissionService.grantPermissions(userId, expandedPermissions)
    }
    
    private fun expandTemplate(template: PermissionTemplate): Set<String> {
        val permissions = template.permissions.toMutableSet()
        
        // 递归展开继承的模板
        template.inheritsFrom.forEach { parentTemplate ->
            permissions.addAll(expandTemplate(parentTemplate))
        }
        
        return permissions
    }
}
```

## 性能优化策略

### 1. 权限预计算

```kotlin
@Component
class PermissionPrecomputer {
    
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点
    fun precomputeUserPermissions() {
        val activeUsers = userService.getActiveUsers()
        
        activeUsers.parallelStream().forEach { user ->
            try {
                val permissions = computeFullUserPermissions(user.id)
                permissionCache.preloadUserPermissions(user.id, permissions)
            } catch (e: Exception) {
                logger.error("预计算用户权限失败: ${user.id}", e)
            }
        }
    }
}
```

### 2. 权限索引优化

```sql
-- 权限查询优化索引
CREATE INDEX idx_policy_user_resource ON access_policies(user_id, resource_type, action);
CREATE INDEX idx_policy_effective_time ON access_policies(effective_start, effective_end);
CREATE INDEX idx_delegation_delegatee_time ON permission_delegations(delegatee_id, start_time, end_time);

-- 分区表优化（按时间分区）
CREATE TABLE permission_audit_logs (
    id BIGINT PRIMARY KEY,
    user_id VARCHAR(50),
    resource_id VARCHAR(50),
    action VARCHAR(50),
    decision VARCHAR(10),
    audit_time TIMESTAMP
) PARTITION BY RANGE (audit_time);
```

## 总结

这个增强设计相比原方案的优势：

1. **更灵活的权限模型**：支持多维度、动态权限
2. **更强的表达能力**：权限表达式引擎
3. **更好的性能**：分层缓存、预计算
4. **更完善的审计**：全链路权限追踪
5. **更丰富的功能**：委托、模板、继承

这样的设计能够应对更复杂的企业级权限需求，同时保持良好的性能和可维护性。
