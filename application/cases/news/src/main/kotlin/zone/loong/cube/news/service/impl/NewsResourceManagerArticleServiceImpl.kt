package zone.loong.cube.news.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.desc
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsResourceManagerArticle
import zone.loong.cube.news.model.TopType
import zone.loong.cube.news.model.dto.NewsArticleSpecification
import zone.loong.cube.news.model.dto.SaveNewsArticle
import zone.loong.cube.news.model.dto.SaveNewsArticles
import zone.loong.cube.news.model.fetchBy
import zone.loong.cube.news.model.issueTime
import zone.loong.cube.news.model.news
import zone.loong.cube.news.model.sequence
import zone.loong.cube.news.model.topStatus
import zone.loong.cube.news.service.NewsResourceManagerArticleService
import zone.loong.cube.platform.organization.model.Org
import java.util.Collections.synchronizedList

@Service
class NewsResourceManagerArticleServiceImpl(
    private val kSqlClient: KSqlClient
) : NewsResourceManagerArticleService {

    override fun fetchArticles(pageQuery: PageQuery<NewsArticleSpecification>): Page<NewsResourceManagerArticle> {
        return kSqlClient.page(pageQuery){
            orderBy(table.topStatus.desc())
            orderBy(table.sequence)
            orderBy(table.news.issueTime.desc())
            select(table.fetchBy {
                topStatus()
                sequence()
                imgStatus()
                news {
                    title()
                    author()
                    newsType()
                    issueTime()
                }
                newsBlock {
                    title()
                }
            })
        }
    }

    override fun save(saveNewsArticle: SaveNewsArticle): NewsResourceManagerArticle {
        return (saveNewsArticle.id?.let {
            kSqlClient.entities.saveCommand(saveNewsArticle, SaveMode.UPSERT).execute().modifiedEntity
        }?:let{
            kSqlClient.entities.saveCommand(saveNewsArticle, SaveMode.INSERT_ONLY).execute().modifiedEntity
        })
    }

    override fun saveArticles(saveNewsArticles: SaveNewsArticles) {
        val articleList = synchronizedList(mutableListOf<NewsResourceManagerArticle>())
        saveNewsArticles.newsId.split( ",").forEach { it ->
            articleList.add(NewsResourceManagerArticle {
                newsBlockId = saveNewsArticles.newsBlockId
                newsId = it
                topStatus = TopType.FALSE
            })
        }
        kSqlClient.entities.saveEntities(articleList)
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(NewsResourceManagerArticle::class, id)
    }

    override fun detail(id: String): NewsResourceManagerArticle {
        return kSqlClient.findById(NewsResourceManagerArticle::class, id) ?: throw Exception("该模块未找到")
    }


}