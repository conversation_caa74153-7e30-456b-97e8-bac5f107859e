package zone.loong.cube.framework.data.kernel.configure

import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import zone.loong.cube.framework.data.kernel.service.AuthProvider
import zone.loong.cube.framework.data.kernel.service.Impl.DefaultAuthProvider

@Configuration
class DataKernelAuthProviderConfigure {
    @Bean
    @ConditionalOnMissingBean(AuthProvider::class)
    fun authProvider(): AuthProvider {
        return DefaultAuthProvider()
    }
}