package zone.loong.cube.platform.auth.controller

import org.springframework.web.bind.annotation.*
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.Role
import zone.loong.cube.platform.auth.model.dto.AccountWithUserInfo
import zone.loong.cube.platform.auth.model.dto.RoleAuthorization
import zone.loong.cube.platform.auth.model.dto.RoleSave
import zone.loong.cube.platform.auth.model.dto.RoleSpecification
import zone.loong.cube.platform.auth.service.RoleService

@RestController
@RequestMapping("/auth/role/")
class RoleController(private val roleService: RoleService) {
    @PostMapping("page")
    fun fetchRole(@RequestBody pageQuery: PageQuery<RoleSpecification>): Page<Role> {
        return roleService.fetchRole(pageQuery)
    }


    @GetMapping("/accounts")
    fun fetchAccounts(@RequestParam("roleId") roleId: String):List<AccountWithUserInfo> {
        return roleService.fetchAccounts(roleId)
    }

    @GetMapping("/permissions")
    fun fetchPermissions(@RequestParam("roleId") roleId: String):List<String> {
        return roleService.fetchPermissions(roleId)
    }

    @PostMapping("/save")
    fun saveRole(@RequestBody role: RoleSave): Role {
        return roleService.saveRole(role)
    }

    //授权
    @PostMapping("/authorization")
    fun authorization(@RequestBody roleAuthorization: RoleAuthorization) {
        roleService.authorize(roleAuthorization)
    }
}