import pub.ihub.plugin.IHubSettingsExtension

plugins {
    id("pub.ihub.plugin.ihub-settings") version "1.7.7"
}

configure<IHubSettingsExtension> {
    includeProjects("kernel").onlySubproject.prefix("cube-framework-kernel-")
    includeProjects("auth").onlySubproject.prefix("cube-framework-auth-")
    includeProjects("data").onlySubproject.prefix("cube-framework-data-")
    includeProjects("http").onlySubproject.prefix("cube-framework-http-")
    includeProjects("cache").onlySubproject.prefix("cube-framework-cache-")
    includeProjects("starter").onlySubproject.prefix("cube-framework-starter-")
    includeProjects("storage").onlySubproject.prefix("cube-framework-storage-")
    includeProjects("tika").onlySubproject.prefix("cube-framework-tika-")
}
