package zone.loong.cube.interior.service

import zone.loong.cube.interior.model.InteriorPositionsDatum
import zone.loong.cube.interior.model.dto.InteriorPositionsDatumSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorPositionsDatum
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

interface InteriorPositionsDatumService {
    fun fetchPositionsDatum(pageQuery: PageQuery<InteriorPositionsDatumSpecification>): Page<InteriorPositionsDatum>
    fun save(saveInteriorPositionsDatum: SaveInteriorPositionsDatum): InteriorPositionsDatum
    fun delete(id: String)
    fun detail(id: String): InteriorPositionsDatum
}