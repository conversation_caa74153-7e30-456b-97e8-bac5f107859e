package zone.loong.cube.framework.data.jimmer.service

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.PolicyTool
import zone.loong.cube.framework.data.jimmer.event.ResourcePolicyUpdate
import zone.loong.cube.framework.data.jimmer.model.*
import zone.loong.cube.framework.data.kernel.model.StrategyType
import com.github.f4b6a3.ulid.UlidCreator
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn

/**
 * ACL权限管理服务
 * 统一处理ACL的创建、更新、树结构变更等操作
 */
@Service
class ACLManagementService(
    private val kSqlClient: KSqlClient,
    private val applicationEventPublisher: ApplicationEventPublisher
) {

    /**
     * 为新创建的资源初始化ACL
     */
    @Transactional
    fun initializeACL(resource: ACLAware): ACL {
        val aclId = UlidCreator.getUlid().toString()
        
        val acl = ACL {
            id = aclId
            resourceId = resource.id
            resourceType = resource.resourceType()
            resourceName = resource.resourceName()
            
            // 设置父级关系
            resource.parentResourceId()?.let { parentId ->
                parent {
                    resourceId = parentId
                }
            }
            
            // 继承父级权限
            policies = inheritParentPolicies(aclId, resource.parentResourceId())
        }
        
        val savedACL = saveACL(acl)
        
        // 发布权限更新事件
        publishPolicyUpdateEvent(listOf(savedACL.resourceId), savedACL.resourceType, savedACL.resourceName)
        
        return savedACL
    }

    /**
     * 处理树结构父节点迁移
     */
    @Transactional
    fun handleParentMigration(resourceId: String, oldParentId: String?, newParentId: String?) {
        val acl = fetchACLByResourceId(resourceId)
        
        // 更新父级关系
        val updatedACL = acl.copy {
            newParentId?.let { 
                parent {
                    resourceId = it
                }
            } ?: run {
                parent = null
            }
        }
        
        saveACL(updatedACL)
        
        // 重新计算权限继承
        refreshInheritedPolicies(resourceId, newParentId)
        
        // 递归更新所有子节点的权限
        refreshChildrenPolicies(resourceId)
    }

    /**
     * 刷新继承的权限策略
     */
    @Transactional
    fun refreshInheritedPolicies(resourceId: String, parentResourceId: String?) {
        val acl = fetchACLByResourceId(resourceId)
        
        // 删除所有继承的权限（DRILL_DOWN 和 DRILL_UP）
        deleteInheritedPolicies(resourceId)
        
        // 重新继承父级权限
        val newInheritedPolicies = inheritParentPolicies(acl.id, parentResourceId)
        
        if (newInheritedPolicies.isNotEmpty()) {
            savePolicies(newInheritedPolicies)
        }
        
        // 发布权限更新事件
        publishPolicyUpdateEvent(listOf(resourceId), acl.resourceType, acl.resourceName)
    }

    /**
     * 递归刷新所有子节点的权限
     */
    @Transactional
    fun refreshChildrenPolicies(parentResourceId: String) {
        val childrenACLs = fetchChildrenACLs(parentResourceId)
        
        childrenACLs.forEach { childACL ->
            refreshInheritedPolicies(childACL.resourceId, parentResourceId)
            // 递归处理子节点的子节点
            refreshChildrenPolicies(childACL.resourceId)
        }
    }

    /**
     * 批量授权操作
     */
    @Transactional
    fun batchAuthorization(authorizationRequests: List<ACLAuthorizationRequest>) {
        val changedACLs = mutableSetOf<ACL>()
        val policiesToAdd = mutableListOf<Policy>()
        val policiesToDelete = mutableListOf<String>()

        authorizationRequests.forEach { request ->
            val acl = fetchACLByResourceId(request.resourceId)
            val currentPolicies = fetchRootPolicies(request.resourceId)
            val targetPolicies = request.policies.map { it.toEntity() }
            
            val (deleted, added) = PolicyTool.diff(currentPolicies, targetPolicies)
            
            if (deleted.isNotEmpty() || added.isNotEmpty()) {
                changedACLs.add(acl)
                
                // 处理新增权限
                added.forEach { policy ->
                    val policyWithACL = policy.copy { aclId = acl.id }
                    policiesToAdd.add(policyWithACL)
                    
                    // 向上传播权限
                    acl.flattenParent().forEach { parent ->
                        changedACLs.add(parent)
                        policiesToAdd.add(policyWithACL.copy {
                            aclId = parent.id
                            strategyType = StrategyType.DRILL_UP
                        })
                    }
                    
                    // 向下传播权限
                    acl.flattenChildren().forEach { child ->
                        changedACLs.add(child)
                        policiesToAdd.add(policyWithACL.copy {
                            aclId = child.id
                            strategyType = StrategyType.DRILL_DOWN
                        })
                    }
                }
                
                // 处理删除权限
                policiesToDelete.addAll(deleted.map { it.id })
            }
        }

        // 批量执行数据库操作
        if (policiesToAdd.isNotEmpty()) {
            savePolicies(policiesToAdd)
        }
        
        if (policiesToDelete.isNotEmpty()) {
            deleteRefPolicies(policiesToDelete)
        }

        // 批量发布权限更新事件
        publishBatchPolicyUpdateEvents(changedACLs.toList())
    }

    /**
     * 继承父级权限
     */
    private fun inheritParentPolicies(aclId: String, parentResourceId: String?): List<Policy> {
        return parentResourceId?.let { parentId ->
            val parentPolicies = fetchRootPolicies(parentId)
            PolicyTool.refPolicy(aclId, StrategyType.DRILL_DOWN, parentPolicies)
        } ?: emptyList()
    }

    /**
     * 删除继承的权限策略
     */
    private fun deleteInheritedPolicies(resourceId: String) {
        kSqlClient.createDelete(Policy::class) {
            where(table.acl.resourceId eq resourceId)
            where(table.strategyType valueIn listOf(StrategyType.DRILL_DOWN, StrategyType.DRILL_UP))
        }.execute()
    }

    /**
     * 获取根权限策略（非继承的）
     */
    private fun fetchRootPolicies(resourceId: String): List<Policy> {
        return kSqlClient.createQuery(Policy::class) {
            where(table.acl.resourceId eq resourceId)
            where(table.strategyType eq StrategyType.ROOT)
            select(table.fetchBy { allTableFields() })
        }.execute()
    }

    /**
     * 获取子级ACL列表
     */
    private fun fetchChildrenACLs(parentResourceId: String): List<ACL> {
        return kSqlClient.createQuery(ACL::class) {
            where(table.parent.resourceId eq parentResourceId)
            select(table.fetchBy {
                resourceId()
                resourceType()
                resourceName()
            })
        }.execute()
    }

    /**
     * 根据资源ID获取ACL
     */
    private fun fetchACLByResourceId(resourceId: String): ACL {
        return kSqlClient.createQuery(ACL::class) {
            where(table.resourceId eq resourceId)
            select(table.fetchBy {
                resourceId()
                resourceType()
                resourceName()
                `parent*`()
                `children*`()
            })
        }.fetchOneOrNull() ?: throw IllegalArgumentException("ACL not found for resource: $resourceId")
    }

    /**
     * 保存ACL
     */
    private fun saveACL(acl: ACL): ACL {
        return kSqlClient.entities.save(acl).modifiedEntity
    }

    /**
     * 批量保存权限策略
     */
    private fun savePolicies(policies: List<Policy>) {
        if (policies.isNotEmpty()) {
            kSqlClient.entities.saveEntities(policies)
        }
    }

    /**
     * 删除引用权限
     */
    private fun deleteRefPolicies(policyIds: List<String>) {
        if (policyIds.isNotEmpty()) {
            kSqlClient.createDelete(Policy::class) {
                where(table.id valueIn policyIds)
                // 同时删除引用这些权限的策略
                where(table.refPolicy.id valueIn policyIds)
            }.execute()
        }
    }

    /**
     * 发布权限更新事件
     */
    private fun publishPolicyUpdateEvent(resourceIds: List<String>, resourceType: String, resourceName: String) {
        applicationEventPublisher.publishEvent(
            ResourcePolicyUpdate(resourceIds, resourceType, resourceName)
        )
    }

    /**
     * 批量发布权限更新事件
     */
    private fun publishBatchPolicyUpdateEvents(acls: List<ACL>) {
        acls.groupBy { it.resourceType }.forEach { (_, aclGroup) ->
            publishPolicyUpdateEvent(
                aclGroup.map { it.resourceId },
                aclGroup.first().resourceType,
                aclGroup.first().resourceName
            )
        }
    }
}

/**
 * ACL授权请求数据类
 */
data class ACLAuthorizationRequest(
    val resourceId: String,
    val policies: List<PolicySave>
)

/**
 * 权限策略保存数据类
 */
data class PolicySave(
    val policyType: zone.loong.cube.framework.data.kernel.model.PolicyType,
    val value: String,
    val ruleId: String? = null
) {
    fun toEntity(): Policy = Policy {
        this.policyType = <EMAIL>
        this.value = <EMAIL>
        this.strategyType = StrategyType.ROOT
        this.ruleId = <EMAIL>
    }
}
