package zone.loong.cube.framework.http.forest.configure

import com.dtflys.forest.converter.json.ForestJacksonConverter
import com.dtflys.forest.converter.json.ForestJsonConverter
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import


@Configuration
@Import(JacksonAutoConfiguration::class)
class ForestConfigure {
    @Bean
    fun forestJacksonConverter(objectMapper: ObjectMapper): ForestJsonConverter {
        // 注入 SpringBoot 上下文中的 ObjectMapper 对象
        return ForestJacksonConverter(objectMapper)
    }
}