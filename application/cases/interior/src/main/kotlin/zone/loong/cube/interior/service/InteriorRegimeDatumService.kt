package zone.loong.cube.interior.service

import zone.loong.cube.interior.model.InteriorRegimeDatum
import zone.loong.cube.interior.model.dto.InteriorRegimeDatumSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorRegimeDatum
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

interface InteriorRegimeDatumService {
    fun fetchRegimeDatum(pageQuery: PageQuery<InteriorRegimeDatumSpecification>): Page<InteriorRegimeDatum>
    fun save(saveInteriorRegimeDatum: SaveInteriorRegimeDatum): InteriorRegimeDatum
    fun delete(id: String)
    fun detail(id: String): InteriorRegimeDatum
}