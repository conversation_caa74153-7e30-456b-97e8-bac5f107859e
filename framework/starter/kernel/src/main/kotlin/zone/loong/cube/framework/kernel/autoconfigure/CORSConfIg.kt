package zone.loong.cube.framework.kernel.autoconfigure

import org.springframework.stereotype.Component
import org.springframework.web.servlet.config.annotation.CorsRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer

@Component
class CORSConfIg : WebMvcConfigurer {
    override fun addCorsMappings(registry: CorsRegistry) {
        registry.addMapping("/**") //对那些请求路径有效
            .allowedOriginPatterns("*")
            .allowedHeaders("*")
            .allowCredentials(true)
            .allowedMethods("GET", "HEAD", "POST", "PUT", "DELETE", "OPTIONS")
    }
}
