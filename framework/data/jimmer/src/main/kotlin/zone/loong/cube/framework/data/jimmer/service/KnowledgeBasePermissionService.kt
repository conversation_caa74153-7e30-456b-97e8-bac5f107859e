package zone.loong.cube.framework.data.jimmer.service

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.model.*
import zone.loong.cube.framework.data.kernel.model.StrategyType
import zone.loong.cube.framework.data.kernel.model.PolicyType
import zone.loong.cube.framework.data.jimmer.PolicyTool
import org.slf4j.LoggerFactory
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import com.github.f4b6a3.ulid.UlidCreator

/**
 * 知识库权限服务
 * 实现知识库分类和案例之间的权限传递
 */
@Service
class KnowledgeBasePermissionService(
    private val kSqlClient: KSqlClient,
    private val applicationEventPublisher: ApplicationEventPublisher
) {
    
    private val logger = LoggerFactory.getLogger(KnowledgeBasePermissionService::class.java)
    
    /**
     * 场景1：给分类授权，向下传递到所有案例
     */
    @Transactional
    fun grantCategoryPermission(
        categoryId: String,
        userId: String,
        permission: String
    ) {
        logger.info("给分类 {} 授予用户 {} 权限 {}", categoryId, userId, permission)
        
        // 1. 创建分类的直接权限
        val categoryPolicy = createDirectPolicy(categoryId, userId, permission)
        val savedCategoryPolicy = kSqlClient.entities.save(categoryPolicy).modifiedEntity
        
        // 2. 向下传递到所有案例
        val cases = getCasesByCategory(categoryId)
        cases.forEach { caseId ->
            val casePolicy = Policy {
                acl { resourceId = caseId }
                policyType = PolicyType.USER
                value = userId
                strategyType = StrategyType.DRILL_DOWN
                ruleId = permission
                refPolicy = savedCategoryPolicy
            }
            
            kSqlClient.entities.save(casePolicy)
            logger.debug("向案例 {} 传递权限 {}", caseId, permission)
        }
        
        logger.info("分类权限传递完成，影响 {} 个案例", cases.size)
    }
    
    /**
     * 场景2：给案例授权，向上传递可见权限到分类
     */
    @Transactional
    fun grantCasePermission(
        caseId: String,
        userId: String,
        permission: String
    ) {
        logger.info("给案例 {} 授予用户 {} 权限 {}", caseId, userId, permission)
        
        // 1. 创建案例的直接权限
        val casePolicy = createDirectPolicy(caseId, userId, permission)
        val savedCasePolicy = kSqlClient.entities.save(casePolicy).modifiedEntity
        
        // 2. 向上传递可见权限到分类
        val categoryId = getCategoryByCase(caseId)
        if (categoryId != null) {
            // 检查是否已经有更高级别的权限
            val existingCategoryPermission = getExistingPermission(categoryId, userId)
            
            if (existingCategoryPermission == null || shouldUpgradeToVisible(existingCategoryPermission)) {
                val categoryVisiblePolicy = Policy {
                    acl { resourceId = categoryId }
                    policyType = PolicyType.USER
                    value = userId
                    strategyType = StrategyType.DRILL_UP
                    ruleId = "VISIBLE"  // 关键：向上只传递可见权限
                    refPolicy = savedCasePolicy
                }
                
                kSqlClient.entities.save(categoryVisiblePolicy)
                logger.debug("向分类 {} 传递可见权限", categoryId)
            }
        }
        
        logger.info("案例权限传递完成")
    }
    
    /**
     * 检查用户权限
     */
    fun hasPermission(userId: String, resourceId: String, requiredPermission: String): Boolean {
        val hasPermission = kSqlClient.createQuery(Policy::class) {
            where(table.acl.resourceId eq resourceId)
            where(table.policyType eq PolicyType.USER)
            where(table.value eq userId)
            where(table.ruleId eq requiredPermission)
            select(table.id)
        }.fetchOneOrNull() != null
        
        logger.debug("权限检查: 用户 {} 对资源 {} 的 {} 权限 = {}", 
            userId, resourceId, requiredPermission, hasPermission)
        
        return hasPermission
    }
    
    /**
     * 获取用户在知识库中的所有权限
     */
    fun getUserKnowledgePermissions(userId: String): List<UserPermissionView> {
        return kSqlClient.createQuery(Policy::class) {
            where(table.policyType eq PolicyType.USER)
            where(table.value eq userId)
            where(table.acl.resourceType valueIn listOf("KNOWLEDGE_CATEGORY", "KNOWLEDGE_CASE"))
            
            select(table.fetchBy {
                id()
                ruleId()
                strategyType()
                acl {
                    resourceId()
                    resourceType()
                    resourceName()
                }
                refPolicy {
                    id()
                }
            })
        }.execute().map { policy ->
            UserPermissionView(
                resourceId = policy.acl.resourceId,
                resourceType = policy.acl.resourceType,
                resourceName = policy.acl.resourceName,
                permission = policy.ruleId ?: "",
                source = when (policy.strategyType) {
                    StrategyType.ROOT -> "直接授权"
                    StrategyType.DRILL_DOWN -> "从分类继承"
                    StrategyType.DRILL_UP -> "从案例传递"
                    else -> "未知"
                },
                isInherited = policy.refPolicy != null
            )
        }
    }
    
    /**
     * 撤销权限（包括传递的权限）
     */
    @Transactional
    fun revokePermission(resourceId: String, userId: String, permission: String) {
        logger.info("撤销权限: 用户 {} 对资源 {} 的 {} 权限", userId, resourceId, permission)
        
        // 1. 找到要撤销的根权限
        val rootPolicy = kSqlClient.createQuery(Policy::class) {
            where(table.acl.resourceId eq resourceId)
            where(table.policyType eq PolicyType.USER)
            where(table.value eq userId)
            where(table.ruleId eq permission)
            where(table.strategyType eq StrategyType.ROOT)
            select(table)
        }.fetchOneOrNull()
        
        if (rootPolicy != null) {
            // 2. 删除所有引用这个权限的传递权限
            val deletedCount = kSqlClient.createDelete(Policy::class) {
                where(table.refPolicy.id eq rootPolicy.id)
            }.execute()
            
            // 3. 删除根权限
            kSqlClient.entities.delete(Policy::class, rootPolicy.id)
            
            logger.info("权限撤销完成，删除了 {} 个传递权限", deletedCount)
        }
    }
    
    // 辅助方法
    private fun createDirectPolicy(resourceId: String, userId: String, permission: String): Policy {
        return Policy {
            id = UlidCreator.getUlid().toString()
            acl { resourceId = resourceId }
            policyType = PolicyType.USER
            value = userId
            strategyType = StrategyType.ROOT
            ruleId = permission
        }
    }
    
    private fun getCasesByCategory(categoryId: String): List<String> {
        return kSqlClient.createQuery(ACL::class) {
            where(table.parent.resourceId eq categoryId)
            where(table.resourceType eq "KNOWLEDGE_CASE")
            select(table.resourceId)
        }.execute()
    }
    
    private fun getCategoryByCase(caseId: String): String? {
        return kSqlClient.createQuery(ACL::class) {
            where(table.resourceId eq caseId)
            select(table.parent.resourceId)
        }.fetchOneOrNull()
    }
    
    private fun getExistingPermission(resourceId: String, userId: String): Policy? {
        return kSqlClient.createQuery(Policy::class) {
            where(table.acl.resourceId eq resourceId)
            where(table.policyType eq PolicyType.USER)
            where(table.value eq userId)
            select(table)
        }.fetchOneOrNull()
    }
    
    private fun shouldUpgradeToVisible(existingPolicy: Policy): Boolean {
        // 如果已经有READ或更高权限，就不需要添加VISIBLE权限
        return existingPolicy.ruleId == null || 
               existingPolicy.ruleId == "VISIBLE" ||
               existingPolicy.strategyType == StrategyType.DRILL_UP
    }
}

/**
 * 用户权限视图
 */
data class UserPermissionView(
    val resourceId: String,
    val resourceType: String,
    val resourceName: String,
    val permission: String,
    val source: String,
    val isInherited: Boolean
)
