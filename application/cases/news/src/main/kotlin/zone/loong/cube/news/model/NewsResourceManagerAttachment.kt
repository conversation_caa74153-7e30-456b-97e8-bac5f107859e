package zone.loong.cube.news.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.IDAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail

@Entity
@Table(name = "news_resource_manager_attachment")
interface NewsResourceManagerAttachment : IDAware {
    @OneToOne
    @Key
    val file: FileDetail

    @ManyToOne
    @Key
    @OnDissociate(DissociateAction.DELETE)
    @JoinColumn(name = "news_id")
    val news: NewsResourceManager?

}