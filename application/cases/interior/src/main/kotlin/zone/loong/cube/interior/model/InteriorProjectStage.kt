package zone.loong.cube.interior.model

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.Id
import org.babyfish.jimmer.sql.GeneratedValue
import org.babyfish.jimmer.sql.GenerationType
import org.babyfish.jimmer.sql.Key
import org.babyfish.jimmer.sql.LogicalDeleted
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import java.time.LocalDateTime

/**
 * 项目阶段
 */
@Entity
interface InteriorProjectStage : AuditAware {

    /**
     * 唯一标识
     */
    @Key
    val code: String

    /**
     * 名称
     */
    val title: String?

    /**
     * 名称
     */
    val sequence: Int

    /**
     * 状态
     */
    val status: String?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}

