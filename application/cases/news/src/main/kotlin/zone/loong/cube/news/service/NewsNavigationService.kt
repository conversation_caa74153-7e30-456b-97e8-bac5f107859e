package zone.loong.cube.news.service

import zone.loong.cube.news.model.NewsNavigation
import zone.loong.cube.news.model.dto.NewsNavigationSpecification
import zone.loong.cube.news.model.dto.SaveNewsNavigation
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery


interface NewsNavigationService {

    fun fetchNavigations(pageQuery: PageQuery<NewsNavigationSpecification>): Page<NewsNavigation>
    fun fetchNavigations(): List<NewsNavigation>
    fun save(saveNewsNavigation : SaveNewsNavigation) : NewsNavigation
    fun delete(id : String)
    fun detail(id : String) : NewsNavigation
    fun fetchNavigationTree(): List<NewsNavigation>

}