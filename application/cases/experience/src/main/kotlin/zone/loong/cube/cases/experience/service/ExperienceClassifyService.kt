package zone.loong.cube.cases.experience.service

import zone.loong.cube.cases.experience.model.ExperienceClassify
import zone.loong.cube.cases.experience.model.dto.ACLClassify
import zone.loong.cube.cases.experience.model.dto.ClassifySpecification
import zone.loong.cube.cases.experience.model.dto.SaveClassify
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.model.Tree

interface ExperienceClassifyService {
    fun fetchClassify(pid: String?): List<ExperienceClassify>
    fun fetchClassify(pageQuery: PageQuery<ClassifySpecification>): Page<ExperienceClassify>
    fun filterClassify(keyword: String): List<Tree>
    fun detail(id: String): ExperienceClassify
    fun delete(id: String)
    fun save(saveClassify: SaveClassify): ExperienceClassify

}