package zone.loong.cube.interior.service

import jakarta.servlet.http.HttpServletResponse
import zone.loong.cube.interior.model.InteriorProjectDatum
import zone.loong.cube.interior.model.dto.InteriorProjectDatumSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorProjectDatum
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

interface InteriorProjectDatumService {
    fun fetchProjectDatum(pageQuery: PageQuery<InteriorProjectDatumSpecification>): Page<InteriorProjectDatum>
    fun save(saveInteriorProjectDatum: SaveInteriorProjectDatum): InteriorProjectDatum
    fun delete(id: String)
    fun detail(id: String): InteriorProjectDatum
    fun download(id: String, response: HttpServletResponse)
}