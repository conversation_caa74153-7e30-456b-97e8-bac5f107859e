package zone.loong.cube.framework.storage.kernel.service

import cn.hutool.core.net.multipart.UploadFile
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import org.dromara.x.file.storage.core.Downloader
import org.dromara.x.file.storage.core.FileInfo
import org.dromara.x.file.storage.core.FileStorageService
import org.dromara.x.file.storage.core.constant.Constant
import org.dromara.x.file.storage.core.copy.CopyPretreatment
import org.dromara.x.file.storage.core.file.FileWrapper
import org.dromara.x.file.storage.core.get.GetFilePretreatment
import org.dromara.x.file.storage.core.get.ListFilesPretreatment
import org.dromara.x.file.storage.core.get.ListFilesSupportInfo
import org.dromara.x.file.storage.core.get.RemoteFileInfo
import org.dromara.x.file.storage.core.move.MovePretreatment
import org.dromara.x.file.storage.core.presigned.GeneratePresignedUrlPretreatment
import org.dromara.x.file.storage.core.upload.*
import org.springframework.web.multipart.MultipartFile
import java.io.InputStream
import java.net.URL
import java.net.URLEncoder
import java.util.*

interface StorageService {
    val platform: String
    val fileStorageService: FileStorageService
    val fileRecorder: FileRecorder

    private fun of(path: String, file: Any): UploadPretreatment {
        return fileStorageService.of(file).setPath(path).setPlatform(platform)
    }
    /**
     * 上传
     */
    fun upload(path: String, file: MultipartFile): FileInfo {
        return of(path, file)
            .upload()
    }

    /**
     * 上传
     */
    fun upload(path: String, file: UploadFile): FileInfo {
        return of(path, file)
            .upload()
    }

    /**
     * 上传
     */
    fun upload(path: String, file: Array<Byte>): FileInfo {
        return of(path, file)
            .upload()
    }

    /**
     * 上传
     */
    fun upload(path: String, file: InputStream): FileInfo {
        return of(path, file)
            .upload()
    }

    /**
     * 上传
     */
    fun upload(path: String, file: URL): FileInfo {
        return of(path, file)
            .upload()
    }

    /**
     * 上传
     */
    fun upload(path: String, file: String): FileInfo {
        return of(path, file)
            .upload()
    }

    /**
     * 上传
     */
    fun upload(path: String, file: HttpServletRequest): FileInfo {
        return of(path, file)
            .upload()
    }

    /**
     * 文件是否存在
     */
    fun exists(fileInfo: FileInfo): Boolean {
        return fileStorageService.exists(fileInfo)
    }

    /**
     * 文件是否存在
     */
    fun exists(url: String): Boolean {
        return fileStorageService.exists(url)
    }

    /**
     * 文件是否存在
     */
    fun delete(fileInfo: FileInfo): Boolean {
        return fileStorageService.delete(fileInfo)
    }

    /**
     * 删除
     */
    fun delete(url: String): Boolean {
        return fileStorageService.delete(url)
    }

    /**
     * 下载
     */
    fun download(url: String): Downloader {
        return fileStorageService.download(url)
    }

    fun download(url: String, response: HttpServletResponse) {
        val fileInfo = fileStorageService.getFileInfoByUrl(url)
        fileStorageService.download(fileInfo).toResponse(fileInfo, response)
    }

    /**
     * 下载
     */
    fun download(fileInfo: FileInfo): Downloader {
        return fileStorageService.download(fileInfo)
    }

    fun download(fileInfo: FileInfo, response: HttpServletResponse) {
        fileStorageService.download(fileInfo).toResponse(fileInfo, response)
    }

    /**
     * 下载
     */
    fun downloadTh(url: String): Downloader {
        return fileStorageService.downloadTh(url)
    }

    /**
     * 下载
     */
    fun downloadTh(fileInfo: FileInfo): Downloader {
        return fileStorageService.downloadTh(fileInfo)
    }

    /**
     * 下载
     */
    fun isSupportPresignedUrl(): Boolean {
        return fileStorageService.isSupportPresignedUrl(platform)
    }

    /**
     * 生成预签名 URL
     * @return 生成预签名 URL 预处理器
     */
    fun generatePresignedUrl(): GeneratePresignedUrlPretreatment {
        return GeneratePresignedUrlPretreatment()
            .setFileStorageService(fileStorageService)
            .setPlatform(platform)
    }

    /**
     * 对文件生成可以签名访问的 URL，无法生成则返回 null
     *
     * @param expiration 到期时间
     */
    fun generatePresignedUrl(fileInfo: FileInfo, expiration: Date): String? {
        val result = generatePresignedUrl()
            .setExpiration(expiration)
            .setPath(fileInfo.path)
            .setFilename(fileInfo.filename)
            .setMethod(Constant.GeneratePresignedUrl.Method.GET)
            .generatePresignedUrl()
        return result?.url
    }

    /**
     * 对缩略图文件生成可以签名访问的 URL，无法生成则返回 null
     *
     * @param expiration 到期时间
     */
    fun generateThPresignedUrl(fileInfo: FileInfo, expiration: Date): String? {
        val result = generatePresignedUrl()
            .setExpiration(expiration)
            .setPath(fileInfo.path)
            .setFilename(fileInfo.thFilename)
            .setMethod(Constant.GeneratePresignedUrl.Method.GET)
            .generatePresignedUrl()
        return result?.url
    }

    /**
     * 是否支持对文件的访问控制列表
     */
    fun isSupportAcl(): Boolean {
        return fileStorageService.isSupportAcl(platform)
    }

    /**
     * 设置文件的访问控制列表，一般情况下只有对象存储支持该功能
     * 详情见[FileInfo.setFileAcl]
     */
    fun setFileAcl(fileInfo: FileInfo, acl: Any): Boolean {
        return fileStorageService.setFileAcl(fileInfo, acl)
    }

    /**
     * 设置缩略图文件的访问控制列表，一般情况下只有对象存储支持该功能
     * 详情见[FileInfo.setFileAcl]
     */
    fun setThFileAcl(fileInfo: FileInfo, acl: Any): Boolean {
        return fileStorageService.setThFileAcl(fileInfo, acl)
    }

    /**
     * 是否支持 Metadata
     */
    fun isSupportMetadata(): Boolean {
        return fileStorageService.isSupportMetadata(platform)
    }


    /**
     * 默认使用的存储平台是否支持手动分片上传
     */
    fun isSupportMultipartUpload(): MultipartUploadSupportInfo {
        return fileStorageService.isSupportMultipartUpload(platform)
    }

    /**
     * 手动分片上传-初始化
     */
    fun initiateMultipartUpload(): InitiateMultipartUploadPretreatment {
        val pre = InitiateMultipartUploadPretreatment()
        pre.setFileStorageService(fileStorageService)
        pre.setPlatform(platform)
        return pre
    }

    /**
     * 手动分片上传-上传分片
     * @param fileInfo 文件信息
     * @param partNumber 分片号。每一个上传的分片都有一个分片号，一般情况下取值范围是1~10000
     * @param source      源
     * @return 手动分片上传-上传分片预处理器
     */
    fun uploadPart(fileInfo: FileInfo?, partNumber: Int, source: Any?): UploadPartPretreatment {
        return fileStorageService.uploadPart(fileInfo, partNumber, source, null)
    }

    /**
     * 手动分片上传-上传分片
     * @param fileInfo 文件信息
     * @param partNumber 分片号。每一个上传的分片都有一个分片号，一般情况下取值范围是1~10000
     * @param source      源
     * @param size        源的文件大小
     * @return 手动分片上传-上传分片预处理器
     */
    fun uploadPart(fileInfo: FileInfo?, partNumber: Int, source: Any?, size: Long?): UploadPartPretreatment {
        val pre = UploadPartPretreatment()
        pre.setFileStorageService(fileStorageService)
        pre.setFileInfo(fileInfo)
        pre.setPartNumber(partNumber)
        // 这是是个优化，如果是 FileWrapper 对象就直接使用，否则就创建 FileWrapper 并指定 contentType 避免自动识别造成性能浪费
        if (source is FileWrapper) {
            pre.setPartFileWrapper(source)
        } else {
            pre.setPartFileWrapper(fileStorageService.wrapper(source, null, "application/octet-stream", size))
        }
        return pre
    }

    /**
     * 手动分片上传-完成
     * @param fileInfo 文件信息，如果在初始化时传入了 ACL 访问控制列表、Metadata 元数据等信息，
     * 一定要保证这里的 fileInfo 也有相同的信息，否则有些存储平台会不生效，
     * 这是因为每个存储平台的逻辑不一样，有些是初始化时传入的，有些是完成时传入的，
     * 建议将 FileInfo 保存到数据库中，这样就可以使用 fileStorageService.getFileInfoByUrl("https://abc.def.com/xxx.png")
     * 来获取 FileInfo 方便操作，详情请阅读 https://x-file-storage.xuyanwu.cn/2.2.0/#/%E5%9F%BA%E7%A1%80%E5%8A%9F%E8%83%BD?id=%E4%BF%9D%E5%AD%98%E4%B8%8A%E4%BC%A0%E8%AE%B0%E5%BD%95
     */
    fun completeMultipartUpload(fileInfo: FileInfo?): CompleteMultipartUploadPretreatment {
        val pre = CompleteMultipartUploadPretreatment()
        pre.setFileStorageService(fileStorageService)
        pre.setFileInfo(fileInfo)
        return pre
    }

    /**
     * 手动分片上传-取消
     */
    fun abortMultipartUpload(fileInfo: FileInfo?): AbortMultipartUploadPretreatment {
        val pre = AbortMultipartUploadPretreatment()
        pre.setFileStorageService(fileStorageService)
        pre.setFileInfo(fileInfo)
        return pre
    }

    /**
     * 手动分片上传-列举已上传的分片
     */
    fun listParts(fileInfo: FileInfo?): ListPartsPretreatment {
        val pre = ListPartsPretreatment()
        pre.setFileStorageService(fileStorageService)
        pre.setFileInfo(fileInfo)
        return pre
    }

    /**
     * 使用的存储平台是否支持列举文件
     */
    fun isSupportListFiles(): ListFilesSupportInfo {
        return fileStorageService.isSupportListFiles(platform)
    }

    /**
     * 列举文件
     */
    fun listFiles(): ListFilesPretreatment {
        val pre = ListFilesPretreatment()
        pre.setPlatform(platform)
        pre.setFileStorageService(fileStorageService)
        return pre
    }

    /**
     * 获取文件
     */
    fun getFile(): GetFilePretreatment {
        return GetFilePretreatment()
            .setPlatform(platform)
            .setFileStorageService(fileStorageService)
    }

    /**
     * 获取文件
     * @param fileInfo 文件信息
     * @return 远程文件信息
     */
    fun getFile(fileInfo: FileInfo): RemoteFileInfo {
        return getFile()
            .setPlatform(fileInfo.platform)
            .setPath(fileInfo.path != null, fileInfo.path)
            .setFilename(fileInfo.filename != null, fileInfo.filename)
            .setUrl(fileInfo.url != null, fileInfo.url)
            .file
    }

    /**
     * 获取缩略图文件
     * @param fileInfo 文件信息
     * @return 远程文件信息
     */
    fun getThFile(fileInfo: FileInfo): RemoteFileInfo {
        return getFile()
            .setPlatform(fileInfo.platform)
            .setPath(fileInfo.path != null, fileInfo.path)
            .setFilename(fileInfo.thFilename != null, fileInfo.thFilename)
            .setUrl(fileInfo.thUrl != null, fileInfo.thUrl)
            .file
    }


    /**
     * 是否支持同存储平台复制文件
     */
    fun isSupportSameCopy(): Boolean {
        return fileStorageService.isSupportSameCopy(platform)
    }

    /**
     * 复制文件
     */
    fun copy(fileInfo: FileInfo): CopyPretreatment {
        return CopyPretreatment(fileInfo, fileStorageService)
    }

    /**
     * 复制文件
     */
    fun copy(url: String): CopyPretreatment {
        return fileStorageService.copy(fileStorageService.getFileInfoByUrl(url))
    }

    /**
     * 是否支持同存储平台移动文件
     */
    fun isSupportSameMove(): Boolean {
        return fileStorageService.isSupportSameMove(platform)
    }

    /**
     * 移动文件
     */
    fun move(fileInfo: FileInfo): MovePretreatment {
        return MovePretreatment(fileInfo, fileStorageService)
    }

    /**
     * 移动文件
     */
    fun move(url: String): MovePretreatment {
        return fileStorageService.move(fileStorageService.getFileInfoByUrl(url))
    }


}

fun Downloader.toResponse(fileInfo: FileInfo, response: HttpServletResponse) {
    response.contentType = "application/octet-stream"
    val filename = URLEncoder.encode(
        fileInfo.originalFilename ?: fileInfo.originalFilename ?: fileInfo.filename,
        Charsets.UTF_8.name()
    )
    response.setHeader(
        "Content-Disposition",
        "attachment; filename=\"${filename}\""
    )
    outputStream(response.outputStream)
}