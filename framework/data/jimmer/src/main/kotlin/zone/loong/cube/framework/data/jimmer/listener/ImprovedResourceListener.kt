package zone.loong.cube.framework.data.jimmer.listener

import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.event.ResourceCreated
import zone.loong.cube.framework.data.jimmer.event.ResourceParentChanged
import zone.loong.cube.framework.data.jimmer.event.ResourceDeleted
import zone.loong.cube.framework.data.jimmer.event.ResourcePolicyUpdate
import zone.loong.cube.framework.data.jimmer.service.ACLManagementService
import org.slf4j.LoggerFactory

/**
 * 改进的资源监听器
 * 正确处理ACL的生命周期管理
 *
 * 职责：
 * 1. 处理资源创建事件，初始化ACL
 * 2. 处理父级变更事件，更新权限继承
 * 3. 处理资源删除事件，清理ACL数据
 */
@Component
class ImprovedResourceListener(
    private val aclManagementService: ACLManagementService,
    private val applicationEventPublisher: ApplicationEventPublisher
) {

    private val logger = LoggerFactory.getLogger(ImprovedResourceListener::class.java)

    /**
     * 处理资源创建事件
     * 在这里进行ACL的实际初始化
     */
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onResourceCreated(event: ResourceCreated) {
        logger.debug("处理资源创建事件: {}", event.resourceId)

        try {
            // 创建ACL记录
            val acl = aclManagementService.createACLRecord(
                aclId = event.aclId,
                resourceId = event.resourceId,
                resourceType = event.resourceType,
                resourceName = event.resourceName,
                parentResourceId = event.parentResourceId
            )

            // 发布权限更新事件
            applicationEventPublisher.publishEvent(
                ResourcePolicyUpdate(
                    resourceId = listOf(acl.resourceId),
                    resourceType = acl.resourceType,
                    resourceName = acl.resourceName
                )
            )

            logger.debug("ACL初始化完成: {}", event.resourceId)
        } catch (e: Exception) {
            logger.error("ACL初始化失败: {}", event.resourceId, e)
            throw e
        }
    }

    /**
     * 处理资源父级变更事件
     */
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onResourceParentChanged(event: ResourceParentChanged) {
        logger.debug("处理父级变更事件: {} {} -> {}",
            event.resourceId, event.oldParentId, event.newParentId)

        try {
            aclManagementService.handleParentMigration(
                event.resourceId,
                event.oldParentId,
                event.newParentId
            )

            logger.debug("父级变更处理完成: {}", event.resourceId)
        } catch (e: Exception) {
            logger.error("父级变更处理失败: {}", event.resourceId, e)
            throw e
        }
    }

    /**
     * 处理资源删除事件
     */
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onResourceDeleted(event: ResourceDeleted) {
        logger.debug("处理资源删除事件: {}", event.resourceId)

        try {
            aclManagementService.cleanupACL(event.resourceId)
            logger.debug("ACL清理完成: {}", event.resourceId)
        } catch (e: Exception) {
            logger.error("ACL清理失败: {}", event.resourceId, e)
            throw e
        }
    }
}
