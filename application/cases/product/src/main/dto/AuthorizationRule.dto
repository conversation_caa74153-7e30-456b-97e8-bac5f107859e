export zone.loong.cube.cases.product.model.AuthorizationRule
    -> package zone.loong.cube.cases.product.model.dto

specification AuthorizationRuleSpecification{
    like(descriptions)
}


input AuthorizationRuleSave{
    id?
    terms
    expression
    descriptions
    groups{
        id
    }
}
AuthorizationRuleView{
    id
    terms
    expression
    descriptions
    groups{
        id
        code
        name
    }
}