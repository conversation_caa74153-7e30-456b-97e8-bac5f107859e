import pub.ihub.plugin.IHubSettingsExtension

plugins {
    id("pub.ihub.plugin.ihub-settings") version "1.7.7"
}
configure<IHubSettingsExtension> {
    includeProjects("platform").prefix("cube-application-")
    includeProjects("cases").onlySubproject.prefix("cube-application-cases-")
    includeProjects("server").onlySubproject.prefix("cube-application-server-")
//    includeProjects("cases","platform", "server").subproject
}

