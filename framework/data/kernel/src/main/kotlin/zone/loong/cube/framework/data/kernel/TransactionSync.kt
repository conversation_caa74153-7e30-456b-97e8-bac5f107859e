package zone.loong.cube.framework.data.kernel
import org.slf4j.LoggerFactory
import org.springframework.transaction.support.TransactionSynchronization
import org.springframework.transaction.support.TransactionSynchronizationManager
import java.util.stream.Collectors

/**
 * Spring 事务同步管理器的包装
 *
 *
 * 因为Spring自身的[TransactionSynchronizationManager]只能增加一个同步任务，而不能完全满足单据模型的复杂事务机制
 *
 *
 *
 *
 * 主要有3中运行情况
 * 1、beforeCommit 在事务提交前执行，如果事务回滚，不执行
 * 2、afterCommit  在事务提交后执行，如果事务回滚，不执行
 * 3、afterCompletion 在事务完成后执行，不论事务提交与回滚，都会执行
 *
 *
 * beforeCommit 与 afterCommit 的队列区别：
 * beforeCommit 的队列中，一旦任务发生异常，则终止后续的任务
 * afterCommit与afterCompletion  的队列中，即使一个任务发生异常，后续的任务仍然执行
 *
 *
 * last：没有last标记的方法，添加的任务按顺序执行，被标记为last的方法，任务倒序执行
 * 即最先添加到last对列里的任务，最末执行，以此类推
 *
 */
@Suppress("unused")
object TransactionSync {
    private val log = LoggerFactory.getLogger(TransactionSync::class.java)
    private val tasksRunAfterCommit = ThreadLocal<MutableList<Handler>>()
    private val tasksRunBeforeCommit = ThreadLocal<MutableList<Handler>>()
    private val tasksRunAfterCompletion = ThreadLocal<MutableList<HandlerCompletion>>()
    private val lock = ThreadLocal<Boolean?>()

    /**
     *
     * 在事务提交前执行函数
     *
     * 如果当前上下文没有事务，则立即执行
     *
     * @param t t
     */
    fun runBeforeCommit(taskDesc: String, t: Function) {
        run(taskDesc, t, System.currentTimeMillis(), tasksRunBeforeCommit)
    }

    /**
     *
     * 在事务提交后执行函数
     *
     * 将函数放置队列中间，在[.runAfterCommitLast]之前
     *
     * 如果当前上下文没有事务，则立即执行
     *
     * @param t t
     */
    fun runAfterCommit(taskDesc: String, t: Function) {
        run(taskDesc, t, System.currentTimeMillis(), tasksRunAfterCommit)
    }

    /**
     *
     * 在事务提交后执行函数
     *
     * 将函数放置队列后执行
     *
     * 如果当前上下文没有事务，则立即执行
     *
     * @param t
     */
    fun runAfterCommitLast(taskDesc: String, t: Function) {
        run(taskDesc, t, Long.MAX_VALUE - System.currentTimeMillis(), tasksRunAfterCommit)
    }


    fun runAfterCompletion(taskDesc: String, t: FunctionCompletion) {
        runAfterCompletion(taskDesc, t, System.currentTimeMillis())
    }

    fun runAfterCompletionLast(taskDesc: String, t: FunctionCompletion) {
        runAfterCompletion(taskDesc, t, Long.MAX_VALUE - System.currentTimeMillis())
    }

    private fun runAfterCompletion(taskDesc: String, function: FunctionCompletion, priority: Long) {
        if (lock.get() != null) {
            log.warn("当前线程事务已提交，任务将立即执行")
            try {
                log.info("* >> 执行： {}", taskDesc)
                function.apply(TransactionSynchronization.STATUS_UNKNOWN)
                log.info("* >> 完成： {}", taskDesc)
            } catch (e: Exception) {
                log.error("* >> 错误： {} Exception: {}", taskDesc, e.message, e)
            }
            return
        }


        // 启动异步线程，在事务提交后，将需要更新solr索引的数据写入同步队列
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            val handler = HandlerCompletion(taskDesc, function, priority)

            var handlers = tasksRunAfterCompletion.get()
            if (handlers == null) {
                handlers = ArrayList()
                tasksRunAfterCompletion.set(handlers)
            }

            handlers.add(handler)

            TransactionSynchronizationManager.registerSynchronization(transactionSync)

            if (log.isInfoEnabled) {
                log.info("* 绑定事务任务, 任务将在事务完成后执行, 当前任务数量 = {}", handlers.size)
                handlers.stream().sorted()
                    .forEach { h: HandlerCompletion -> log.info("* -- {}", h.taskDesc) }
            }
        } else {
            log.warn("* 当前线程无事务管理，任务将立即执行")
            try {
                function.apply(TransactionSynchronization.STATUS_UNKNOWN)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }

    private fun run(
        taskDesc: String,
        function: Function,
        priority: Long,
        targetTaskList: ThreadLocal<MutableList<Handler>>
    ) {
        if (lock.get() != null) throw RuntimeException("服务已经开始执行，不能嵌套使用")


        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            val handler = Handler(taskDesc, function, priority)

            var handlers = targetTaskList.get()
            if (handlers == null) {
                handlers = ArrayList()
                targetTaskList.set(handlers)
            }

            handlers.add(handler)

            TransactionSynchronizationManager.registerSynchronization(transactionSync)

            if (log.isErrorEnabled) {
                if (targetTaskList === tasksRunBeforeCommit) {
                    log.info("* 绑定事务任务, 任务将在事务提交前执行, 当前任务数量 = {}", handlers.size)
                } else {
                    log.info("* 绑定事务任务, 任务将在事务提交后执行, 当前任务数量 = {}", handlers.size)
                }
                handlers.stream().sorted().forEach { h: Handler -> log.info("* -- {}", h.taskDesc) }
            }
        } else {
            log.warn("* 当前线程无事务管理，任务将立即执行")
            try {
                function.apply()
            } catch (e: Exception) {
                throw RuntimeException(e.message, e)
            }
        }
    }

    private val transactionSync: TransactionSynchronization = object : TransactionSynchronization {
        override fun beforeCommit(readOnly: Boolean) {
            super.beforeCommit(readOnly)

            lock()

            val handlers: List<Handler>? = tasksRunBeforeCommit.get()
            if (handlers != null) {
                log.info("* >>>>>>>> 准备执行事务提交前的任务，任务数量 = {}", handlers.size)

                tasksRunBeforeCommit.remove()


                var last: Handler? = null
                try {
                    for (handler in handlers.stream().sorted().collect(Collectors.toList())) {
                        last = handler
                        log.info("* >> 执行： {}", handler.taskDesc)
                        handler.task.apply()
                        log.info("* >> 完成： {}", handler.taskDesc)
                    }
                    log.info("* <<<<<<<< 事务提交前的任务执行完成")
                } catch (e: RuntimeException) {
                    log.error("* >> 错误： {} Exception: {}", last?.taskDesc ?: "unknown", e.message, e)
                    log.info("* <<<<<<<< 事务提交前的任务执行终止")
                    throw e
                }
            }

            unlock()
        }

        override fun afterCommit() {
            lock()

            val handlers: List<Handler>? = tasksRunAfterCommit.get()
            if (handlers != null) {
                log.info("* >>>>>>>> 准备执行事务提交后的任务，任务数量 = {}", handlers.size)

                tasksRunAfterCommit.remove()

                for (handler in handlers.stream().sorted().collect(Collectors.toList())) {
                    try {
                        log.info("* >> 执行： {}", handler.taskDesc)
                        handler.task.apply()
                        log.info("* >> 完成： {}", handler.taskDesc)
                    } catch (e: Exception) {
                        log.error("* >> 错误： {} Exception: {}", handler.taskDesc, e.message, e)
                    }
                }
                log.info("* <<<<<<<< 事务提交后的任务执行完成")
            }

            unlock()
        }

        override fun afterCompletion(status: Int) {
            try {
                lock()

                val handlers: List<HandlerCompletion>? = tasksRunAfterCompletion.get()
                if (handlers != null) {
                    log.info("* >>>>>>>> 准备执行事务完成的任务，任务数量 = {}", handlers.size)

                    tasksRunAfterCompletion.remove()

                    for (handler in handlers.stream().sorted().collect(Collectors.toList())) {
                        try {
                            log.info("* >> 执行： {}", handler.taskDesc)
                            handler.task.apply(status)
                            log.info("* >> 完成： {}", handler.taskDesc)
                        } catch (e: Exception) {
                            log.error("* >> 错误： {} Exception: {}", handler.taskDesc, e.message, e)
                        }
                    }
                    log.info("* <<<<<<<< 事务完成的任务执行完成")
                }
            } finally {
                // 再一次清理线程变量，确保数据不污染
                unlock()
                tasksRunBeforeCommit.remove()
                tasksRunAfterCommit.remove()
                tasksRunAfterCompletion.remove()
            }
        }
    }

    private fun lock() {
        lock.set(true)
    }

    private fun unlock() {
        lock.remove()
    }

    internal class Handler(val taskDesc: String, val task: Function, private val priority: Long) : Comparable<Handler> {
        override fun compareTo(other: Handler): Int {
            return priority.compareTo(other.priority)
        }
    }

    internal class HandlerCompletion(val taskDesc: String, val task: FunctionCompletion, private val priority: Long) :
        Comparable<HandlerCompletion> {
        override fun compareTo(other: HandlerCompletion): Int {
            return priority.compareTo(other.priority)
        }
    }

    fun interface Function {
        @Throws(Exception::class)
        fun apply()
    }

    fun interface FunctionCompletion {
        @Throws(Exception::class)
        fun apply(status: Int)
    }
}