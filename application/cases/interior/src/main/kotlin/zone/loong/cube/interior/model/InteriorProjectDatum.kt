package zone.loong.cube.interior.model

import com.fasterxml.jackson.annotation.JsonValue
import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail

/**
 * 项目数据管理
 */
@Entity
interface InteriorProjectDatum : AuditAware {

    /**
     * 唯一标识
     */
    val code: String?

    /**
     * 名称
     */
    @Key
    val title: String

    /**
     * 标签
     */
    @Serialized
    val tags: List<String>

    /**
     * 类型(文件/文件夹)
     */
    val type: FileType

    /** 文件id */
    @OneToOne
    @JoinColumn(name = "FILE_ID")
    val file: FileDetail?

    /**
     * 阶段id
     */
    @ManyToOne
    val stage: InteriorProjectStage

    /**
     * 项目id
     */
    @ManyToOne
    val project: InteriorProject

    /**
     * 父级id
     */
    @ManyToOne
    @OnDissociate(DissociateAction.SET_NULL)
    val parent: InteriorProjectDatum?

    /**
     * 备注
     */
    val remark: String?

    /**
     * 状态
     */
    val status: String?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}

enum class FileType(@JsonValue val value: String) {
    DIRECTORY("DIRECTORY"),
    FILE("FILE");
}


