package zone.loong.cube.platform.auth.controller

import org.babyfish.jimmer.client.meta.Api
import org.springframework.web.bind.annotation.*
import zone.loong.cube.framework.auth.security.model.Group
import zone.loong.cube.framework.auth.security.model.Permission
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.Role
import zone.loong.cube.platform.auth.model.dto.*
import zone.loong.cube.platform.auth.service.impl.AccountServiceImpl
import zone.loong.cube.platform.organization.model.User

@RestController
@RequestMapping("/account")
@Api
class AccountController(private val accountService: AccountServiceImpl) {
    @Api
    @PostMapping()
    fun fetchAccount(@RequestBody pageQuery: PageQuery<AccountSpecification>): Page<AccountWithUserInfo> {
        return accountService.fetchAccount(pageQuery)
    }

    @GetMapping("/groups/{userId}")
    fun fetchGroups(@PathVariable("userId") userId: String): List<Group> {
        return accountService.fetchGroups(userId)
    }

    @GetMapping("/info")
    fun fetchAccountInfo(): AccountInfo{
        return accountService.fetchAccountInfo()
    }

    @PostMapping("/preferences")
    fun savePreference(@RequestBody accountPreference: AccountPreference){
        accountService.savePreference(accountPreference)
    }

    @GetMapping("/roles/{userId}")
    fun fetchRoles(@PathVariable("userId") userId: String): List<Role> {
        return accountService.fetchRoles(userId)
    }

    @GetMapping("/workspace")
    fun fetchWorkspace(): List<Permission>{
        return accountService.fetchWorkspace();
    }

    @PostMapping("/authorization")
    fun authenticate(@RequestBody accountAuthorization: AccountAuthorization) {
        accountService.authenticate(accountAuthorization)
    }

    @GetMapping("/getUserInfo")
    fun getUserInfo(): User?{
        return accountService.getUserInfo();
    }
}