package zone.loong.cube.news.controller

import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.news.model.NewsNavigation
import zone.loong.cube.news.model.dto.NewsNavigationSpecification
import zone.loong.cube.news.model.dto.SaveNewsNavigation
import zone.loong.cube.news.service.NewsNavigationService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

@RestController
@RequestMapping("/news/navigation")
class NewsNavigationController(
    private val newsNavigationService: NewsNavigationService
) {

    @PostMapping("/page")
    fun fetchNavigations(@RequestBody pageQuery: PageQuery<NewsNavigationSpecification>) : Page<NewsNavigation> {
        return newsNavigationService.fetchNavigations(pageQuery)
    }

    @GetMapping("/tree")
    fun fetchNavigationTree(): List<NewsNavigation> {
        return newsNavigationService.fetchNavigationTree()
    }

    @GetMapping("/getNavigations")
    fun fetchNavigations() : List<NewsNavigation> {
        return newsNavigationService.fetchNavigations()
    }

    @PostMapping("/save")
    fun save(@RequestBody saveNewsNavigation : SaveNewsNavigation) {
        newsNavigationService.save(saveNewsNavigation)
    }

    @DeleteMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        return newsNavigationService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id : String) : NewsNavigation {
        return newsNavigationService.detail(id)
    }



}