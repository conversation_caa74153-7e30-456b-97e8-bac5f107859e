package zone.loong.cube.cases.share.model

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonValue
import org.babyfish.jimmer.Formula
import org.babyfish.jimmer.sql.*
import zone.loong.cube.cases.share.constants.ShareConstants
import zone.loong.cube.framework.data.jimmer.model.ACLAware
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.data.kernel.annotations.ACL
import zone.loong.cube.framework.storage.kernel.model.FileDetail

@Entity
@Table(name = "CASES_SHARE_RESOURCE")
@ACL(
    type = ShareConstants.RESOURCE,
    name = ShareConstants.RESOURCE_NAME,
    parentResourceId = "#parent?.id"
)
interface ShareResource : ACLAware, AuditAware {

    @Key
    val name: String

    @ManyToOne
    val parent: ShareResource?

    val type:ShareResourceType

    //标签
    @Serialized
    val tags: List<String>

    @OneToOne
    val file: FileDetail?

    @OneToMany(mappedBy = "parent", orderedProps = [OrderedProp(value = "createdTime", desc = false)])
    val children: List<ShareResource>


    @Formula(dependencies = ["parent"])
    val path: List<ShareResource>
        get() = getAllPath()

    fun flattenChildren(): List<ShareResource> {
        return children + children.flatMap { it.flattenChildren() }
    }

    fun flattenParent(): List<ShareResource> {
        return parent?.let {
            listOf(it) + it.flattenParent()
        }?.reversed()?: emptyList()
    }

    @JsonIgnore
    fun getAllPath():List<ShareResource>{
        val name = this.name
        val id =this.id
        return flattenParent() + listOf(ShareResource{
            this.name = name
            this.id = id
        })
    }

}
enum class ShareResourceType(@JsonValue val value: String) {
    DIRECTORY("DIRECTORY"),
    FILE("FILE");
}