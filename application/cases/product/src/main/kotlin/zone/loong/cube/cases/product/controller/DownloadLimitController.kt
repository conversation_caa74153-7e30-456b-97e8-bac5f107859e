package zone.loong.cube.cases.product.controller

import org.springframework.web.bind.annotation.*
import zone.loong.cube.cases.product.model.DownloadLimit
import zone.loong.cube.cases.product.model.dto.*
import zone.loong.cube.cases.product.service.DownloadLimitService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

@RestController
@RequestMapping("/product/download-limit")
class DownloadLimitController(private val downloadLimitService: DownloadLimitService) {

    @PostMapping
    fun fetchDownloadLimits(@RequestBody query: PageQuery<DownLoadLimitSpecification>):  Page<DownLoadLimitView> {
        return downloadLimitService.fetchDownloadLimit(query)
    }


    @PostMapping("/save")
    fun save(@RequestBody downloadLimit: SaveDownLoadLimit): DownloadLimit {
        return downloadLimitService.save(downloadLimit)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): DownLoadLimitView {
        return downloadLimitService.detail(id)
    }

    @GetMapping("/delete/{id}")
    fun delete(@PathVariable("id") classify: DownloadLimit) {
        return downloadLimitService.delete(classify.id)
    }

    @PostMapping("records")
    fun fetchLimitRecord(@RequestBody pageQuery: PageQuery<LimitRecordSpecification>): Page<LimitRecordView>{
        return downloadLimitService.fetchLimitRecords(pageQuery)
    }

}