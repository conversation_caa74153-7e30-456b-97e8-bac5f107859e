package zone.loong.cube.platform.auth.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.auth.security.model.Group
import zone.loong.cube.framework.data.jimmer.model.AuditAware

@Entity
@Table(name = "AUTH_GROUP")
interface Group : Group, AuditAware {
    @Key
    override val code: String

    // 组名称
    override val name: String

    // 组描述
    override val description: String?

    @ManyToMany(mappedBy = "groups")
    override val accounts: List<Account>

    @ManyToMany(mappedBy = "groups")
    override val permissions: List<Permission>

    @OneToOne(mappedBy = "group")
    val groupTerm: GroupTerm?
}