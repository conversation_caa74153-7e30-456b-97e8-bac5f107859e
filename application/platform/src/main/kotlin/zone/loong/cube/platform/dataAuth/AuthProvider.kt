package zone.loong.cube.platform.dataAuth

import cn.dev33.satoken.stp.StpUtil
import org.springframework.context.annotation.Lazy
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.kernel.service.AuthProvider
import zone.loong.cube.platform.auth.service.impl.AccountServiceImpl


@Service
class AuthProvider(
    @Lazy private val accountService: AccountServiceImpl
) : AuthProvider {

    override fun getUserGroups(): List<String> {
        val userName = accountService.fetchAccountByToken(StpUtil.getTokenValue() as String).userName
        return accountService.fetchAccountGroups(userName)
    }

    override fun getUserIdentity(): String {
        return accountService.fetchAccountByToken(StpUtil.getTokenValue() as String).userName
    }

    override fun getUserOrganizationIds(): List<String> {
        return accountService.fetchAccount(getUserIdentity()).user?.orgIds ?: emptyList()
    }

    override fun getUserRoles(): List<String> {
        val userName = accountService.fetchAccountByToken(StpUtil.getTokenValue() as String).userName
        return accountService.fetchAccountRoles(userName)
    }
}