package zone.loong.cube.cases.experience.service.impl

import org.babyfish.jimmer.kt.isLoaded
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.desc
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.like
import org.babyfish.jimmer.sql.kt.ast.expression.or
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.cases.experience.model.*
import zone.loong.cube.cases.experience.model.dto.ACLClassify
import zone.loong.cube.cases.experience.model.dto.ClassifySpecification
import zone.loong.cube.cases.experience.model.dto.SaveClassify
import zone.loong.cube.cases.experience.service.ExperienceClassifyService
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.framework.kernel.model.Tree

@Service
class ExperienceClassifyServiceImpl(
    private val kSqlClient: KSqlClient,
) : ExperienceClassifyService {
    override fun fetchClassify(pid: String?): List<ExperienceClassify> {
        return kSqlClient.createQuery(ExperienceClassify::class) {
            pid?.takeIf { it.isNotEmpty() }?.let {
                where(table.parentId eq pid)
            } ?: where(table.parentId eq null)
            select(table.fetchBy {
                allTableFields()
                parent()
                `children*`()
            })
        }.execute()

    }

    override fun fetchClassify(pageQuery: PageQuery<ClassifySpecification>): Page<ExperienceClassify> {
        return kSqlClient.page(pageQuery) {
            orderBy(table.parentId.desc())
            select(table)
        }
    }

    override fun filterClassify(keyword: String): List<Tree> {
        val classifies = kSqlClient.createQuery(ExperienceClassify::class) {
            where(or(table.name like keyword, table.code eq keyword))
            select(table.fetchBy {
                code()
                name()
                parent {
                    code()
                    name()
                }
                `parent*`()
            })
        }.execute()

        //展平树
        val flatten = classifies.flatMap { it.flattenParent() + it }.distinctBy { it.id }
        val tree = flatten.map {
            Tree().apply {
                id = it.id
                label = it.name
                value = it.code
                if (isLoaded(it, ExperienceClassify::parent)) {
                    pid = it.parent?.id ?: ""
                }
                children = mutableListOf()
            }
        }
        return Tree.transformPid(tree)
    }

    override fun detail(id: String): ExperienceClassify {
        return kSqlClient.findById(ExperienceClassify::class, id) ?: throw BizException("该目录不存在或者已经被删除")
    }

    @Transactional
    override fun save(saveClassify: SaveClassify): ExperienceClassify {
      return  kSqlClient.entities.save(saveClassify).modifiedEntity
    }


    override fun delete(id: String) {
        //当前分类下有子分类不允许删除
        val existsChildren = kSqlClient.createQuery(ExperienceClassify::class) {
            where(table.parentId eq id)
            select(table.id)
        }.exists()
        if (existsChildren) {
            throw BizException("当前分类下有子分类不允许删除!")
        }
        //当前分类下有案例不允许删除
        val existsCase = kSqlClient.createQuery(ExperienceCase::class) {
            where(table.classifyId eq id)
            select(table.id)
        }.exists()
        if (existsCase) {
            throw BizException("当前分类下有案例，不允许删除!")
        }

        kSqlClient.deleteById(ExperienceClassify::class, id)
    }



}
