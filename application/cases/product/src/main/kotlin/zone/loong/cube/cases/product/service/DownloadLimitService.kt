package zone.loong.cube.cases.product.service

import zone.loong.cube.cases.product.model.DownloadLimit
import zone.loong.cube.cases.product.model.dto.*
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

interface DownloadLimitService {
    fun fetchDownloadLimit(pageQuery: PageQuery<DownLoadLimitSpecification>): Page<DownLoadLimitView>
    fun save(downloadLimit: SaveDownLoadLimit): DownloadLimit
    fun detail(id: String): DownLoadLimitView
    fun delete(id: String)
    fun fetchLimitRecords(pageQuery: PageQuery<LimitRecordSpecification>): Page<LimitRecordView>
}