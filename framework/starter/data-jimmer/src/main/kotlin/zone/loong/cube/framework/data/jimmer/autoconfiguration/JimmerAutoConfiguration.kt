package zone.loong.cube.framework.data.jimmer.autoconfiguration

import jakarta.annotation.PostConstruct
import lombok.extern.java.Log
import org.babyfish.jimmer.spring.cfg.JimmerAutoConfiguration
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.context.annotation.Import
import zone.loong.cube.framework.kernel.log.annotation.Log.Companion.log

@AutoConfiguration
@Import(JimmerAutoConfiguration::class)
@Log
class JimmerAutoConfiguration {

    @PostConstruct
    fun load() {
        log.info("[CUBE FRAMEWORK] MODULE [DATA-JIMMER] LOAD [SUCCESS].")
    }
}