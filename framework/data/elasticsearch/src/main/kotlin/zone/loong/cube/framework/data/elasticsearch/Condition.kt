package zone.loong.cube.framework.data.elasticsearch

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty

/**
 * ES 查询构造器
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
data class Condition(
    var term: Map<String, Any>? = null,
    var terms: Map<String, List<Any>>? = null,
    @JsonProperty("multi_match")
    var multiMatch: ConditionMultiMatch? = null,
    var range: Map<String, ConditionRange>? = null
)