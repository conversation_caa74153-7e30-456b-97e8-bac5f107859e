package zone.loong.cube.framework.kernel.extension

import org.springframework.stereotype.Component
import java.lang.annotation.Inherited


@Inherited
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
@Component
annotation class Extension(
    val bizId: String = BizScenario.DEFAULT_BIZ_ID,
    val useCase: String = BizScenario.DEFAULT_USE_CASE,
    val scenario: String = BizScenario.DEFAULT_SCENARIO
)
