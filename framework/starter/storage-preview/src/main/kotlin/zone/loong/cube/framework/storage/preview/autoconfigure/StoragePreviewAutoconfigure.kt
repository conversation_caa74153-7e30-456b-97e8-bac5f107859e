package zone.loong.cube.framework.storage.preview.autoconfigure

import jakarta.annotation.PostConstruct
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.context.annotation.Import
import zone.loong.cube.framework.kernel.log.annotation.Log
import zone.loong.cube.framework.kernel.log.annotation.Log.Companion.log
import zone.loong.cube.framework.storage.preview.configure.StoragePreviewConfigure

@AutoConfiguration
@Import(StoragePreviewConfigure::class)
@Log
class StoragePreviewAutoconfigure {
    @PostConstruct
    fun postConstruct() {
        //增加颜色
        log.info("[CUBE FRAMEWORK] MODULE [STORAGE-PREVIEW] LOAD [SUCCESS]")
    }
}