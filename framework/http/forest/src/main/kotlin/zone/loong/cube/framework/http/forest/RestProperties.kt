package zone.loong.cube.framework.http.forest

import org.springframework.boot.context.properties.ConfigurationProperties
import zone.loong.cube.framework.http.forest.exception.RestException

@ConfigurationProperties(prefix = "cube.rest")
class RestProperties {
    var hosts: List<Host> = emptyList()
    fun getHost(code: String): Host {
        return hosts.firstOrNull { it.code == code } ?: throw RestException("编码为 [$code] 的主机不存在")
    }
}

class Host {
    var code: String = ""
    var url: String = ""
    var port: Int = 0
}