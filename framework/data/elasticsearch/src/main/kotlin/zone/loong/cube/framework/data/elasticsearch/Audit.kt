package zone.loong.cube.framework.data.elasticsearch

import org.springframework.data.annotation.Id
import org.springframework.data.elasticsearch.annotations.DateFormat
import org.springframework.data.elasticsearch.annotations.Field
import org.springframework.data.elasticsearch.annotations.FieldType
import zone.loong.cube.framework.data.kernel.model.Audit
import java.time.LocalDateTime

open class Audit : Audit {
    @Id
    var id: String = ""

    @Field(type = FieldType.Date, format = [DateFormat.date_hour_minute_second, DateFormat.epoch_millis])
    override var createdTime: LocalDateTime = LocalDateTime.now()

    @Field
    override var createdBy: String = ""

    @Field(type = FieldType.Date, format = [DateFormat.date_hour_minute_second, DateFormat.epoch_millis])
    override var modifiedTime: LocalDateTime = LocalDateTime.now()

    @Field
    override var modifiedBy: String = ""

}