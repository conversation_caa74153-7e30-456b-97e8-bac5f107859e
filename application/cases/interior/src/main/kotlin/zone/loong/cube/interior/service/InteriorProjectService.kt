package zone.loong.cube.interior.service

import zone.loong.cube.interior.model.InteriorProject
import zone.loong.cube.interior.model.dto.InteriorProjectSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorProject
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

interface InteriorProjectService {
    fun fetchProjects(pageQuery: PageQuery<InteriorProjectSpecification>): Page<InteriorProject>
    fun save(saveInteriorProject: SaveInteriorProject): InteriorProject
    fun delete(id: String)
    fun detail(id: String): InteriorProject
}