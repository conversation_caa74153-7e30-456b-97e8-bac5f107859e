//package zone.loong.cube
//
//import cn.dev33.satoken.SaManager
//import cn.dev33.satoken.spring.SaTokenContextForSpringInJakartaServlet
//import cn.dev33.satoken.stp.StpUtil
//import com.fasterxml.jackson.databind.ObjectMapper
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import zone.loong.cube.interior.service.InteriorProjectStageService
//import zone.loong.cube.news.model.NewsNavigation
//import zone.loong.cube.news.model.dto.SaveNewsNavigation
//import zone.loong.cube.news.service.NewsNavigationService
//
//@SpringBootTest
//class ApplicationTest(
//    @Autowired private val casesNewsNavigationService: NewsNavigationService,
//    @Autowired private val casesInteriorProjectStageService: InteriorProjectStageService,
//    @Autowired private val objectMapper: ObjectMapper
//) {
//    @Test
//    fun perview() {
//        SaManager.setSaTokenContext(SaTokenContextForSpringInJakartaServlet())
//        StpUtil.login("admin")
//        casesNewsNavigationService.save(
//            SaveNewsNavigation(NewsNavigation {
//                title = "测试"
//                code = "test1"
//                children = listOf(
//                    NewsNavigation {
//                        title = "测试2"
//                        code = "test2"
//                    }
//                )
//            })
//        )
//    }
//    @Test
//    fun tree() {
//        SaManager.setSaTokenContext(SaTokenContextForSpringInJakartaServlet())
//        StpUtil.login("admin")
//        println(
//            objectMapper.writeValueAsString(
//                casesNewsNavigationService.fetchNavigations("01JXHBS1WWZDMG6VRX408HNZHE")
//            )
//
//        )
//    }
//    @Test
//    fun delete() {
//        SaManager.setSaTokenContext(SaTokenContextForSpringInJakartaServlet())
//        StpUtil.login("admin")
//        println(
//            objectMapper.writeValueAsString(
//                casesNewsNavigationService.delete("01JXHBS1WWZDMG6VRX408HNZHE")
//            )
//
//        )
//    }
//    @Test
//    fun selectAll() {
//        SaManager.setSaTokenContext(SaTokenContextForSpringInJakartaServlet())
//        StpUtil.login("admin")
//        println(
//            objectMapper.writeValueAsString(
//                casesInteriorProjectStageService.fetchStages()
//            )
//
//        )
//    }
//}
//
//
