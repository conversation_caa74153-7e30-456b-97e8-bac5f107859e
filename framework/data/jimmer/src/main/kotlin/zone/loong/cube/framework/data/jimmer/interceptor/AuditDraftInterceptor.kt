package zone.loong.cube.framework.data.jimmer.interceptor

import org.babyfish.jimmer.kt.isLoaded
import org.babyfish.jimmer.sql.DraftInterceptor
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.data.jimmer.model.AuditAwareDraft
import zone.loong.cube.framework.data.kernel.service.AuthProvider
import java.time.LocalDateTime

class AuditDraftInterceptor(private val authProvider: AuthProvider) : DraftInterceptor<AuditAware, AuditAwareDraft> {

    override fun beforeSave(draft: AuditAwareDraft, original: AuditAware?) {
        if (!isLoaded(draft, AuditAware::modifiedTime)) {
            draft.modifiedTime = LocalDateTime.now()
        }
        if (!isLoaded(draft, AuditAware::modifiedBy)) {
            draft.modifiedBy = authProvider.getUserIdentity()
        }
        if (original === null) {
            if (!isLoaded(draft, AuditAwareDraft::createdTime)) {
                draft.createdTime = LocalDateTime.now()
            }
            if (!isLoaded(draft, AuditAwareDraft::createdBy)) {
                draft.createdBy = authProvider.getUserIdentity()
            }
        }
    }
}