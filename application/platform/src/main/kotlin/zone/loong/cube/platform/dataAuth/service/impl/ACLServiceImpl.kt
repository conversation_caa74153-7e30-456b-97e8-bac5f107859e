package zone.loong.cube.platform.dataAuth.service.impl

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.or
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.PolicyTool
import zone.loong.cube.framework.data.jimmer.event.ResourcePolicyUpdate
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.data.jimmer.model.*
import zone.loong.cube.framework.data.kernel.TransactionSync
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.data.kernel.model.PolicyType
import zone.loong.cube.framework.data.kernel.model.StrategyType
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.platform.auth.model.*
import zone.loong.cube.platform.auth.model.dto.ACLSave
import zone.loong.cube.platform.auth.model.dto.ACLSpecification
import zone.loong.cube.platform.auth.model.dto.ACLView
import zone.loong.cube.platform.auth.model.dto.AccountWithUserInfo
import zone.loong.cube.platform.dataAuth.service.ACLService
import zone.loong.cube.platform.organization.model.Org
import zone.loong.cube.platform.organization.model.enable
import zone.loong.cube.platform.organization.model.id

@Service
class ACLServiceImpl(
    private val kSqlClient: KSqlClient,
    private val applicationEventPublisher: ApplicationEventPublisher
) : ACLService {
    @Transactional()
    override fun authorization(aclSave: ACLSave) {
        val originACL: ACL = fetchACL(aclSave.resourceId)
        val originPolicies = fetchPolicies(aclSave.resourceId)
        val targetPolices = aclSave.policies.map { it.toEntity() }
        val (deleted, added) = PolicyTool.diff(originPolicies, targetPolices)
        check(deleted.isNotEmpty() || added.isNotEmpty()) { return }
        val changedACL = mutableListOf(originACL)
        val policies = added.map { policy ->
            policy.copy {
                aclId = originACL.id
                //前置分类
                with(this.policies()) {
                    originACL.flattenParent().map { parent ->
                        changedACL.add(parent)
                        addBy(policy.copy {
                            aclId = parent.id
                            strategyType = StrategyType.DRILL_UP
                        })
                    }

                    //后置分类
                    originACL.flattenChildren().map { children ->
                        changedACL.add(children)
                        addBy(policy.copy {
                            aclId = children.id
                            strategyType = StrategyType.DRILL_DOWN
                        })
                    }
                }
            }
        }
        addPolicy(policies)
        deleteRefPolicy(deleted.map { it.id })
        TransactionSync.runAfterCommit("推送授权变更") {
            changedACL.groupBy { it.resourceType }.forEach {
                applicationEventPublisher.publishEvent(
                    ResourcePolicyUpdate(
                        it.value.map { it.resourceId },
                        it.value[0].resourceType,
                        it.value[0].resourceName
                    )
                )
            }
        }
    }


    @Transactional()
    override fun authorization(acls: List<ACLSave>) {
        val resourceIds = acls.map { it.resourceId }
        val originACLList = fetchACL(resourceIds)
        val aclPairs = originACLList.map {
            val resourceId = it.resourceId
            val target = acls.firstOrNull { targetACL -> targetACL.resourceId == resourceId }
            Pair(it, target)
        }
        val changedACL = originACLList.toMutableList()


        val addList = mutableListOf<Policy>()
        val deleteList = mutableListOf<Policy>()
        aclPairs.map { (origin, target) ->
            val (deleted, added) = PolicyTool.diff(
                origin.policies,
                target?.policies?.map { it.toEntity() } ?: emptyList())
            deleteList.addAll(deleted)
            addList.addAll(
                added.map { policy ->
                    policy.copy {
                        aclId = origin.id
                        //前置分类
                        with(this.policies()) {
                            origin.flattenParent().map { parent ->
                                changedACL.add(parent)
                                addBy(policy.copy {
                                    aclId = parent.id
                                    strategyType = StrategyType.DRILL_UP
                                })
                            }

                            //后置分类
                            origin.flattenChildren().map { children ->
                                changedACL.add(children)
                                addBy(policy.copy {
                                    aclId = children.id
                                    strategyType = StrategyType.DRILL_DOWN
                                })
                            }
                        }
                    }
                }
            )

        }
        addPolicy(addList)
        deleteRefPolicy(deleteList.map { it.id })
        TransactionSync.runAfterCommit("推送授权变更") {
            changedACL.groupBy { it.resourceType }.forEach {
                applicationEventPublisher.publishEvent(
                    ResourcePolicyUpdate(
                        it.value.map { it.resourceId },
                        it.value[0].resourceType,
                        it.value[0].resourceName
                    )
                )
            }
        }
    }

    private fun fetchACL(resourceId: String): ACL {
        return kSqlClient.createQuery(ACL::class) {
            where(table.resourceId eq resourceId)
            select(table.fetchBy {
                resourceId()
                resourceType()
                resourceName()
                `parent*`()
                `children*`()
            })
        }.fetchOneOrNull() ?: throw BizException("当前资源不存在或已被删除")
    }


    private fun fetchACL(resourceIds: List<String>): List<ACL> {
        return kSqlClient.createQuery(ACL::class) {
            where(table.resourceId valueIn resourceIds)
            select(table.fetchBy {
                resourceId()
                resourceType()
                resourceName()
                `parent*`()
                `children*`()
                policies {
                    allTableFields()
                }
            })
        }.execute()
    }

    private fun fetchPolicies(resourceId: String): List<Policy> {
        return kSqlClient.createQuery(Policy::class) {
            where(table.acl.resourceId eq resourceId)
            select(table.fetchBy {
                allTableFields()
            })
        }.execute()

    }

    override fun fetchACL(pageQuery: PageQuery<ACLSpecification>): Page<ACLView> {
        return kSqlClient.page(pageQuery) {
            select(table.fetchBy {
                allScalarFields()
                policies({
                    filter {
                        where(table.strategyType eq StrategyType.ROOT)
                    }
                }
                ) {
                    allScalarFields()
                }
            })
        }.convertData {
            ACLView(it)
        }
    }

    override fun fetchACL(aclSpecification: ACLSpecification): List<ACLView> {
        return kSqlClient.createQuery(ACL::class) {
            where(aclSpecification)
            orderBy(table.createdTime)
            select(table.fetchBy {
                allScalarFields()
                policies({
                    filter {
                        where(table.strategyType eq StrategyType.ROOT)
                        where(table.policyType eq aclSpecification.policyType)
                    }
                }
                ) {
                    allScalarFields()
                }
            })
        }.execute().map {
            ACLView(it)
        }
    }

    override fun deleteRefPolicy(policyIds: List<String>) {
        check(policyIds.isNotEmpty()) { return }
        kSqlClient.createDelete(Policy::class) {
          where(or(table.id valueIn policyIds,table.refPolicyId valueIn policyIds))
        }.execute()
    }


    override fun addPolicy(policies: List<Policy>) {
        check(policies.isNotEmpty()) { return }
        kSqlClient.entities.saveEntities(policies)
    }

    override fun fetchAccount(
        resourceId: String
    ): List<AccountWithUserInfo> {
        return kSqlClient
            .filters {
                disableByTypes(ACLFilter::class)
            }
            .createQuery(Account::class) {
                where(table.userName valueIn subQuery(Policy::class) {
                    where(table.policyType eq PolicyType.USER)
                    where(table.acl.resourceId eq resourceId)
                    select(table.value)
                })
                select(table.fetch(AccountWithUserInfo::class))
            }.execute()
    }

    override fun fetchOrganization(resourceId: String): List<Org> {
        return kSqlClient.createQuery(Org::class) {
            where(table.enable eq true)
            where(table.id valueIn subQuery(Policy::class) {
                where(table.strategyType eq StrategyType.ROOT)
                where(table.policyType eq PolicyType.ORGANIZATION)
                where(table.acl.resourceId eq resourceId)
                select(table.value)
            })
            select(table)
        }.execute()
    }

    override fun fetchGroups(resourceId: String): List<Group> {
        return kSqlClient.createQuery(Group::class) {
            where(table.code valueIn subQuery(Policy::class) {
                where(table.strategyType eq StrategyType.ROOT)
                where(table.policyType eq PolicyType.GROUP)
                where(table.acl.resourceId eq resourceId)
                select(table.value)
            })
            select(table)
        }.execute()
    }

    override fun fetchRoles(resourceId: String): List<Role> {
        return kSqlClient.createQuery(Role::class) {
            where(table.code valueIn subQuery(Policy::class) {
                where(table.strategyType eq StrategyType.ROOT)
                where(table.policyType eq PolicyType.ROLE)
                where(table.acl.resourceId eq resourceId)
                select(table.value)
            })
            select(table)
        }.execute()
    }

}