package zone.loong.cube.framework.data.elasticsearch.configuration

import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.data.elasticsearch.core.ElasticsearchOperations
import zone.loong.cube.framework.data.elasticsearch.service.SearchIndexService
import zone.loong.cube.framework.data.elasticsearch.service.SearchService
import zone.loong.cube.framework.data.elasticsearch.service.impl.DefaultSearchIndexServiceImpl
import zone.loong.cube.framework.data.elasticsearch.service.impl.DefaultSearchServiceImpl
import zone.loong.cube.framework.data.kernel.configure.DataKernelAuthProviderConfigure
import zone.loong.cube.framework.data.kernel.service.AuthProvider

@Configuration
@Import(value = [JacksonAutoConfiguration::class, DataKernelAuthProviderConfigure::class])
class ElasticsearchConfiguration(
) {
    @Bean
    @ConditionalOnMissingBean
    fun searchService(
        elasticsearchOperations: ElasticsearchOperations,
        objectMapper: ObjectMapper,
        authProvider: AuthProvider
    ): SearchService {
        return DefaultSearchServiceImpl(
            elasticsearchOperations,
            objectMapper,
            authProvider
        )
    }


    @Bean
    @ConditionalOnMissingBean
    fun searchIndexService(
        elasticsearchOperations: ElasticsearchOperations,
    ): SearchIndexService {
        return DefaultSearchIndexServiceImpl(
            elasticsearchOperations
        )
    }
}