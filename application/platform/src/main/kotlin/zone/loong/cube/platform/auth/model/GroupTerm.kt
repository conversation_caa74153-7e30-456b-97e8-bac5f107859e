package zone.loong.cube.platform.auth.model

import com.fasterxml.jackson.annotation.JsonValue
import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.IDAware

@Entity
@Table(name = "AUTH_GROUP_TERMS")
interface GroupTerm : IDAware {

    @OneToOne
    @Key
    val group: Group

    @Serialized
    val terms: Terms
    val expression: String
}

// Option 类
data class Option(
    val value: String,
    val label: String
)

// 操作符类型枚举
enum class OperatorType(@JsonValue val value: String) {
    EQUAL("equal"),
    NOT_EQUAL("notEqual"),
    NOT_EMPTY("notEmpty"),
    EMPTY("empty"),
    CONTAINS("contains"),
    NOT_CONTAINS("notContains"),
    BELONG_TO("belongTo"),
    NOT_BELONG_TO("notBelongTo"),
    START_WITH("startWith"),
    END_WITH("endWith"),
    LESS("less"),
    LESS_OR_EQUAL("lessOrEqual"),
    GREATER("greater"),
    GREATER_OR_EQUAL("greaterOrEqual"),
    BETWEEN("between"),
    NOT_BETWEEN("notBetween"),
    MATCH("match"),
    ANY("any"),
    ALL("all");
}

// 字段类型枚举
enum class FieldType(@JsonValue val value: String) {
    TEXT("text"),
    NUMBER("number"),
    BOOLEAN("boolean"),
    DATE("date"),
    TIME("time"),
    DATETIME("datetime"),
    SELECT("select"),
    ARRAY(
        "array"
    ),
    OBJECT("object");
}

// 基础字段接口
interface BaseField {
    val type: FieldType
    val fieldName: String
    val fieldCode: String
    val operators: List<OperatorType>?
    val placeholder: String?
    val children: List<Field>
}

// 文本字段
data class TextField(
    override val fieldName: String,
    override val fieldCode: String,
    override val operators: List<OperatorType>? = null,
    override val placeholder: String? = null,
    override val children: List<Field> = emptyList(),
    val minLength: Int? = null,
    val maxLength: Int? = null
) : BaseField {
    override val type: FieldType = FieldType.TEXT
}

// 数字字段
data class NumberField(
    override val fieldName: String,
    override val fieldCode: String,
    override val operators: List<OperatorType>? = null,
    override val placeholder: String? = null,
    override val children: List<Field> = emptyList(),
    val maximum: Double? = null,
    val minimum: Double? = null,
    val step: Double? = null,
    val precision: Int? = null
) : BaseField {
    override val type: FieldType = FieldType.NUMBER
}

// 日期字段
data class DateField(
    override val fieldName: String,
    override val fieldCode: String,
    override val operators: List<OperatorType>? = null,
    override val placeholder: String? = null,
    override val children: List<Field> = emptyList(),
    val format: String? = null,
    val inputFormat: String? = null,
    val minDate: String? = null,
    val maxDate: String? = null
) : BaseField {
    override val type: FieldType = FieldType.DATE
}

// 时间字段
data class TimeField(
    override val fieldName: String,
    override val fieldCode: String,
    override val operators: List<OperatorType>? = null,
    override val placeholder: String? = null,
    override val children: List<Field> = emptyList(),
    val minTime: String? = null,
    val maxTime: String? = null,
    val format: String? = null,
    val inputFormat: String? = null
) : BaseField {
    override val type: FieldType = FieldType.TIME
}

// 日期时间字段
data class DatetimeField(
    override val fieldName: String,
    override val fieldCode: String,
    override val operators: List<OperatorType>? = null,
    override val placeholder: String? = null,
    override val children: List<Field> = emptyList(),
    val format: String? = null,
    val inputFormat: String? = null,
    val timeFormat: String? = null
) : BaseField {
    override val type: FieldType = FieldType.DATETIME
}

// 选择字段
data class SelectField(
    override val fieldName: String,
    override val fieldCode: String,
    override val operators: List<OperatorType>? = null,
    override val placeholder: String? = null,
    override val children: List<Field> = emptyList(),
    val multiple: Boolean? = null,
    val options: List<Option>? = null
) : BaseField {
    override val type: FieldType = FieldType.SELECT
}

// 布尔字段
data class BooleanField(
    override val fieldName: String,
    override val fieldCode: String,
    override val operators: List<OperatorType>? = null,
    override val placeholder: String? = null,
    override val children: List<Field> = emptyList()
) : BaseField {
    override val type: FieldType = FieldType.BOOLEAN
}

// 数组字段
data class ArrayField(
    override val fieldName: String,
    override val fieldCode: String,
    override val operators: List<OperatorType>? = null,
    override val placeholder: String? = null,
    override val children: List<Field> = emptyList()
) : BaseField {
    override val type: FieldType = FieldType.ARRAY
}

// 对象字段
data class ObjectField(
    override val fieldName: String,
    override val fieldCode: String,
    override val operators: List<OperatorType>? = null,
    override val placeholder: String? = null,
    override val children: List<Field> = emptyList()
) : BaseField {
    override val type: FieldType = FieldType.OBJECT
}

// 字段类型密封类
sealed class Field {
    data class Text(val field: TextField) : Field()
    data class Number(val field: NumberField) : Field()
    data class Date(val field: DateField) : Field()
    data class Time(val field: TimeField) : Field()
    data class Datetime(val field: DatetimeField) : Field()
    data class Select(val field: SelectField) : Field()
    data class Boolean(val field: BooleanField) : Field()
    data class Array(val field: ArrayField) : Field()
    data class Object(val field: ObjectField) : Field()
}

// 字段信息类
data class FieldInfo(
    val type: FieldType, val code: String
)

// 条件类
data class Condition(
    val field: FieldInfo, val op: String, // 可以是 OperatorType 的值或空字符串
    val value: Any?
)

// 逻辑类型枚举
enum class LogicType(@JsonValue val value: String) {
    AND("AND"), OR("OR");
}

// 条件组类
data class Terms(
    val conditions: List<Condition> = emptyList(),
    val groups: List<Terms> = emptyList(),
    val logic: LogicType = LogicType.AND
)

// 条件类型枚举
enum class TermType(@JsonValue val value: String) {
    GROUP("GROUP"), CONDITION("CONDITION");
}
