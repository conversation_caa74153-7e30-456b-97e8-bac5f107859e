package zone.loong.cube.interior.model

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.LogicalDeleted
import org.babyfish.jimmer.sql.Serialized
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import java.time.LocalDate
import java.time.LocalDateTime

/**
 * 项目管理
 */
@Entity
interface InteriorProject : AuditAware {

    /**
     * 唯一标识（未使用）
     */
    val code: String?

    /**
     * 项目名称
     */
    val title: String

    /**
     * 标签
     */
    @Serialized
    val tags: List<String>

    /**
     * 负责人
     */
    val principal: String

    /**
     * 成员
     */
    val member: String

    /**
     * 项目成本
     */
    val projectCost: String

    /**
     * 状态
     */
    val status: String?

    /**
     * 开始时间
     */
    val startTime: LocalDate

    /**
     * 结束时间
     */
    val endTime: LocalDate

    /**
     * 计划工期
     */
    val planningCycle: String

    /**
     * 备注
     */
    val remark: String?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}

