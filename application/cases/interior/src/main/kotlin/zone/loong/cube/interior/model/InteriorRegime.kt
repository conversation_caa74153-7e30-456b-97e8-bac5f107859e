package zone.loong.cube.interior.model

import com.fasterxml.jackson.annotation.JsonValue
import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.LogicalDeleted
import org.babyfish.jimmer.sql.Serialized
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import java.time.LocalDate

/**
 * 制度管理
 */
@Entity
interface InteriorRegime : AuditAware {

    /**
     * 唯一编码
     */
    val code: String?

    /**
     * 制度标题
     */
    val title: String

    /**
     * 标签
     */
    @Serialized
    val tags: List<String>

    /**
     * 负责组
     */
    val responsibleTeam: String

    /**
     * 负责人
     */
    val principal: String

    /**
     * 状态
     */
    val status: RegimeType

    /**
     * 制度生效期
     */
    val effectiveDate: LocalDate

    /**
     * 废止日期
     */
    val failureDate: LocalDate

    /**
     * 制度版本号
     */
    val regimeVersion: String?

    /**
     * 备注
     */
    val remark: String?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}

enum class RegimeType(@JsonValue val value: String) {
    ENABLED("ENABLED"),
    DISABLED("DISABLED");
}

