package zone.loong.cube.news.controller

import org.springframework.web.bind.annotation.*
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsResourceManager
import zone.loong.cube.news.model.dto.PageSpecification
import zone.loong.cube.news.model.dto.SaveNewsResourceManager
import zone.loong.cube.news.model.elasticsearch.ESNewsDispersion
import zone.loong.cube.news.service.EsResourceService
import zone.loong.cube.news.service.NewsResourceManagerService

@RestController
@RequestMapping("/news/resourceManager")
class NewsResourceManagerController(
    private val newsResourceManagerService: NewsResourceManagerService,
    private val esResourceService: EsResourceService
) {

    @PostMapping("/es/page")
    fun fetch(@RequestBody pageQuery: PageQuery<ESSpecification>): ESPage<ESNewsDispersion> {
        return newsResourceManagerService.fetchNews(pageQuery)
    }

    @PostMapping("/page")
    fun fetch(@RequestBody pageQuery: PageQuery<PageSpecification>): Page<NewsResourceManager> {
        return newsResourceManagerService.fetchResourceManagers(pageQuery)
    }

    @PostMapping("/save")
    fun save(@RequestBody saveNewsResourceManager: SaveNewsResourceManager): NewsResourceManager {
        return newsResourceManagerService.save(saveNewsResourceManager)
    }

    @DeleteMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        newsResourceManagerService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): NewsResourceManager {
        return newsResourceManagerService.detail(id)
    }

    @GetMapping("/sync")
    fun sync() {
        return newsResourceManagerService.sync()
    }
}