package zone.loong.cube.platform.organization.service.sync

import com.dtflys.forest.annotation.GetRequest
import com.dtflys.forest.annotation.Var
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.module.kotlin.jsonMapper
import com.fasterxml.jackson.module.kotlin.readValue
import org.springframework.stereotype.Component
import zone.loong.cube.framework.http.forest.annotation.Rest


class Response<T> {
    val errmsg: String? = null
    val success: Boolean = false
    val data: List<T> = emptyList()
}

class Company {
    @JsonProperty("id")
    val id: String = ""

    @JsonProperty("pk_corp")
    val code: String = ""

    @JsonProperty("name")
    val name: String = ""

    @JsonProperty("fatherpk")
    val parent: String = ""

    @JsonProperty("isenable")
    var enable: Int = 0
}

class Department {
    @JsonProperty("id")
    val id: String = ""

    @JsonProperty("orgid")
    val companyId: String = ""

    @JsonProperty("code")
    val code: String = ""

    @JsonProperty("name")
    val name: String = ""

    @JsonProperty("pk_fathedept")
    val parent: String? = null

    @JsonProperty("isenable")
    val enable: Int = 0
}

class Position {
    @JsonProperty("postid")
    val id: String = ""

    @JsonProperty("deptid")
    val deptId: String = ""

    @JsonProperty("posLevel")
    val code: String = ""

    @JsonProperty("postname")
    val name: String = ""

    @JsonProperty("isenable")
    val enable: Boolean = false
}

class User {
    @JsonProperty("psnid")
    val id: String = ""

    @JsonProperty("postid")
    val postId: String? = null

    @JsonProperty("psnpy")
    val code: String = ""

    @JsonProperty("name")
    val name: String = ""

    @JsonProperty("email")
    val email: String = ""

    @JsonProperty("mobile")
    val phone: String = ""

    @JsonProperty("sex")
    val sex: String = ""

    @JsonProperty("esnuser")
    val manager: String = "N"

    @JsonProperty("sendpost")
    val sendpost: String = ""

    @JsonProperty("levelname")
    val levelname: String = ""

    @JsonProperty("isenable")
    val enable: Int = 0

    fun getPostIds(): List<String> {
        val postIds = mutableListOf<String>()
        if (sendpost.isNotEmpty()) {
            val sendPosts = jsonMapper().readValue<List<Map<String, String>>>(sendpost).map {
                it["岗位ID"] ?: ""
            }.filter { it.isNotEmpty() }.toList()
            postIds.addAll(sendPosts)
        }
        postId?.let {
            postIds.add(postId)
        }
        println(postIds)
        return postIds

    }
}

@Rest(code = "organization-sync")
@Component
interface OrganizationClient {
    @GetRequest("/WebApi/jh/Getcorpnc/{ts}")
    fun fetchCompany(@Var("ts") ts: String): Response<Company>

    @GetRequest("/WebApi/jh/Getdeptnc/{companyId}/{ts}")
    fun fetchDepartment(@Var("companyId") companyId: String, @Var("ts") ts: String): Response<Department>

    @GetRequest("/WebApi/jh/Getpostnc/{companyId}/{ts}")
    fun fetchPosition(@Var("companyId") companyId: String, @Var("ts") ts: String): Response<Position>

    @GetRequest("/WebApi/jh/Getpersonnc/{companyId}/{ts}")
    fun fetchUser(@Var("companyId") companyId: String, @Var("ts") ts: String): Response<User>
}

