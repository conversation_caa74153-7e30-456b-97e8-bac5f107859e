package zone.loong.cube.cases.experience.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.cases.experience.model.ExperienceCase
import zone.loong.cube.cases.experience.model.dto.CaseViewWithName
import zone.loong.cube.cases.experience.model.dto.SaveCase
import zone.loong.cube.cases.experience.model.elasticsearch.ESExperienceCase
import zone.loong.cube.cases.experience.service.ExperienceCasesService
import zone.loong.cube.cases.experience.service.ExperienceESCasesService
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.elasticsearch.service.SearchService
import zone.loong.cube.framework.data.elasticsearch.service.page
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.exception.BizException

@Service
class ExperienceCasesServiceImpl(
    private val kSqlClient: KSqlClient,
    private val searchService: SearchService,
    private val experienceEsCasesService: ExperienceESCasesService,
) : ExperienceCasesService {

    override fun fetchCases(pageQuery: PageQuery<ESSpecification>): ESPage<ESExperienceCase> {
        return searchService.page<ESExperienceCase>(pageQuery)
    }

    override fun detail(id: String): CaseViewWithName {
        return kSqlClient.findById(CaseViewWithName::class, id) ?: throw BizException("该目录不存在或者已经被删除")
    }


    @Transactional
    override fun save(saveCase: SaveCase): ExperienceCase {
       return (saveCase.id?.let {
            kSqlClient.entities.saveCommand(saveCase, SaveMode.UPSERT).execute().modifiedEntity
        }?:let{
            kSqlClient.entities.saveCommand(saveCase, SaveMode.INSERT_ONLY).execute().modifiedEntity
        }).also {
           experienceEsCasesService.sync(it.id,true)
       }
    }

    /**
     * 删除逻辑
     * 1.删除当前案例
     * 2.删除当前案例对应的授权
     * 3.删除当前案例授权的传播模型
     */
    @Transactional
    override fun delete(id: String) {
        kSqlClient.deleteById(ExperienceCase::class, id)
        experienceEsCasesService.delete(id)
    }

    override fun sync() {
        val casesIds = kSqlClient.filters {
            disableByTypes(ACLFilter::class)
        }.entities.findAll(ExperienceCase::class).map { it.id }
        experienceEsCasesService.syncWithFile(casesIds)
    }

}