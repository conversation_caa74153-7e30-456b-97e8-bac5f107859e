package zone.loong.cube.cases.experience.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.cases.experience.constants.ExperienceConstants
import zone.loong.cube.framework.data.jimmer.model.ACLAware
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.data.kernel.annotations.ACL
import zone.loong.cube.framework.storage.kernel.model.FileDetail
import java.time.LocalDateTime

@Entity
@Table(name = "CASES_EXPERIENCE")
@ACL(
    type = ExperienceConstants.CASE_RESOURCE,
    name = ExperienceConstants.CASE_RESOURCE_NAME,
    parentResourceId = "#classify?.id"
)
interface ExperienceCase : ACLAware, AuditAware {
    //标题
    val title: String

    //标签
    @Serialized
    val tags: List<String>

    //年度
    val caseOfYear: String

    //案例状态
    val status: CaseStatus

    //提报日期
    val postDate: LocalDateTime

    //提报人
    val postUser: String

    //文件ID
    @OneToOne
    @Key
    val file: FileDetail

    @ManyToOne
    val classify: ExperienceClassify?

}

enum class CaseStatus {
    //上架
    ON_SHELVES,

    //下架
    OFF_SHELVES

}

enum class CaseType {
    //案例
    CASE,

    //产品
    PRODUCT

}