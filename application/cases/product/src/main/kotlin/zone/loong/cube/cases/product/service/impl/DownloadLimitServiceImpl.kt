package zone.loong.cube.cases.product.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Service
import zone.loong.cube.cases.product.model.DownloadLimit
import zone.loong.cube.cases.product.model.dto.*
import zone.loong.cube.cases.product.service.DownloadLimitService
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.exception.BizException

@Service
class DownloadLimitServiceImpl(private val kSqlClient: KSqlClient) : DownloadLimitService {
    override fun fetchDownloadLimit(pageQuery: PageQuery<DownLoadLimitSpecification>): Page<DownLoadLimitView> {
        return kSqlClient.page(pageQuery) {
            select(table.fetch(DownLoadLimitView::class))
        }
    }

    override fun fetchLimitRecords(pageQuery: PageQuery<LimitRecordSpecification>): Page<LimitRecordView> {
        return kSqlClient.page(pageQuery) {
            select(table.fetch(LimitRecordView::class))
        }
    }

    override fun save(downloadLimit: SaveDownLoadLimit): DownloadLimit {
        return downloadLimit.id?.let {
            kSqlClient.entities.save(downloadLimit, SaveMode.UPSERT).modifiedEntity
        } ?: let {
            kSqlClient.entities.save(downloadLimit, SaveMode.INSERT_ONLY).modifiedEntity
        }
    }

    override fun detail(id: String): DownLoadLimitView {
        return kSqlClient.findById(DownLoadLimitView::class, id) ?: throw BizException("资源未找到或已被删除")
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(DownloadLimit::class, id)
    }
}