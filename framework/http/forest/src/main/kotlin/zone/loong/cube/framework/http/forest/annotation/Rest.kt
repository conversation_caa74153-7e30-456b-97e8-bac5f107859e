package zone.loong.cube.framework.http.forest.annotation

import com.dtflys.forest.annotation.MethodLifeCycle
import com.dtflys.forest.annotation.RequestAttributes
import zone.loong.cube.framework.http.forest.lifeCycle.RestLifeCycle

@MustBeDocumented
@MethodLifeCycle(RestLifeCycle::class)
@RequestAttributes
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.TYPE, AnnotationTarget.FUNCTION, AnnotationTarget.CLASS)
annotation class Rest(
    val code: String,
)
