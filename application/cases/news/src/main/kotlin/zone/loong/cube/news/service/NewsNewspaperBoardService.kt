package zone.loong.cube.news.service

import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsNewspaperBoard
import zone.loong.cube.news.model.dto.NewsNewspaperBoardSpecification
import zone.loong.cube.news.model.dto.SaveNewsNewspaperBoard

interface NewsNewspaperBoardService {
    fun fetchResourceManagers(pageQuery: PageQuery<NewsNewspaperBoardSpecification>): Page<NewsNewspaperBoard>
    fun save(saveNewsNewspaperBoard: SaveNewsNewspaperBoard): NewsNewspaperBoard
    fun delete(id: String)
    fun detail(id: String): NewsNewspaperBoard
}