package zone.loong.cube.framework.storage.convert.service

import org.dromara.x.file.storage.core.FileInfo
import java.util.UUID

/**
 * 文件转换任务数据模型。
 * 只需传递源文件ID，目标类型由 targetType 指定。
 * @property id 任务唯一ID
 * @property sourceFileId 源文件ID
 * @property targetFile 目标文件信息（转换完成后赋值）
 * @property targetType 目标类型（仅支持 PDF、IMAGE）
 * @property status 当前任务状态
 * @property progress 进度百分比（0-100）
 * @property errorMsg 错误信息（失败时赋值）
 */
data class FileConvertTask(
    val id: String = UUID.randomUUID().toString(),
    val sourceFileId: String,
    var targetFile: FileInfo? = null,
    val targetType: TargetType,
    var status: FileConvertStatus = FileConvertStatus.PENDING,
    var progress: Int = 0,
    var errorMsg: String? = null
)

/**
 * 支持的目标类型枚举。
 */
enum class TargetType {
    PDF, IMAGE
}
