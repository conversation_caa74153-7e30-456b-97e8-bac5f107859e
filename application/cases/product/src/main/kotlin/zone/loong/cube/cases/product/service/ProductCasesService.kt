package zone.loong.cube.cases.product.service


import jakarta.servlet.http.HttpServletResponse
import zone.loong.cube.cases.product.model.ProductCase
import zone.loong.cube.cases.product.model.dto.CaseViewWithName
import zone.loong.cube.cases.product.model.dto.SaveCase
import zone.loong.cube.cases.product.model.elasticsearch.ESProductCase
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.kernel.model.PageQuery

interface ProductCasesService {
    fun fetchCases(pageQuery: PageQuery<ESSpecification>): ESPage<ESProductCase>
    fun detail(id: String): CaseViewWithName
    fun save(saveCase: SaveCase): ProductCase
    fun delete(id: String)
    fun download(attachmentId: String,response: HttpServletResponse)
    fun sync()
}