package zone.loong.cube.interior.service.impl

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.asc
import org.springframework.stereotype.Service
import zone.loong.cube.interior.model.InteriorProjectStage
import zone.loong.cube.interior.model.sequence
import zone.loong.cube.interior.service.InteriorProjectStageService

@Service
class InteriorProjectStageServiceImpl(
    private val kSqlClient: KSqlClient
) : InteriorProjectStageService {

    override fun fetchStages(): List<InteriorProjectStage> {
        val stageList = kSqlClient.createQuery(InteriorProjectStage::class){
            orderBy(table.sequence.asc())
            select(table)
        }.execute()
        return stageList
    }



}