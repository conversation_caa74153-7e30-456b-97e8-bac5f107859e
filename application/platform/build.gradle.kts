plugins{
    alias(libs.plugins.ksp)
}
dependencies {
    api(libs.postgresql)
    api(libs.cube.framework.kernel)
    api(libs.cube.framework.auth)
    api(libs.cube.framework.storage.local)
    api(libs.cube.framework.storage.preview)
    api(libs.cube.framework.cache.jetcache)
    api(libs.cube.framework.data.jimmer)
    api(libs.cube.framework.http.forest)
    api(libs.forest)
    api("org.springframework.boot:spring-boot-starter-aop")
    ksp(libs.jimmer.ksp)
}