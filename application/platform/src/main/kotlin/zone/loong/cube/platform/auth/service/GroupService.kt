package zone.loong.cube.platform.auth.service

import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.Group
import zone.loong.cube.platform.auth.model.Terms
import zone.loong.cube.platform.auth.model.dto.AccountWithUserInfo
import zone.loong.cube.platform.auth.model.dto.GroupAccount
import zone.loong.cube.platform.auth.model.dto.GroupAuthorization
import zone.loong.cube.platform.auth.model.dto.GroupSpecification

interface GroupService {
    fun fetchGroup(pageQuery: PageQuery<GroupSpecification>): Page<Group>
    fun fetchAccounts(groupId: String): List<AccountWithUserInfo>
    fun fetchPermissions(groupId: String): List<String>
    fun fetchTerms(groupId: String): Terms?
    fun authorize(groupAuthorization:GroupAuthorization)
    fun saveGroup(group: Group): Group
    fun groupAccount(groupAccount: GroupAccount): Group
    fun getGroupById(id: String): Group
}