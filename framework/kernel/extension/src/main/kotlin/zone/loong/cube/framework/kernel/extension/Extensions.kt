package zone.loong.cube.framework.kernel.extension

import org.springframework.stereotype.Component
import java.lang.annotation.Inherited

@Inherited
@Target(AnnotationTarget.TYPE)
@Retention(AnnotationRetention.RUNTIME)
@Component
annotation class Extensions(
    val bizId: Array<String> = [BizScenario.DEFAULT_BIZ_ID],
    val useCase: Array<String> = [BizScenario.DEFAULT_USE_CASE],
    val scenario: Array<String> = [BizScenario.DEFAULT_SCENARIO]
)