export zone.loong.cube.platform.auth.model.Group
    -> package zone.loong.cube.platform.auth.model.dto

specification GroupSpecification{
    code
    like(name)
}

input GroupAccount{
    id
    accounts{
        id
    }
}
GroupPermission{
    id
    permissions{
        id
        code
        name
        type
        href
        status
        meta
        description
        children*
        parent{
            id
        }
    }
}

input GroupAuthorization{
    id
    permissions?{
        id
    }
    accounts?{
        id
    }
    dynamic groupTerm{
        expression
        terms
    }
}