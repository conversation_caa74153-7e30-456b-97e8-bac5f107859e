package zone.loong.cube.framework.storage.convert

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Configuration

/**
 * 文件转换相关配置。
 * 主要用于配置临时文件存储路径等参数。
 */
@Configuration
@ConfigurationProperties(prefix = "file.convert")
class FileConvertProperties {
    /**
     * 临时文件存储路径（如 /tmp/convert）
     */
    var tempDir: String = "/tmp/convert"
}
