package zone.loong.cube.framework.kernel.extension

import org.springframework.stereotype.Component
import zone.loong.cube.framework.kernel.log.annotation.Log
import java.util.function.Consumer
import java.util.function.Function

@Component
@Log
class ExtensionExecutor(private val extensionRepository: ExtensionRepository) {
    fun <R : Any, T : ExtensionPointI> execute(
        bizScenario: BizScenario,
        exeFunction: Function<T, R>
    ): R {
        return exeFunction.apply(extensionRepository.locateComponent(bizScenario))
    }

    fun <T : ExtensionPointI> execute(bizScenario: BizScenario, action: Consumer<T>) {
        action.accept(extensionRepository.locateComponent(bizScenario))
    }

}

