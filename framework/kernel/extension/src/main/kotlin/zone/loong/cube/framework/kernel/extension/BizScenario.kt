package zone.loong.cube.framework.kernel.extension

class BizScenario {
    companion object {
        const val DEFAULT_BIZ_ID: String = "#defaultBizId#"

        const val DEFAULT_USE_CASE: String = "#defaultUseCase#"

        const val DEFAULT_SCENARIO: String = "#defaultScenario#"

        const val DOT_SEPARATOR: String = "."

        fun valueOf(bizId: String, useCase: String, scenario: String): BizScenario {
            val bizScenario = BizScenario()
            bizScenario.bizId = bizId
            bizScenario.useCase = useCase
            bizScenario.scenario = scenario
            return bizScenario
        }

        fun valueOf(bizId: String, useCase: String): BizScenario {
            return valueOf(bizId, useCase, DEFAULT_SCENARIO)
        }

        fun valueOf(scenario: String): BizScenario {
            return valueOf(DEFAULT_BIZ_ID, DEFAULT_USE_CASE, scenario)
        }

        fun newDefault(): BizScenario {
            return valueOf(DEFAULT_BIZ_ID, DEFAULT_USE_CASE, DEFAULT_SCENARIO)
        }
    }


    /**
     * bizId is used to identify a business, such as "tmall", it's nullable if there is only one biz
     */
    private var bizId = DEFAULT_BIZ_ID

    /**
     * useCase is used to identify a use case, such as "placeOrder", can not be null
     */
    private var useCase = DEFAULT_USE_CASE

    /**
     * scenario is used to identify a use case, such as "88vip","normal", can not be null
     */
    private var scenario = DEFAULT_SCENARIO

    /**
     * For above case, the BizScenario will be "tmall.placeOrder.88vip",
     * with this code, we can provide extension processing other than "tmall.placeOrder.normal" scenario.
     *
     * @return
     */
    fun getUniqueIdentity(): String {
        return bizId + DOT_SEPARATOR + useCase + DOT_SEPARATOR + scenario
    }


    fun getIdentityWithDefaultScenario(): String {
        return bizId + DOT_SEPARATOR + useCase + DOT_SEPARATOR + DEFAULT_SCENARIO
    }

    fun getIdentityWithDefaultUseCase(): String {
        return bizId + DOT_SEPARATOR + DEFAULT_USE_CASE + DOT_SEPARATOR + DEFAULT_SCENARIO
    }

    //hash code
    override fun hashCode(): Int {
        var result = bizId.hashCode()
        result = 31 * result + useCase.hashCode()
        result = 31 * result + scenario.hashCode()
        return result
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as BizScenario

        if (bizId != other.bizId) return false
        if (useCase != other.useCase) return false
        if (scenario != other.scenario) return false

        return true
    }
}