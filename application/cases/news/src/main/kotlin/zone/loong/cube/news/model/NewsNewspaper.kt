package zone.loong.cube.news.model

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.JoinColumn
import org.babyfish.jimmer.sql.LogicalDeleted
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OneToMany
import org.babyfish.jimmer.sql.OneToOne
import org.babyfish.jimmer.sql.OrderedProp
import org.babyfish.jimmer.sql.Serialized
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail
import java.time.LocalDate

/**
 * 报纸管理
 */
@Entity
interface NewsNewspaper : AuditAware {

    /**
     * 唯一标识
     */
    val code: String?

    /**
     * 模块id
     */
    @ManyToOne
    @JoinColumn(name = "BLOCK_ID")
    val newsBlock: NewsBlock

    /**
     * 版面
     */
    @OneToMany(
        mappedBy = "newspaper",
        orderedProps = [OrderedProp("sequence", desc = false)]
    )
    val newspaperBoards: List<NewsNewspaperBoard>

    /**
     * 标题
     */
    val title: String?

    /**
     * 标签
     */
    @Serialized
    val tags: List<String>

    /**
     * 期数
     */
    val periods: Int?

    /**
     * 页数
     */
    val pageNum: Int?

    /**
     * 主办
     */
    val sponsor: String?

    /**
     * 出版日期
     */
    val publishDate: LocalDate?

    /**
     * 发布时间
     */
    val issueTime: LocalDate?

    /**
     * 发布人
     */
    val issuer: String?

    /**
     * 发布单位
     */
    val issueUnit: String?

    /**
     * 阅读量
     */
    val readingNum: Integer?

    /**
     * 文件
     */
    @OneToOne
    @JoinColumn(name = "FILE_ID")
    val file: FileDetail?

    /**
     * 状态
     */
    val status: String?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}


// 3. 为每一组封装期数区间
data class NewspaperGroup(
    val title: String,       // 区间名称，例如：第一期标题 - 第六期标题
    val newspaperIds: List<String>,    // 该区间内所有期数对应的id集合
)

