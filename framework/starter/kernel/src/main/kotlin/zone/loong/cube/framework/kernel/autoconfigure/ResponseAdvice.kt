package zone.loong.cube.framework.kernel.autoconfigure

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.annotation.Nullable
import org.apache.juli.logging.Log
import org.apache.juli.logging.LogFactory
import org.springframework.core.MethodParameter
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.http.MediaType
import org.springframework.http.converter.HttpMessageConverter
import org.springframework.http.server.ServerHttpRequest
import org.springframework.http.server.ServerHttpResponse
import org.springframework.validation.ObjectError
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.RestControllerAdvice
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice
import zone.loong.cube.framework.kernel.annotation.IgnoreAdvice
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.framework.kernel.exception.ResultCode
import  zone.loong.cube.framework.kernel.model.Result
import java.io.PrintWriter
import java.io.StringWriter
import java.util.stream.Collectors

@RestControllerAdvice
@Order(Ordered.LOWEST_PRECEDENCE)
class ResponseAdvice(private val objectMapper: ObjectMapper) : ResponseBodyAdvice<Any> {
    companion object {
        val log: Log = LogFactory.getLog(ResponseAdvice::class.java)
    }

    override fun supports(methodParameter: MethodParameter, aClass: Class<out HttpMessageConverter<*>>): Boolean {
        val ignoreWarp = methodParameter.methodAnnotations.any {
            it.annotationClass == (IgnoreAdvice::class)
        }
        // response是Result类型
        return !methodParameter.parameterType.isAssignableFrom(Result::class.java) && !ignoreWarp
    }

    override fun beforeBodyWrite(
        @Nullable data: Any?,
        returnType: MethodParameter,
        mediaType: MediaType,
        aClass: Class<out HttpMessageConverter<*>>,
        request: ServerHttpRequest,
        response: ServerHttpResponse
    ): Any? {

        // String类型不能直接包装
        if (returnType.genericParameterType == String::class.java) {
            return try {
                // 将数据包装在ResultVo里后转换为json串进行返回
                objectMapper.writeValueAsString(zone.loong.cube.framework.kernel.model.Result(data ?: Any()))
            } catch (e: JsonProcessingException) {
                throw BizException(ResultCode.FAILED.code, e.message!!)
            }
        }
        // 否则直接包装成ResultVo返回
        return Result(data ?: Any())
    }

    /**
     * 处理系统异常
     */
    @ExceptionHandler(Exception::class)
    fun exceptionHandler(e: Exception): Result {
        logException(e)
        return Result.error(
            ResultCode.RUNTIME_FAILED.code,
            ResultCode.RUNTIME_FAILED.msg
        )
    }

    /**
     * 处理自定义业务异常
     */
    @ExceptionHandler(BizException::class)
    fun globalExceptionHandler(e: BizException): Result {
        logException(e)
        return Result.error(ResultCode.FAILED.code, e.msg)
    }

    /**
     * 处理校验对象时候的异常MethodArgumentNotValidException
     */
    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun globalExceptionHandler(e: MethodArgumentNotValidException): Result {
        logException(e)
        val errors = e.bindingResult.allErrors
        val messages = errors.stream().map { obj: ObjectError -> obj.defaultMessage }.collect(Collectors.joining(","))
        return Result.error(ResultCode.VALIDATE_ERROR.code, messages)
    }

    private fun logException(e: Exception) {
        val errorsWriter = StringWriter()
        e.printStackTrace(PrintWriter(errorsWriter))
        log.info(errorsWriter.toString())
    }
}
