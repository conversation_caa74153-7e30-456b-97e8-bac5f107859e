package zone.loong.cube.platform.auth.extPt

import jodd.util.StringUtil
import org.springframework.stereotype.Service
import zone.loong.cube.framework.auth.security.constants.SecurityConstants
import zone.loong.cube.framework.auth.security.service.AccountService
import zone.loong.cube.framework.auth.security.service.LoginExtPoint
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.framework.kernel.extension.Extension
import zone.loong.cube.modules.auth.model.LoginScenario
import zone.loong.cube.platform.login.LoginService

@Service
@Extension(
    bizId = SecurityConstants.DEFAULT_LOGIN_EXT_POINT_BIZ_ID,
    useCase = SecurityConstants.DEFAULT_LOGIN_EXT_POINT_USE_CASE,
    scenario = LoginScenario.DING_TALK
)
class DingTalkLoginServiceExtPoint(private val accountService: AccountService, private val loginService: LoginService) :
    LoginExtPoint {
    override fun valid(accountIdentity: String, password: String): String {
        var account: String = ""
        val appToken = loginService.getAppToken(
            "54579b9a5017e5d31403da9bab0a945022abb5f41eb69f242b57c26e6586ac7c",
            "9a927dfc97792b82a04205c26d1017994cbfd2e783095a1e1576399177ff78ac"
        )
        if (appToken.success && appToken.data != null) {
            val token = loginService.doLogin(password, appToken.data.appToken)
            if (token.success && token.data != null) {
                val userInfo = loginService.getUserInfo("Bearer " + token.data.accessToken, appToken.data.appToken)
                if (userInfo.success && userInfo.data != null) {
                    account = userInfo.data.userName
                }
            }
        }
        if (StringUtil.isEmpty(account)) {
            throw BizException("获取用户信息失败")
        }
        return account
    }
}