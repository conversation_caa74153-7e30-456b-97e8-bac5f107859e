package zone.loong.cube.framework.data.jimmer.event

/**
 * 资源父级变更事件
 */
data class ResourceParentChanged(
    val resourceId: String,
    val oldParentId: String?,
    val newParentId: String?,
    val resourceType: String,
    val resourceName: String
)

/**
 * 资源删除事件
 */
data class ResourceDeleted(
    val resourceId: String,
    val resourceType: String,
    val resourceName: String
)

/**
 * 批量权限更新事件
 */
data class BatchResourcePolicyUpdate(
    val updates: List<ResourcePolicyUpdate>
)
