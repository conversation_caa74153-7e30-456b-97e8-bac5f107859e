package zone.loong.cube.platform.auth.listener

import cn.dev33.satoken.listener.SaTokenListener
import cn.dev33.satoken.stp.parameter.SaLoginParameter
import org.springframework.stereotype.Component
import zone.loong.cube.platform.auth.service.impl.AccountServiceImpl

@Component
class AuthListener(private val accountServiceImpl: AccountServiceImpl) : SaTokenListener {

    override fun doLogin(
        loginType: String?,
        loginId: Any?,
        tokenValue: String?,
        loginParameter: SaLoginParameter?
    ) {

    }

    override fun doLogout(loginType: String?, loginId: Any, tokenValue: String?) {
        loginId as String
        accountServiceImpl.cleanAccount(loginId)
        accountServiceImpl.cleanGroup(loginId)
    }

    override fun doKickout(loginType: String?, loginId: Any?, tokenValue: String?) {

    }

    override fun doReplaced(loginType: String?, loginId: Any?, tokenValue: String?) {

    }

    override fun doDisable(loginType: String?, loginId: Any?, service: String?, level: Int, disableTime: Long) {

    }

    override fun doUntieDisable(loginType: String?, loginId: Any?, service: String?) {

    }

    override fun doOpenSafe(loginType: String?, tokenValue: String?, service: String?, safeTime: Long) {

    }

    override fun doCloseSafe(loginType: String?, tokenValue: String?, service: String?) {

    }

    override fun doCreateSession(id: String?) {

    }

    override fun doLogoutSession(id: String?) {

    }

    override fun doRenewTimeout(tokenValue: String?, loginId: Any?, timeout: Long) {

    }
}