package zone.loong.cube.news.task

import com.dtflys.forest.annotation.BaseRequest
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.framework.http.forest.RestProperties
import zone.loong.cube.news.model.ContentType
import zone.loong.cube.news.model.NewsBlock
import zone.loong.cube.news.model.NewsResourceManager
import zone.loong.cube.news.model.NewsResourceManagerArticle
import zone.loong.cube.news.model.NewsResourceManagerAttachment
import zone.loong.cube.news.model.NewsType
import zone.loong.cube.news.model.dto.SaveNewsArticles
import zone.loong.cube.news.model.dto.SaveNewsResourceManager
import zone.loong.cube.news.model.navigationId
import zone.loong.cube.news.service.NewsResourceManagerArticleService
import zone.loong.cube.news.service.NewsResourceManagerService
import zone.loong.cube.platform.storage.service.StorageService
import java.net.URL
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

// 获取配置文件定义的url地址
@BaseRequest(
    baseURL = "#{cube.storage.preview.kk-file-view-url}",     // 默认域名
)

@Component
class NewsSyncTask(
    private val kSqlClient: KSqlClient,
    private val officialAccountsClient: OfficialAccountsClient,
    private val storageService: StorageService,
    private val restProperties: RestProperties,
    private val newsResourceManagerService: NewsResourceManagerService,
    private val newsResourceManagerArticleService: NewsResourceManagerArticleService
) {

    // 同步OA公众号的新闻到数据库
    @Scheduled(cron = "0 0 * * * ?")
    fun syncNewsTask() {
        val host = restProperties.getHost("news-official-accounts-oa")
        val baseUrl = host.url + ":" + host.port
//        val ts = "2025-08-01 00:00:00"
        val ts = getTodayZero()
        val suffixData = "modifiedTime=$ts&issueTime=$ts&needContent=false"
        try {
            // 获取新闻下所有的模块，用于匹配
            val blocksList: List<NewsBlock> = kSqlClient.createQuery(NewsBlock::class) {
                where(table.navigationId eq "01K0V1KP5H388PH2QR90EC1JJW")
                select(table)
            }.execute()
            val newsList = officialAccountsClient.fetchOfficialAccounts(suffixData)
            for (news in newsList.data) {
                // 30为发布，00为废弃
                if ("30".equals(news.status)) {
                    // 获取附件
                    val newsFiles = officialAccountsClient.fetchGetNewsFiles("fdId=" + news.fdId)
                    val fileIds : MutableList<String> = mutableListOf()
                    newsFiles.data.forEach {
                        val fileId = storageService.upload(URL(it))
                        fileIds.add(fileId)
                    }
                    var contentFormat : String? = null
                    // 获取富文本
                    if (news.fdLinkUrl == null || news.fdLinkUrl == "") {
                        val newsContent = officialAccountsClient.fetchGetNewsContent("fdId=" + news.fdId)
                        contentFormat = newsContent.msg?.replace("\"/ekp/", "\"$baseUrl/ekp/")
                    }
                    // 标签分割
                    val tagArr = news.tags?.split(";")?.map { t -> t.trim() }?.filter { t -> t.isNotEmpty() }?.distinct()
                    val matchedBlocks = tagArr
                        ?.mapNotNull { tagArr -> blocksList.firstOrNull { b -> b.title == tagArr } }
                        ?: emptyList()
                    val finalBlocks = matchedBlocks.ifEmpty {
                        listOf(blocksList.first { b -> b.title == "其他" })
                    }

                    val saveNews = NewsResourceManager {
                        id = news.fdId
                        fdId = news.fdId
                        title = news.docTitle
                        author = news.docAuthor
                        issuer = news.docAuthor
                        issueDept = news.deptName
                        issueUnit = news.deptName
                        contentBrief = news.description
                        contentType = ContentType.CONTENT
                        fdLinkUrl = news.fdLinkUrl
                        issueTime = news.modifiedTime
                        attachments = fileIds.map { item ->
                            NewsResourceManagerAttachment {
                                fileId = item
                            }
                        }
                        attachments = ArrayList()
                        newsType = NewsType.OA
                        content = contentFormat
                        readingNum = 0
                        tags = news.tags?.split(";")?.toList()
                    }
                    // 保存新闻
                    val newsResourceManager = newsResourceManagerService.save(SaveNewsResourceManager(saveNews))
                    // 新闻绑定模块
                    for (block in finalBlocks) {
                        val saveArticle = NewsResourceManagerArticle {
                            newsId = newsResourceManager.id
                            newsBlockId = block.id
                        }
                        newsResourceManagerArticleService.saveArticles(SaveNewsArticles(saveArticle))
                    }
                } else {
                    newsResourceManagerService.delete(news.fdId)
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }



    // 获取当天整点的字符串
    private fun getTodayZero(): String {
        return LocalDateTime.now().minusHours(1).withMinute(0).withSecond(0).withNano(0)
            .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
    }




}