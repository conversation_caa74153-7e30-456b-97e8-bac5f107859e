package zone.loong.cube.interior.service

import zone.loong.cube.interior.model.dto.InteriorPositionsSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorPositions
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.interior.model.InteriorPositions

interface InteriorPositionsService {
    fun fetchPositions(pageQuery: PageQuery<InteriorPositionsSpecification>): Page<InteriorPositions>
    fun save(saveInteriorPositions: SaveInteriorPositions): InteriorPositions
    fun delete(id: String)
    fun detail(id: String): InteriorPositions
}