package zone.loong.cube.platform.auth.controller

import org.springframework.web.bind.annotation.*
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.Group
import zone.loong.cube.platform.auth.model.Terms
import zone.loong.cube.platform.auth.model.dto.AccountWithUserInfo
import zone.loong.cube.platform.auth.model.dto.GroupAuthorization
import zone.loong.cube.platform.auth.model.dto.GroupSpecification
import zone.loong.cube.platform.auth.service.GroupService

@RestController
@RequestMapping("/auth/group")
class GroupController(private val groupService: GroupService) {
    @PostMapping("/page")
    fun fetchGroup(@RequestBody pageQuery: PageQuery<GroupSpecification>): Page<Group> {
        return groupService.fetchGroup(pageQuery)
    }

    @PostMapping("/save")
    fun saveGroup(@RequestBody group: Group): Group {
        return groupService.saveGroup(group)
    }


    @GetMapping("/accounts")
    fun fetchAccounts(@RequestParam("groupId") groupId: String): List<AccountWithUserInfo> {
        return groupService.fetchAccounts(groupId)
    }

    @GetMapping("/terms")
    fun fetchTerms(@RequestParam("groupId") groupId: String): Terms? {
        return groupService.fetchTerms(groupId)
    }

    @GetMapping("/permissions")
    fun fetchPermissions(@RequestParam("groupId") groupId: String): List<String> {
        return groupService.fetchPermissions(groupId)
    }

    // 根据ID获取当前用户组信息
    @PostMapping("/getGroup/{id}")
    fun getGroupById(@PathVariable("id") id: String): Group {
        return groupService.getGroupById(id)
    }

    //授权
    @PostMapping("/authorization")
    fun authorization(@RequestBody groupAuthorization: GroupAuthorization) {
        groupService.authorize(groupAuthorization)
    }
}