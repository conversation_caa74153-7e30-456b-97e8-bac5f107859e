package zone.loong.cube.framework.storage.local.autoconfigure

import jakarta.annotation.PostConstruct
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.context.annotation.Import
import zone.loong.cube.framework.kernel.log.annotation.Log
import zone.loong.cube.framework.kernel.log.annotation.Log.Companion.log
import zone.loong.cube.framework.storage.local.configure.LocalStorageConfigure

@AutoConfiguration
@Import(LocalStorageConfigure::class)
@Log
class LocalStorageAutoconfigure {
    @PostConstruct
    fun postConstruct() {
        //增加颜色
        log.info("[CUBE FRAMEWORK] MODULE [STORAGE-LOCAL] LOAD [SUCCESS]")
    }
}