package zone.loong.cube.cases.product.controller

import jakarta.servlet.http.HttpServletResponse
import org.springframework.web.bind.annotation.*
import zone.loong.cube.cases.product.log.ProductAttachmentLogFetch
import zone.loong.cube.cases.product.log.ProductCaseFetch
import zone.loong.cube.cases.product.model.ProductCase
import zone.loong.cube.cases.product.model.dto.CaseViewWithName
import zone.loong.cube.cases.product.model.dto.SaveCase
import zone.loong.cube.cases.product.model.elasticsearch.ESProductCase
import zone.loong.cube.cases.product.service.ProductCasesService
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.annotation.ModuleLog

@RestController
@RequestMapping("/product/cases")
class ProductCasesController(private val casesService: ProductCasesService) {

    @PostMapping()
    fun fetchCases(@RequestBody pageQuery: PageQuery<ESSpecification>): ESPage<ESProductCase> {
        return casesService.fetchCases(pageQuery)
    }

    @GetMapping("/{id}")
    @ModuleLog(
        workspace = "产品库",
        module = "产品",
        operation = "预览",
        identity = "#id",
        fetch = ProductCaseFetch::class
    )
    fun detail(@PathVariable("id") id: String): CaseViewWithName {
        return casesService.detail(id)
    }


    @PostMapping("/save")
    fun save(@RequestBody saveCase: SaveCase): ProductCase {
        return casesService.save(saveCase)
    }

    @GetMapping("/download")
    @ModuleLog(
        workspace = "产品库",
        module = "产品附件",
        operation = "下载",
        identity = "#id",
        fetch = ProductAttachmentLogFetch::class
    )
    fun download(@RequestParam("attachmentId") id: String, response: HttpServletResponse) {
        return casesService.download(id, response)
    }

    @GetMapping("/delete")
    fun delete(@RequestParam("id") id: String) {
        return casesService.delete(id)
    }


    @GetMapping("/sync")
    fun sync() {
        casesService.sync()
    }
}