package zone.loong.cube.framework.storage.preview.configure

import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.stereotype.Component
import zone.loong.cube.framework.storage.kernel.service.FileRecorder
import zone.loong.cube.framework.storage.preview.StoragePreviewProperties
import zone.loong.cube.framework.storage.preview.service.KKFileViewClient
import zone.loong.cube.framework.storage.preview.service.PreviewService
import zone.loong.cube.framework.storage.preview.service.impl.PreviewServiceImpl

@Component
@EnableConfigurationProperties(StoragePreviewProperties::class)
class StoragePreviewConfigure {
    @Bean
    fun previewService(storagePreviewProperties: StoragePreviewProperties,fileRecorder: FileRecorder, kkFileViewClient: KKFileViewClient): PreviewService {
        return PreviewServiceImpl( storagePreviewProperties,fileRecorder, kkFileViewClient)
    }
}