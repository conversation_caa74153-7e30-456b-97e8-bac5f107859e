package zone.loong.cube.platform.auth.model

import com.fasterxml.jackson.annotation.JsonValue
import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.auth.security.model.Account
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.platform.organization.model.User
import java.time.LocalDateTime

@Entity
@Table(name = "AUTH_ACCOUNT")
interface Account : Account, AuditAware {
    @Key
    override val userName: String

    override val password: String

    //开始生效日期
    override val effectiveFrom: LocalDateTime

    //结束日期
    override val effectiveUntil: LocalDateTime

    @ManyToOne
    val user: User?

    @OneToMany(mappedBy = "account")
    override val banRecords: List<BanRecord>

    @OneToMany(mappedBy = "account")
    override val accessRecords: List<AccessRecord>

    @ManyToMany
    @JoinTable(name = "AUTH_ROLE_ACCOUNT_MAPPING")
    override val roles: List<Role>

    @ManyToMany
    @JoinTable(name = "AUTH_GROUP_ACCOUNT_MAPPING")
    override val groups: List<Group>

    @Transient
    override val effective: Boolean

    @Serialized
    val preference: Preference?

}

class Preference {
    var workspace: String = ""
    var theme: ThemeMode = ThemeMode.LIGHT
}

enum class ThemeMode(@JsonValue val value: String) {
    LIGHT("LIGHT"),
    DARK("DARK"),
    SYSTEM("SYSTEM")
}