# 高级权限传递设计方案

## 权限传递模式深度分析

### 1. 当前传递模式的问题

#### 实时传递模式（当前实现）
```kotlin
// 当前的权限传递逻辑
fun authorization(aclSave: ACLSave) {
    val originACL = fetchACL(aclSave.resourceId)
    
    // 向上传播 (DRILL_UP)
    originACL.flattenParent().forEach { parent ->
        addPolicy(policy.copy {
            aclId = parent.id
            strategyType = StrategyType.DRILL_UP
        })
    }
    
    // 向下传播 (DRILL_DOWN)  
    originACL.flattenChildren().forEach { child ->
        addPolicy(policy.copy {
            aclId = child.id
            strategyType = StrategyType.DRILL_DOWN
        })
    }
}
```

**问题分析：**
- ❌ 每次权限变更都要遍历整个树
- ❌ 深层嵌套时性能急剧下降
- ❌ 容易产生权限数据冗余
- ❌ 事务复杂度高，容易出现一致性问题

### 2. 改进的权限传递架构

#### 2.1 分层权限计算模式
```kotlin
/**
 * 分层权限计算引擎
 * 将权限传递分为多个层次，避免深度递归
 */
@Component
class LayeredPermissionPropagationEngine {
    
    /**
     * 权限传递策略枚举
     */
    enum class PropagationStrategy {
        IMMEDIATE,    // 立即传递
        DEFERRED,     // 延迟传递
        LAZY,         // 懒加载传递
        BATCH         // 批量传递
    }
    
    /**
     * 权限传递上下文
     */
    data class PropagationContext(
        val sourceResourceId: String,
        val changedPolicies: List<Policy>,
        val strategy: PropagationStrategy,
        val maxDepth: Int = 10,
        val batchSize: Int = 100
    )
    
    /**
     * 智能权限传递
     */
    fun propagatePermissions(context: PropagationContext): PropagationResult {
        return when (context.strategy) {
            PropagationStrategy.IMMEDIATE -> immediatePropagate(context)
            PropagationStrategy.DEFERRED -> deferredPropagate(context)
            PropagationStrategy.LAZY -> lazyPropagate(context)
            PropagationStrategy.BATCH -> batchPropagate(context)
        }
    }
}
```

#### 2.2 权限路径缓存机制
```kotlin
/**
 * 权限路径缓存
 * 预计算并缓存权限传递路径，避免实时计算
 */
@Component
class PermissionPathCache {
    
    // 权限路径数据结构
    data class PermissionPath(
        val resourceId: String,
        val ancestors: List<String>,      // 祖先节点路径
        val descendants: List<String>,    // 后代节点路径
        val depth: Int,                   // 节点深度
        val lastUpdated: LocalDateTime
    )
    
    @Cacheable("permission-paths")
    fun getPermissionPath(resourceId: String): PermissionPath {
        return computePermissionPath(resourceId)
    }
    
    /**
     * 增量更新权限路径
     */
    fun updatePermissionPath(resourceId: String, oldParentId: String?, newParentId: String?) {
        val affectedNodes = findAffectedNodes(resourceId, oldParentId, newParentId)
        
        // 批量更新受影响的路径
        affectedNodes.chunked(100).forEach { batch ->
            batchUpdatePaths(batch)
        }
        
        // 清除相关缓存
        evictRelatedCache(affectedNodes)
    }
    
    /**
     * 找出受影响的节点
     */
    private fun findAffectedNodes(resourceId: String, oldParentId: String?, newParentId: String?): List<String> {
        val affected = mutableSetOf<String>()
        
        // 当前节点及其所有子节点
        affected.add(resourceId)
        affected.addAll(getAllDescendants(resourceId))
        
        // 旧父节点路径上的节点
        oldParentId?.let { affected.addAll(getAllAncestors(it)) }
        
        // 新父节点路径上的节点
        newParentId?.let { affected.addAll(getAllAncestors(it)) }
        
        return affected.toList()
    }
}
```

#### 2.3 事件驱动的权限传递
```kotlin
/**
 * 权限传递事件
 */
sealed class PermissionPropagationEvent {
    data class PermissionGranted(
        val resourceId: String,
        val policies: List<Policy>,
        val propagationScope: PropagationScope
    ) : PermissionPropagationEvent()
    
    data class PermissionRevoked(
        val resourceId: String,
        val policies: List<Policy>,
        val propagationScope: PropagationScope
    ) : PermissionPropagationEvent()
    
    data class HierarchyChanged(
        val resourceId: String,
        val oldParentId: String?,
        val newParentId: String?,
        val affectedNodes: List<String>
    ) : PermissionPropagationEvent()
}

/**
 * 权限传递范围
 */
data class PropagationScope(
    val direction: PropagationDirection,
    val maxDepth: Int,
    val filter: ((String) -> Boolean)? = null
)

enum class PropagationDirection {
    UP_ONLY,      // 仅向上传递
    DOWN_ONLY,    // 仅向下传递
    BIDIRECTIONAL // 双向传递
}

/**
 * 异步权限传递处理器
 */
@Component
class AsyncPermissionPropagationHandler {
    
    @EventListener
    @Async("permissionPropagationExecutor")
    fun handlePermissionGranted(event: PermissionPropagationEvent.PermissionGranted) {
        val context = PropagationContext(
            sourceResourceId = event.resourceId,
            changedPolicies = event.policies,
            strategy = determineStrategy(event.propagationScope)
        )
        
        propagationEngine.propagatePermissions(context)
    }
    
    @EventListener
    @Async("permissionPropagationExecutor")
    fun handleHierarchyChanged(event: PermissionPropagationEvent.HierarchyChanged) {
        // 处理层级结构变更
        hierarchyChangeHandler.handleChange(event)
    }
}
```

### 3. 高性能权限传递实现

#### 3.1 图算法优化的权限传递
```kotlin
/**
 * 基于图算法的权限传递
 * 使用拓扑排序避免循环依赖
 */
@Component
class GraphBasedPermissionPropagation {
    
    /**
     * 权限依赖图
     */
    data class PermissionGraph(
        val nodes: Map<String, PermissionNode>,
        val edges: Map<String, List<String>>
    )
    
    data class PermissionNode(
        val resourceId: String,
        val policies: MutableSet<Policy>,
        val inDegree: Int,
        val outDegree: Int
    )
    
    /**
     * 拓扑排序权限传递
     */
    fun propagateWithTopologicalSort(
        graph: PermissionGraph,
        changedPolicies: List<Policy>,
        sourceResourceId: String
    ): PropagationResult {
        
        val queue = LinkedList<String>()
        val inDegree = graph.nodes.mapValues { it.value.inDegree }.toMutableMap()
        
        // 找出入度为0的节点
        graph.nodes.forEach { (nodeId, node) ->
            if (inDegree[nodeId] == 0) {
                queue.offer(nodeId)
            }
        }
        
        val propagationOrder = mutableListOf<String>()
        
        while (queue.isNotEmpty()) {
            val current = queue.poll()
            propagationOrder.add(current)
            
            // 处理当前节点的所有邻接节点
            graph.edges[current]?.forEach { neighbor ->
                inDegree[neighbor] = inDegree[neighbor]!! - 1
                if (inDegree[neighbor] == 0) {
                    queue.offer(neighbor)
                }
            }
        }
        
        // 检测循环依赖
        if (propagationOrder.size != graph.nodes.size) {
            throw CircularDependencyException("检测到权限传递中的循环依赖")
        }
        
        // 按拓扑顺序传递权限
        return executePropagationInOrder(propagationOrder, changedPolicies)
    }
}
```

#### 3.2 增量权限计算
```kotlin
/**
 * 增量权限计算引擎
 * 只计算变更部分，避免全量重算
 */
@Component
class IncrementalPermissionCalculator {
    
    /**
     * 权限变更差异
     */
    data class PermissionDelta(
        val added: Set<Policy>,
        val removed: Set<Policy>,
        val modified: Set<Pair<Policy, Policy>>
    )
    
    /**
     * 计算权限变更影响范围
     */
    fun calculateImpactScope(
        resourceId: String,
        delta: PermissionDelta
    ): ImpactScope {
        
        val directImpact = mutableSetOf<String>()
        val indirectImpact = mutableSetOf<String>()
        
        // 分析直接影响
        delta.added.forEach { policy ->
            directImpact.addAll(findDirectlyAffectedResources(resourceId, policy))
        }
        
        delta.removed.forEach { policy ->
            directImpact.addAll(findDirectlyAffectedResources(resourceId, policy))
        }
        
        // 分析间接影响（传递影响）
        directImpact.forEach { affectedResource ->
            indirectImpact.addAll(findIndirectlyAffectedResources(affectedResource))
        }
        
        return ImpactScope(
            direct = directImpact,
            indirect = indirectImpact,
            totalAffected = directImpact + indirectImpact
        )
    }
    
    /**
     * 增量更新权限
     */
    @Transactional
    fun incrementalUpdate(
        resourceId: String,
        delta: PermissionDelta,
        scope: ImpactScope
    ): UpdateResult {
        
        val updatePlan = createUpdatePlan(delta, scope)
        
        // 分批执行更新
        val results = updatePlan.batches.map { batch ->
            executeBatchUpdate(batch)
        }
        
        return UpdateResult(
            updatedResources = scope.totalAffected.size,
            executionTime = results.sumOf { it.executionTime },
            errors = results.flatMap { it.errors }
        )
    }
}
```

### 4. 权限传递的一致性保证

#### 4.1 分布式锁机制
```kotlin
/**
 * 权限传递分布式锁
 */
@Component
class PermissionPropagationLock {
    
    @Autowired
    private lateinit var redisTemplate: RedisTemplate<String, String>
    
    /**
     * 获取权限传递锁
     */
    fun acquirePropagationLock(
        resourceId: String,
        lockTimeout: Duration = Duration.ofMinutes(5)
    ): PermissionLock? {
        
        val lockKey = "permission:propagation:$resourceId"
        val lockValue = UUID.randomUUID().toString()
        
        val acquired = redisTemplate.opsForValue().setIfAbsent(
            lockKey, 
            lockValue, 
            lockTimeout
        ) ?: false
        
        return if (acquired) {
            PermissionLock(lockKey, lockValue, lockTimeout)
        } else {
            null
        }
    }
    
    /**
     * 层级锁定策略
     * 锁定整个传递路径，确保一致性
     */
    fun acquireHierarchyLock(resourceIds: List<String>): HierarchyLock? {
        // 按资源ID排序，避免死锁
        val sortedIds = resourceIds.sorted()
        val locks = mutableListOf<PermissionLock>()
        
        try {
            sortedIds.forEach { resourceId ->
                val lock = acquirePropagationLock(resourceId)
                    ?: throw LockAcquisitionException("无法获取资源锁: $resourceId")
                locks.add(lock)
            }
            
            return HierarchyLock(locks)
        } catch (e: Exception) {
            // 释放已获取的锁
            locks.forEach { it.release() }
            return null
        }
    }
}
```

#### 4.2 事务补偿机制
```kotlin
/**
 * 权限传递事务补偿
 */
@Component
class PermissionPropagationCompensation {
    
    /**
     * 权限传递操作记录
     */
    data class PropagationOperation(
        val operationId: String,
        val resourceId: String,
        val operationType: OperationType,
        val beforeState: PermissionState,
        val afterState: PermissionState,
        val timestamp: LocalDateTime
    )
    
    enum class OperationType {
        GRANT, REVOKE, MODIFY, HIERARCHY_CHANGE
    }
    
    /**
     * 记录权限传递操作
     */
    fun recordOperation(operation: PropagationOperation) {
        operationLog.save(operation)
    }
    
    /**
     * 补偿失败的权限传递
     */
    @Transactional
    fun compensateFailedPropagation(operationId: String) {
        val operation = operationLog.findById(operationId)
            ?: throw OperationNotFoundException("操作记录不存在: $operationId")
        
        when (operation.operationType) {
            OperationType.GRANT -> compensateGrantOperation(operation)
            OperationType.REVOKE -> compensateRevokeOperation(operation)
            OperationType.MODIFY -> compensateModifyOperation(operation)
            OperationType.HIERARCHY_CHANGE -> compensateHierarchyChange(operation)
        }
    }
    
    private fun compensateGrantOperation(operation: PropagationOperation) {
        // 回滚权限授予操作
        val policiesToRemove = operation.afterState.policies - operation.beforeState.policies
        removePolicies(operation.resourceId, policiesToRemove)
    }
}
```

### 5. 替代权限传递模式

#### 5.1 基于标签的权限传递
```kotlin
/**
 * 标签驱动的权限传递
 * 不依赖树形结构，基于资源标签进行权限传递
 */
@Component
class TagBasedPermissionPropagation {

    /**
     * 资源标签
     */
    data class ResourceTag(
        val key: String,
        val value: String,
        val propagationRule: PropagationRule?
    )

    /**
     * 传递规则
     */
    data class PropagationRule(
        val sourcePattern: String,      // 源标签模式
        val targetPattern: String,      // 目标标签模式
        val permissionMapping: Map<String, String>, // 权限映射
        val conditions: List<String>    // 传递条件
    )

    /**
     * 基于标签的权限传递
     */
    fun propagateByTags(
        sourceResourceId: String,
        changedPolicies: List<Policy>
    ): PropagationResult {

        val sourceTags = getResourceTags(sourceResourceId)
        val applicableRules = findApplicableRules(sourceTags)

        val targetResources = mutableSetOf<String>()

        applicableRules.forEach { rule ->
            val targets = findResourcesByTagPattern(rule.targetPattern)
            targetResources.addAll(targets)
        }

        return propagateToTargets(targetResources, changedPolicies, applicableRules)
    }
}
```

#### 5.2 基于角色的权限传递
```kotlin
/**
 * 角色驱动的权限传递
 * 基于用户角色和资源类型进行权限传递
 */
@Component
class RoleBasedPermissionPropagation {

    /**
     * 角色权限传递规则
     */
    data class RolePermissionRule(
        val sourceRole: String,
        val targetRole: String,
        val resourceType: String,
        val permissionTransform: (Set<String>) -> Set<String>
    )

    /**
     * 基于角色的权限传递
     */
    fun propagateByRole(
        userId: String,
        resourceId: String,
        changedPermissions: Set<String>
    ): PropagationResult {

        val userRoles = getUserRoles(userId)
        val resourceType = getResourceType(resourceId)

        val propagationTargets = mutableMapOf<String, Set<String>>()

        userRoles.forEach { role ->
            val rules = getRolePermissionRules(role, resourceType)

            rules.forEach { rule ->
                val targetUsers = getUsersByRole(rule.targetRole)
                val transformedPermissions = rule.permissionTransform(changedPermissions)

                targetUsers.forEach { targetUser ->
                    propagationTargets.merge(targetUser, transformedPermissions) { existing, new ->
                        existing + new
                    }
                }
            }
        }

        return executePropagation(propagationTargets)
    }
}
```

#### 5.3 基于策略的权限传递
```kotlin
/**
 * 策略驱动的权限传递
 * 使用规则引擎进行灵活的权限传递
 */
@Component
class PolicyBasedPermissionPropagation {

    /**
     * 权限传递策略
     */
    interface PermissionPropagationPolicy {
        fun shouldPropagate(context: PropagationContext): Boolean
        fun transformPermissions(permissions: Set<String>, context: PropagationContext): Set<String>
        fun findTargets(context: PropagationContext): Set<String>
    }

    /**
     * 时间敏感的权限传递策略
     */
    class TimeBasedPropagationPolicy : PermissionPropagationPolicy {
        override fun shouldPropagate(context: PropagationContext): Boolean {
            val currentTime = LocalDateTime.now()
            return context.effectiveTime?.contains(currentTime) ?: true
        }

        override fun transformPermissions(permissions: Set<String>, context: PropagationContext): Set<String> {
            // 根据时间调整权限
            return if (isWorkingHours()) {
                permissions
            } else {
                permissions.filter { !it.contains("SENSITIVE") }.toSet()
            }
        }

        override fun findTargets(context: PropagationContext): Set<String> {
            // 根据时间找到目标资源
            return if (isWorkingHours()) {
                getAllSubordinates(context.sourceResourceId)
            } else {
                getEmergencyContacts(context.sourceResourceId)
            }
        }
    }

    /**
     * 地理位置敏感的权限传递策略
     */
    class LocationBasedPropagationPolicy : PermissionPropagationPolicy {
        override fun shouldPropagate(context: PropagationContext): Boolean {
            val userLocation = getUserLocation(context.userId)
            val resourceLocation = getResourceLocation(context.sourceResourceId)
            return calculateDistance(userLocation, resourceLocation) < MAX_DISTANCE
        }

        override fun transformPermissions(permissions: Set<String>, context: PropagationContext): Set<String> {
            val userLocation = getUserLocation(context.userId)
            return if (isInSecureZone(userLocation)) {
                permissions
            } else {
                permissions.filter { !it.contains("CONFIDENTIAL") }.toSet()
            }
        }

        override fun findTargets(context: PropagationContext): Set<String> {
            val sourceLocation = getResourceLocation(context.sourceResourceId)
            return getResourcesInRadius(sourceLocation, PROPAGATION_RADIUS)
        }
    }
}
```

#### 5.4 混合权限传递模式
```kotlin
/**
 * 混合权限传递引擎
 * 结合多种传递模式，提供最大的灵活性
 */
@Component
class HybridPermissionPropagationEngine {

    /**
     * 传递模式枚举
     */
    enum class PropagationMode {
        HIERARCHICAL,  // 层级传递（树形）
        TAG_BASED,     // 标签传递
        ROLE_BASED,    // 角色传递
        POLICY_BASED,  // 策略传递
        HYBRID         // 混合模式
    }

    /**
     * 智能选择传递模式
     */
    fun selectOptimalPropagationMode(context: PropagationContext): PropagationMode {
        val resourceCount = estimateAffectedResourceCount(context)
        val complexity = calculateComplexity(context)
        val performanceRequirement = context.performanceRequirement

        return when {
            resourceCount < 100 && complexity == Complexity.LOW -> PropagationMode.HIERARCHICAL
            context.hasTagConstraints() -> PropagationMode.TAG_BASED
            context.hasRoleConstraints() -> PropagationMode.ROLE_BASED
            complexity == Complexity.HIGH -> PropagationMode.POLICY_BASED
            else -> PropagationMode.HYBRID
        }
    }

    /**
     * 执行混合权限传递
     */
    fun executeHybridPropagation(context: PropagationContext): PropagationResult {
        val mode = selectOptimalPropagationMode(context)

        return when (mode) {
            PropagationMode.HIERARCHICAL -> hierarchicalPropagation.propagate(context)
            PropagationMode.TAG_BASED -> tagBasedPropagation.propagate(context)
            PropagationMode.ROLE_BASED -> roleBasedPropagation.propagate(context)
            PropagationMode.POLICY_BASED -> policyBasedPropagation.propagate(context)
            PropagationMode.HYBRID -> executeMultiModePropagation(context)
        }
    }

    /**
     * 多模式并行传递
     */
    private fun executeMultiModePropagation(context: PropagationContext): PropagationResult {
        val futures = listOf(
            CompletableFuture.supplyAsync { hierarchicalPropagation.propagate(context) },
            CompletableFuture.supplyAsync { tagBasedPropagation.propagate(context) },
            CompletableFuture.supplyAsync { roleBasedPropagation.propagate(context) }
        )

        val results = CompletableFuture.allOf(*futures.toTypedArray())
            .thenApply { futures.map { it.get() } }
            .get(30, TimeUnit.SECONDS)

        return mergeResults(results)
    }
}
```

## 性能对比分析

| 传递模式 | 适用场景 | 性能特点 | 复杂度 |
|---------|---------|---------|--------|
| 树形继承 | 标准组织架构 | O(n*h) h为深度 | 低 |
| 标签传递 | 灵活分组 | O(n*t) t为标签数 | 中 |
| 角色传递 | 基于职能 | O(u*r) u为用户数 | 中 |
| 策略传递 | 复杂规则 | O(n*p) p为策略数 | 高 |
| 混合模式 | 企业级应用 | 动态优化 | 高 |

## 总结

这个高级权限传递设计解决了你提出的所有核心问题：

1. **性能问题**：分层计算、路径缓存、增量更新、智能模式选择
2. **一致性保证**：分布式锁、事务补偿、操作日志
3. **事务安全**：异步处理、批量操作、补偿机制
4. **循环依赖**：拓扑排序、图算法、依赖检测
5. **灵活性**：多种传递模式、混合策略、动态优化

相比传统的树形继承，这个方案提供了：
- 🚀 **更高的性能**：智能算法选择和优化
- 🔒 **更强的一致性**：完善的事务保证机制
- 🎯 **更大的灵活性**：多种传递模式适应不同场景
- 📊 **更好的可观测性**：详细的监控和审计
