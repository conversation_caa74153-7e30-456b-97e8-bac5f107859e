package zone.loong.cube.cases.share.task

import com.dtflys.forest.annotation.GetRequest
import org.springframework.stereotype.Component
import zone.loong.cube.framework.http.forest.annotation.Rest

class Response<T> {
    var code: Int = 200
    var msg: String = ""
    var data: List<T> = emptyList()
    fun success(): <PERSON><PERSON><PERSON> {
        return code == 200
    }
}


@Rest(code = "ding-talk-sync")
@Component
interface DingTalkSyncClient {
    /**
     * 返回共享组用户
     */
    @GetRequest("/ding/getUserListFromCorrespondent")
    fun fetchShareGroupUser(): Response<String>
}