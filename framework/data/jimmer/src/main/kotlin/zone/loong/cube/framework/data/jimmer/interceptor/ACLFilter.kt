package zone.loong.cube.framework.data.jimmer.interceptor

import org.babyfish.jimmer.sql.kt.ast.expression.*
import org.babyfish.jimmer.sql.kt.filter.KFilter
import org.babyfish.jimmer.sql.kt.filter.KFilterArgs
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.model.*
import zone.loong.cube.framework.data.kernel.constants.ACLConstants
import zone.loong.cube.framework.data.kernel.model.PolicyType
import zone.loong.cube.framework.data.kernel.service.AuthProvider

@Service
class ACLFilter(private val authProvider: AuthProvider) : KFilter<ACLAware> {

    override fun filter(args: KFilterArgs<ACLAware>) {
        val userRoles = authProvider.getUserRoles()
        val userGroups = authProvider.getUserGroups()
        if (userGroups.any {
                it.equals(
                    ACLConstants.GROUP_SUPER_ADMIN,
                    true
                )
            } || userRoles.any { it.equals(ACLConstants.ROLE_SUPER_ADMIN, true) }) {
            return
        }
        val userIdentity = authProvider.getUserIdentity()
        val userOrganizationIds = authProvider.getUserOrganizationIds()
        args.apply {
            where(exists(subQuery(Policy::class) {
                where(
                    or(
                        and(
                            table.policyType eq PolicyType.ORGANIZATION, table.value valueIn userOrganizationIds
                        ), and(
                            table.policyType eq PolicyType.USER, table.value eq userIdentity
                        ), and(
                            table.policyType eq PolicyType.ROLE, table.value valueIn userRoles
                        ), and(
                            table.policyType eq PolicyType.GROUP, table.value valueIn userGroups
                        )
                    ),table.aclId eq parentTable.aclId
                )
                select(table.id)
            }))


        }
    }

//    override fun getParameters(): SortedMap<String, Any> {
//        authProvider.getUserIdentity().let {
//            return sortedMapOf("identity" to it)
//        }
//    }
//
//    override fun isAffectedBy(e: EntityEvent<*>): Boolean {
//        return e.isChanged(ACLAware::acl)
//    }
}