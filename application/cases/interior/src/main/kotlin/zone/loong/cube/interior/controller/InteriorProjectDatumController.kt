package zone.loong.cube.interior.controller

import jakarta.servlet.http.HttpServletResponse
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.interior.model.InteriorProjectDatum
import zone.loong.cube.interior.model.dto.InteriorProjectDatumSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorProjectDatum
import zone.loong.cube.interior.service.InteriorProjectDatumService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

@RestController
@RequestMapping("/interior/projectDatum")
class InteriorProjectDatumController(
    private val interiorProjectDatumService: InteriorProjectDatumService
) {


    @PostMapping("/page")
    fun fetchProjectDatum(@RequestBody pageQuery: PageQuery<InteriorProjectDatumSpecification>): Page<InteriorProjectDatum> {
        return interiorProjectDatumService.fetchProjectDatum(pageQuery)
    }

    @PostMapping("/save")
    fun save(@RequestBody saveInteriorProject: SaveInteriorProjectDatum): InteriorProjectDatum {
        return interiorProjectDatumService.save(saveInteriorProject)
    }

    @DeleteMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        interiorProjectDatumService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): InteriorProjectDatum {
        return interiorProjectDatumService.detail(id)
    }

    @GetMapping("/download")
    fun download(@RequestParam("fileId") id: String, response: HttpServletResponse) {
        return interiorProjectDatumService.download(id, response)
    }
}