spring:
  profiles:
    active: dev
  application:
    name: doc
  servlet:
    multipart:
      max-file-size: 1000MB
      max-request-size: 1000MB
logging:
  file:
    path: logs
  level:
    tracer: TRACE
    org.elasticsearch.client.RestClient: trace
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss.SSS} %5p[%X{SOFA-TraceId},%X{SOFA-SpanId}] ----%m%n'
    file: '%d{yyyy-MM-dd HH:mm:ss.SSS} %5p[%X{SOFA-TraceId},%X{SOFA-SpanId}] ----%m%n'
jimmer:
  language: kotlin
  show-sql: true
  pretty-sql: true
  client:
    openapi:
      path: /openapi.yml
      ui-path: /openapi.html
      properties:
        info:
          title: 案例库管理
          description: 案例库管理API
          version: 1.0
    ts:
      path: /zip
  trigger-type: transaction_only
cube:
  security:
    exclude:
      - /resource/**
      - /auth/login
forest:
  read-timeout: 99999999
