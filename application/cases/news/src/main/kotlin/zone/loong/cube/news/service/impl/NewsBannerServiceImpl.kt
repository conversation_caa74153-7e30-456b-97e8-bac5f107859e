package zone.loong.cube.news.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Service
import zone.loong.cube.news.model.NewsBanner
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.dto.NewsBannerSpecification
import zone.loong.cube.news.model.dto.SaveNewsBanner
import zone.loong.cube.news.model.fetchBy
import zone.loong.cube.news.service.NewsBannerService

@Service
class NewsBannerServiceImpl(
    private val kSqlClient: KSqlClient
) : NewsBannerService {

    override fun fetchBanners(pageQuery: PageQuery<NewsBannerSpecification>): Page<NewsBanner> {
        return kSqlClient.page(pageQuery){
            select(table.fetchBy {
                allScalarFields()
                image {
                    originalFilename()
                    metadata()
                    url()
                }
                navigation {
                    allScalarFields()
                }
            })
        }
    }

    override fun save(saveNewsBanner: SaveNewsBanner): NewsBanner {
        return (saveNewsBanner.id?.let {
            kSqlClient.entities.saveCommand(saveNewsBanner, SaveMode.UPSERT).execute().modifiedEntity
        }?:let{
            kSqlClient.entities.saveCommand(saveNewsBanner, SaveMode.INSERT_ONLY).execute().modifiedEntity
        })
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(NewsBanner::class, id)
    }

    override fun detail(id: String): NewsBanner {
        return kSqlClient.findById(NewsBanner::class, id) ?: throw Exception("该banner未找到")
    }


}