//package zone.loong.cube
//
//import org.junit.jupiter.api.Test
//import org.springframework.beans.factory.annotation.Autowired
//import org.springframework.boot.test.context.SpringBootTest
//import zone.loong.cube.framework.data.elasticsearch.service.SearchIndexService
//import zone.loong.cube.news.model.elasticsearch.ESNewsDispersion
//
//@SpringBootTest
//class ApplicationTest(
//    @Autowired private val searchIndexService: SearchIndexService
//) {
//    @Test
//    fun createIndex() {
//        searchIndexService.dropAndCreate(ESNewsDispersion::class)
//    }
//}
//
//
