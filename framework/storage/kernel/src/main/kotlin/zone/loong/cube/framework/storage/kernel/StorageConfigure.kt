package zone.loong.cube.framework.storage.kernel

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.dromara.x.file.storage.core.FileStorageProperties
import org.dromara.x.file.storage.core.FileStorageProperties.*
import org.dromara.x.file.storage.core.FileStorageService
import org.dromara.x.file.storage.core.FileStorageServiceBuilder
import org.dromara.x.file.storage.core.aspect.FileStorageAspect
import org.dromara.x.file.storage.core.file.FileWrapperAdapter
import org.dromara.x.file.storage.core.platform.FileStorage
import org.dromara.x.file.storage.core.platform.FileStorageClientFactory
import org.dromara.x.file.storage.core.tika.ContentTypeDetect
import org.dromara.x.file.storage.core.tika.DefaultTikaFactory
import org.dromara.x.file.storage.core.tika.TikaContentTypeDetect
import org.dromara.x.file.storage.core.tika.TikaFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.ApplicationContext
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.event.ContextRefreshedEvent
import org.springframework.context.event.EventListener
import zone.loong.cube.framework.storage.kernel.service.FileRecorder
import zone.loong.cube.framework.storage.kernel.wrapper.MultipartFileWrapperAdapter
import java.util.function.Consumer


@Configuration
@EnableConfigurationProperties(value = [StorageProperties::class])
@Suppress("UNCHECKED_CAST")
class StorageConfigure(
    private val applicationContext: ApplicationContext,
    private val storageProperties: StorageProperties
) {
    @Bean
    fun fileStorageProperties(configs: List<BaseConfig>): FileStorageProperties {
        val fileStorageProperties = FileStorageProperties().apply {
            defaultPlatform = storageProperties.defaultPlatform
            thumbnailSuffix = storageProperties.thumbnailSuffix
            uploadNotSupportMetadataThrowException = storageProperties.uploadNotSupportMetadataThrowException
            uploadNotSupportAclThrowException = storageProperties.uploadNotSupportAclThrowException
            copyNotSupportMetadataThrowException = storageProperties.copyNotSupportMetadataThrowException
            copyNotSupportAclThrowException = storageProperties.copyNotSupportAclThrowException
            moveNotSupportMetadataThrowException = storageProperties.moveNotSupportMetadataThrowException
            moveNotSupportAclThrowException = storageProperties.moveNotSupportAclThrowException
        }
        configs.forEach {
            setConfig(fileStorageProperties, it)
        }
        return fileStorageProperties
    }

    @Bean
    @ConditionalOnMissingBean(FileRecorder::class)
    fun fileRecorder(kSqlClient: KSqlClient): FileRecorder {
        return FileRecorder(kSqlClient)
    }
    /**
     * Tika 工厂类型，用于识别上传的文件的 MINE
     */
    @Bean
    @ConditionalOnMissingBean(TikaFactory::class)
    fun tikaFactory(): TikaFactory {
        return DefaultTikaFactory()
    }

    /**
     * 识别文件的 MIME 类型
     */
    @Bean
    @ConditionalOnMissingBean(ContentTypeDetect::class)
    fun contentTypeDetect(tikaFactory: TikaFactory): ContentTypeDetect {
        return TikaContentTypeDetect(tikaFactory)
    }

    @Bean(destroyMethod = "destroy")
    fun fileStorageService(
        properties: FileStorageProperties,
        fileRecorder: FileRecorder,
        @Autowired(required = false) fileStorageLists: MutableList<List<FileStorage>>?,
        @Autowired(required = false) aspectList: MutableList<FileStorageAspect>?,
        @Autowired(required = false) fileWrapperAdapterList: MutableList<FileWrapperAdapter>?,
        contentTypeDetect: ContentTypeDetect,
        @Autowired(required = false) clientFactoryList: MutableList<List<FileStorageClientFactory<*>>>?
    ): FileStorageService {

        val builder = FileStorageServiceBuilder.create(properties)
            .setFileRecorder(fileRecorder)
            .setAspectList(aspectList ?: mutableListOf())
            .setContentTypeDetect(contentTypeDetect)
            .setFileWrapperAdapterList(fileWrapperAdapterList ?: mutableListOf())
            .setClientFactoryList(clientFactoryList ?: mutableListOf())

        fileStorageLists?.forEach(Consumer { storageList: List<FileStorage?>? -> builder.addFileStorage(storageList) })

        builder.addByteFileWrapperAdapter()
        builder.addUriFileWrapperAdapter()
        builder.addInputStreamFileWrapperAdapter()
        builder.addLocalFileWrapperAdapter()
        builder.addHttpServletRequestFileWrapperAdapter()
        builder.addFileWrapperAdapter(MultipartFileWrapperAdapter())

        return builder.build()
    }

    /**
     * 对 FileStorageService 注入自己的代理对象，不然会导致针对 FileStorageService 的代理方法不生效
     */
    @EventListener(ContextRefreshedEvent::class)
    fun onContextRefreshedEvent() {
        val service: FileStorageService = applicationContext.getBean(FileStorageService::class.java)
        service.self = service
    }


    private fun setConfig(fileStorageProperties: FileStorageProperties, config: BaseConfig) {
        when (config) {
            // 本地
            is LocalPlusConfig -> {
                val localPlus = fileStorageProperties.localPlus
                if (localPlus.isEmpty()) {
                    fileStorageProperties.setLocalPlus(listOf(config))
                } else {
                    localPlus as MutableList<LocalPlusConfig>
                    localPlus.add(config)
                }
            }
            // FTP
            is FtpConfig -> {
                val ftps = fileStorageProperties.ftp
                if (ftps.isEmpty()) {
                    fileStorageProperties.setFtp(listOf(config))
                } else {
                    ftps as MutableList<FtpConfig>
                    ftps.add(config)
                }
            }
            // MINIO
            is MinioConfig -> {
                val minio = fileStorageProperties.minio
                if (minio.isEmpty()) {
                    fileStorageProperties.setMinio(listOf(config))
                } else {
                    minio as MutableList<MinioConfig>
                    minio.add(config)
                }

            }
            // 阿里云OSS
            is AliyunOssConfig -> {
                val aliyunOss = fileStorageProperties.aliyunOss
                if (aliyunOss.isEmpty()) {
                    fileStorageProperties.setAliyunOss(listOf(config))
                } else {
                    aliyunOss as MutableList<AliyunOssConfig>
                    aliyunOss.add(config)
                }
            }
            // 七牛云KODO
            is QiniuKodoConfig -> {
                val qiniu = fileStorageProperties.qiniuKodo
                if (qiniu.isEmpty()) {
                    fileStorageProperties.setQiniuKodo(listOf(config))
                } else {
                    qiniu as MutableList<QiniuKodoConfig>
                    qiniu.add(config)
                }
            }
            // 腾讯云COS
            is TencentCosConfig -> {
                val tencentCos = fileStorageProperties.tencentCos
                if (tencentCos.isEmpty()) {
                    fileStorageProperties.setTencentCos(listOf(config))
                } else {
                    tencentCos as MutableList<TencentCosConfig>
                    tencentCos.add(config)
                }
            }

        }

    }
}