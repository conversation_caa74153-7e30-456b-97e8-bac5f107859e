package zone.loong.cube.news.controller

import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsBlock
import zone.loong.cube.news.model.NewsNewspaper
import zone.loong.cube.news.model.dto.NewsNewsPaperSpecification
import zone.loong.cube.news.model.dto.SaveNewsNewsPaper
import zone.loong.cube.news.service.NewsNewspaperService

@RestController
@RequestMapping("/news/newspaper")
class NewsNewspaperController(
    private val newsNewspaperService: NewsNewspaperService
) {

    @PostMapping("/page")
    fun fetch(@RequestBody pageQuery: PageQuery<NewsNewsPaperSpecification>): Page<NewsNewspaper> {
        return newsNewspaperService.fetchResourceManagers(pageQuery)
    }

    @GetMapping("/getNewspaperTree")
    fun fetchTree(): List<NewsNewspaper> {
        return newsNewspaperService.fetchResourceManagers()
    }

    @PostMapping("/save")
    fun save(@RequestBody saveNewsNewspaper: SaveNewsNewsPaper): NewsNewspaper {
        return newsNewspaperService.save(saveNewsNewspaper)
    }

    @DeleteMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        newsNewspaperService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): NewsNewspaper {
        return newsNewspaperService.detail(id)
    }





}