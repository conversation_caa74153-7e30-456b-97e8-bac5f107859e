//package zone.loong.cube.framework.data.jimmer.service.impl
//
//import org.babyfish.jimmer.sql.kt.KSqlClient
//import org.babyfish.jimmer.sql.kt.ast.expression.eq
//import org.babyfish.jimmer.sql.kt.ast.expression.or
//import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
//import org.springframework.context.ApplicationEventPublisher
//import org.springframework.stereotype.Service
//import org.springframework.transaction.annotation.Transactional
//import zone.loong.cube.framework.data.jimmer.PolicyTool
//import zone.loong.cube.framework.data.jimmer.event.ResourcePolicyUpdate
//import zone.loong.cube.framework.data.jimmer.model.*
//import zone.loong.cube.framework.data.jimmer.model.dto.ACLSave
//import zone.loong.cube.framework.data.jimmer.service.ACLService
//import zone.loong.cube.framework.data.kernel.TransactionSync
//import zone.loong.cube.framework.data.kernel.model.StrategyType
//import zone.loong.cube.framework.kernel.exception.BizException
//
//@Service
//class ACLServiceImpl(
//    private val kSqlClient: KSqlClient, private val applicationEventPublisher: ApplicationEventPublisher
//) : ACLService {
//    @Transactional()
//    override fun authorization(aclSave: ACLSave) {
//        val originACL: ACL = fetchACL(aclSave.resourceId)
//        val originPolicies = fetchPolicies(aclSave.resourceId)
//        val targetPolices = aclSave.policies.map { it.toEntity() }
//        val (deleted, added) = PolicyTool.diff(originPolicies, targetPolices)
//        check(deleted.isEmpty() && deleted.isEmpty()) { return }
//        val changedACL = mutableListOf(originACL)
//        val policies = added.map { policy ->
//            policy.copy {
//                aclId = originACL.id
//                //前置分类
//                with(this.policies()) {
//                    originACL.flattenParent().map { parent ->
//                        changedACL.add(parent)
//                        addBy(policy.copy {
//                            aclId = parent.id
//                            strategyType = StrategyType.DRILL_UP
//                        })
//                    }
//
//                    //后置分类
//                    originACL.flattenChildren().map { children ->
//                        changedACL.add(children)
//                        addBy(policy.copy {
//                            aclId = children.id
//                            strategyType = StrategyType.DRILL_DOWN
//                        })
//                    }
//                }
//            }
//        }
//        addPolicy(policies)
//        deleteRefPolicy(deleted.map { it.id })
//        TransactionSync.runBeforeCommit("推送授权变更") {
//            changedACL.groupBy { it.resourceType }.forEach {
//                applicationEventPublisher.publishEvent(
//                    ResourcePolicyUpdate(
//                        it.value.map { it.resourceId }, it.value[0].resourceType, it.value[0].resourceName
//                    )
//                )
//            }
//        }
//    }
//
//    private fun fetchACL(resourceId: String): ACL {
//        return kSqlClient.createQuery(ACL::class) {
//            where(table.resourceId eq resourceId)
//            select(table.fetchBy {
//                resourceId()
//                resourceType()
//                resourceName()
//                `parent*`()
//                `children*`()
//            })
//        }.fetchOneOrNull() ?: throw BizException("当前资源不存在或已被删除")
//    }
//
//    private fun fetchPolicies(resourceId: String): List<Policy> {
//        return kSqlClient.createQuery(Policy::class) {
//            where(table.acl.resourceId eq resourceId)
//            select(table.fetchBy {
//                allTableFields()
//
//            })
//        }.execute()
//
//    }
//
//    fun deleteRefPolicy(policyIds: List<String>) {
//        check(policyIds.isNotEmpty()) { return }
//        kSqlClient.createDelete(Policy::class) {
//            or(
//                table.id valueIn policyIds, table.refPolicyId valueIn policyIds
//            )
//        }.execute()
//    }
//
//
//    fun addPolicy(policies: List<Policy>) {
//        check(policies.isNotEmpty()) { return }
//        kSqlClient.entities.saveEntities(policies)
//    }
//
//}