package zone.loong.cube.platform.auth.controller

import cn.dev33.satoken.temp.SaTempUtil
import com.github.f4b6a3.ulid.Ulid
import org.springframework.web.bind.annotation.*
import zone.loong.cube.framework.auth.security.model.Token
import zone.loong.cube.framework.auth.security.service.AuthService
import zone.loong.cube.modules.auth.model.LoginParam

@RestController
@RequestMapping("/auth")
class AuthController(private val authService: AuthService) {
    @PostMapping("/login")
    fun login(@RequestBody loginParam: LoginParam): Token {
        return authService.login(loginParam.account, loginParam.password, loginParam.scenario)
    }

    @PostMapping("/token")
    fun token(): Token {
        return authService.token()
    }

    @GetMapping("/once")
    fun tempToken(): String {
        return SaTempUtil.createToken(Ulid.fast().toString(), 2)
    }

    @PostMapping("/logout")
    fun logout() {
        return authService.logout()
    }

    @PostMapping("/banAccount")
    fun banAccount(@RequestParam("userName") accountIdentity: String) {
        //永久封禁
        authService.banAccount(accountIdentity, -1, 0)
    }

    @PostMapping("/unbanAccount")
    fun unBanAccount(@RequestParam("userName") accountIdentity: String) {
        authService.unBanAccount(accountIdentity)
    }
}