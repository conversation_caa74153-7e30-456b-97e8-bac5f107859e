package zone.loong.cube.news.model

import org.babyfish.jimmer.sql.Default
import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.LogicalDeleted
import zone.loong.cube.framework.data.jimmer.model.AuditAware

/**
 * 资源目录;
 */
@Entity
interface NewsResourceCatalog : AuditAware {


    /**
     * 资源id
     */
    val resourceId: String?

    /**
     * 是否置顶
     */
    val topStatus: String?

    /**
     * 类型
     */
    val type: String?

    /**
     * 模块id
     */
    val moduleId: String?

    /**
     * 状态
     */
    val status: String?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}

