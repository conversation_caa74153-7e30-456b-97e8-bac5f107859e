package zone.loong.cube.framework.auth.security.model

import java.time.LocalDateTime

// 封禁记录
interface BanRecord {

    // 封禁等级
    val banLevel: BanLevel

    // 封禁原因
    val banReason: String

    // 封禁时间
    val banAt: LocalDateTime

    //自动解封时间
    val banUntil: LocalDateTime

    // 封禁者
    val banBy: String

    //是否解封
    val unBan: Boolean

    // 解封原因
    val unBanReason: String

    // 解封者
    val unBanBy: String

    // 解封时间
    val unBanAt: LocalDateTime
}


enum class BanLevel(val second: Long) {
    // 永久禁用 999年
    LEVEL_FOREVER(999L * 365 * 24 * 60 * 60),

    // 1分钟
    LEVEL_MINUTES_1(60),

    // 5分钟
    LEVEL_MINUTES_5(5 * 60),

    // 30分钟
    LEVEL_MINUTES_30(30 * 60),

    // 12小时
    LEVEL_HOURS_12(12 * 60 * 60),

    // 24小时
    LEVEL_DAY(24 * 60 * 60),

    // 7天
    LEVEL_WEEK(7 * 24 * 60 * 60),

    // 一个月
    LEVEL_MONTH(30 * 24 * 60 * 60),

    // 一年
    LEVEL_YEAR(365 * 24 * 60 * 60),

}
