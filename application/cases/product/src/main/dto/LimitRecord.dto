export zone.loong.cube.cases.product.model.LimitRecord
    -> package zone.loong.cube.cases.product.model.dto

specification LimitRecordSpecification {
    flat(account) {
        as(^ -> account) {
            flat(user) {
                code
                name
            }
        }
    }
}

LimitRecordView {
    tag
    counts
    account {
        user {
            code

            name
        }
    }

    attachments {
        file {
            originalFilename
            metadata
        }
    }
}