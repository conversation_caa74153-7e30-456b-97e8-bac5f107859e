package zone.loong.cube.framework.kernel.autoconfigure

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.annotation.PostConstruct
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import zone.loong.cube.framework.kernel.extension.configure.ExtensionConfiguration
import zone.loong.cube.framework.kernel.jackson.configuration.JacksonConfiguration
import zone.loong.cube.framework.kernel.log.annotation.Log
import zone.loong.cube.framework.kernel.log.annotation.Log.Companion.log

@AutoConfiguration
@Import(JacksonConfiguration::class, ExtensionConfiguration::class, CORSConfIg::class)
@Log
class FrameworkAutoConfigure {

    @Bean
    @ConditionalOnMissingBean
    fun responseAdvice(objectMapper: ObjectMapper): ResponseAdvice {
        return ResponseAdvice(objectMapper)
    }

    @PostConstruct
    fun init() {
        log.info("[CUBE FRAMEWORK] INITIALIZED")
    }
}