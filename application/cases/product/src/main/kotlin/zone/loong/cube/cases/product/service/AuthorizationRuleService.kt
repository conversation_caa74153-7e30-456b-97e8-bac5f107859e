package zone.loong.cube.cases.product.service

import zone.loong.cube.cases.product.model.AuthorizationRule
import zone.loong.cube.cases.product.model.dto.AuthorizationRuleSave
import zone.loong.cube.cases.product.model.dto.AuthorizationRuleSpecification
import zone.loong.cube.cases.product.model.dto.AuthorizationRuleView
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.Group

interface AuthorizationRuleService {
    fun fetchRules(pageQuery: PageQuery<AuthorizationRuleSpecification>): Page<AuthorizationRuleView>
    fun save(authorizationRule: AuthorizationRuleSave): AuthorizationRule
    fun detail(id: String): AuthorizationRuleView
    fun fetchRuleGroups(id: String): List<Group>
    fun delete(id: String)
}