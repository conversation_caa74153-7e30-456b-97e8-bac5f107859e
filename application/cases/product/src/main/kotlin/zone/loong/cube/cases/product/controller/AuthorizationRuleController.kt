package zone.loong.cube.cases.product.controller

import org.springframework.web.bind.annotation.*
import zone.loong.cube.cases.product.model.AuthorizationRule
import zone.loong.cube.cases.product.model.dto.AuthorizationRuleSave
import zone.loong.cube.cases.product.model.dto.AuthorizationRuleSpecification
import zone.loong.cube.cases.product.model.dto.AuthorizationRuleView
import zone.loong.cube.cases.product.service.AuthorizationRuleService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.Group

@RestController
@RequestMapping("/product/authorization-rule")
class AuthorizationRuleController(private val authorizationRuleService: AuthorizationRuleService) {

    @PostMapping
    fun fetchRules(@RequestBody query: PageQuery<AuthorizationRuleSpecification>): Page<AuthorizationRuleView> {
        return authorizationRuleService.fetchRules(query)
    }


    @PostMapping("/save")
    fun save(@RequestBody authorizationRule: AuthorizationRuleSave): AuthorizationRule {
        return authorizationRuleService.save(authorizationRule)

    }

    @GetMapping("/{id}")
    fun detail(@PathVariable("id") id: String): AuthorizationRuleView {
        return authorizationRuleService.detail(id)
    }

    @GetMapping("/groups")
    fun fetchRuleGroups(@RequestParam("id") id: String): List<Group> {
        return authorizationRuleService.fetchRuleGroups(id)
    }
}