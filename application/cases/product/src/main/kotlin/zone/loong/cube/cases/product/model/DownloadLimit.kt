package zone.loong.cube.cases.product.model

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.JoinTable
import org.babyfish.jimmer.sql.ManyToMany
import org.babyfish.jimmer.sql.Table
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.platform.auth.model.Group

@Entity
@Table(name = "CASES_PRODUCT_DOWNLOAD_LIMIT")
interface DownloadLimit : AuditAware {
    val tag:String

    @ManyToMany
    @JoinTable(name = "CASES_PRODUCT_DOWNLOAD_GROUP_MAPPING")
    val groups: List<Group>

    val limits: Int
}