package zone.loong.cube.interior.service.impl

import jakarta.servlet.http.HttpServletResponse
import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.interior.model.InteriorProjectDatum
import zone.loong.cube.interior.model.dto.InteriorProjectDatumSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorProjectDatum
import zone.loong.cube.interior.model.fetchBy
import zone.loong.cube.interior.model.id
import zone.loong.cube.interior.model.parentId
import zone.loong.cube.interior.service.InteriorProjectDatumService
import zone.loong.cube.platform.storage.service.StorageService

@Service
class InteriorProjectDatumServiceImpl(
    private val kSqlClient: KSqlClient,
    private val storageService: StorageService
) : InteriorProjectDatumService {

    override fun fetchProjectDatum(pageQuery: PageQuery<InteriorProjectDatumSpecification>): Page<InteriorProjectDatum> {
        return kSqlClient.page(pageQuery) {
            pageQuery.specification.parentId?.let {
                where(table.parentId eq it)
            } ?: where(table.parentId eq null)
            select(table.fetchBy {
                allScalarFields()
                project {
                    title()
                }
                stage {
                    title()
                }
                parent {
                }
                file {
                    allScalarFields()
                    metadata()
                }
            })
        }
    }

    override fun save(saveInteriorProjectDatum: SaveInteriorProjectDatum): InteriorProjectDatum {
        return (saveInteriorProjectDatum.id?.let {
            kSqlClient.entities.saveCommand(saveInteriorProjectDatum, SaveMode.UPSERT).execute().modifiedEntity
        } ?: let {
            kSqlClient.entities.saveCommand(saveInteriorProjectDatum, SaveMode.INSERT_ONLY).execute().modifiedEntity
        })
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(InteriorProjectDatum::class, id)
    }

    override fun detail(id: String): InteriorProjectDatum {
        return kSqlClient.createQuery(InteriorProjectDatum::class) {
            where(table.id eq id)
            select(table.fetchBy {
                allScalarFields()
                project {
                    title()
                }
                stage {
                    title()
                }
                parent {
                    id
                }
                file {
                    allScalarFields()
                    metadata()
                }

            })
        }.fetchOneOrNull() ?: throw BizException("资源不存在或已被删除")
    }

    override fun download(id: String, response: HttpServletResponse) {
        storageService.download(id, response)
    }


}