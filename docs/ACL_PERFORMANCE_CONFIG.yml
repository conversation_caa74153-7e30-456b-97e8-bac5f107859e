# ACL性能优化配置示例

cube:
  acl:
    # 启用改进的ACL实现
    improved:
      enabled: true
    
    # 性能配置
    performance:
      # 批处理大小 - 每次处理的节点数量
      batch-size: 100
      
      # 启用异步处理
      async-enabled: true
      
      # 最大递归深度 - 防止无限递归
      max-depth: 10
      
      # 异步操作超时时间（秒）
      timeout-seconds: 30

# Spring异步配置
spring:
  task:
    execution:
      pool:
        # 核心线程数
        core-size: 4
        # 最大线程数
        max-size: 8
        # 队列容量
        queue-capacity: 100
        # 线程名前缀
        thread-name-prefix: "acl-async-"
      shutdown:
        # 优雅关闭
        await-termination: true
        await-termination-period: 60s

# 数据库连接池配置（针对大量权限操作）
spring:
  datasource:
    hikari:
      # 最大连接数
      maximum-pool-size: 20
      # 最小空闲连接数
      minimum-idle: 5
      # 连接超时时间
      connection-timeout: 30000
      # 空闲超时时间
      idle-timeout: 600000
      # 最大生命周期
      max-lifetime: 1800000

# 日志配置
logging:
  level:
    zone.loong.cube.framework.data.jimmer.service.ACLManagementService: DEBUG
    zone.loong.cube.framework.data.jimmer.service.ACLPerformanceMonitor: INFO
