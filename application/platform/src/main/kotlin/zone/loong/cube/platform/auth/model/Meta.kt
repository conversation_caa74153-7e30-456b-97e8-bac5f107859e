package zone.loong.cube.platform.auth.model

import com.fasterxml.jackson.annotation.JsonAnyGetter
import com.fasterxml.jackson.annotation.JsonAnySetter
import com.fasterxml.jackson.module.kotlin.jsonMapper

open class Meta {
    private val extraFields: MutableMap<String, Any> = HashMap()

    @JsonAnySetter
    fun setExtraFields(key: String, value: Any) {
        extraFields[key] = value
    }

    @JsonAnyGetter
    fun getExtraFields(): Map<String, Any> {
        return extraFields
    }

    fun <T> getExtraField(key: String, clazz: Class<T>, default: T): T {
        return jsonMapper().convertValue(extraFields.getOrDefault(key, default), clazz)
    }
}