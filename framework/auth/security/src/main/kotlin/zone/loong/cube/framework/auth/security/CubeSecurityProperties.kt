package zone.loong.cube.framework.auth.security

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "cube.security")
class CubeSecurityProperties {
    var account: String = "admin"
    var password: String = "P@ssw0rd"

    // token 名称（同时也是cookie名称）
    var tokenName: String = "Authorization"

    //排除的路径
    var exclude: List<String> = emptyList()

    // API 调用签名秘钥
    var securityKey: String = ""

    // token 有效期（单位：秒），默认30天，-1代表永不过期
    var expireTime: Long = 30 * 24 * 60 * 60

    // 接口调用时的时间戳允许的差距（单位：ms），-1 代表不校验差距，默认15分钟
    var timestampDisparity: Long = 900000

    // token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
    var activeTimeout: Long = -1
}