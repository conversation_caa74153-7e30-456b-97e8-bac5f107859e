package zone.loong.cube.interior.controller

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.interior.model.InteriorRegimeDatum
import zone.loong.cube.interior.model.dto.InteriorRegimeDatumSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorRegimeDatum
import zone.loong.cube.interior.service.InteriorRegimeDatumService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery

@RestController
@RequestMapping("/interior/regimeDatum")
class InteriorRegimeDatumController(
    private val interiorRegimeDatumService: InteriorRegimeDatumService
) {

    @PostMapping("/page")
    fun fetchProjectDatum(@RequestBody pageQuery: PageQuery<InteriorRegimeDatumSpecification>): Page<InteriorRegimeDatum> {
        return interiorRegimeDatumService.fetchRegimeDatum(pageQuery)
    }

    @PostMapping("/save")
    fun save(@RequestBody saveInteriorRegimeDatum: SaveInteriorRegimeDatum): InteriorRegimeDatum {
        return interiorRegimeDatumService.save(saveInteriorRegimeDatum)
    }

    @GetMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        interiorRegimeDatumService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): InteriorRegimeDatum {
        return interiorRegimeDatumService.detail(id)
    }
}