package zone.loong.cube.platform.storage.convert

import org.dromara.x.file.storage.core.FileInfo
import org.dromara.x.file.storage.core.upload.FilePartInfo
import zone.loong.cube.framework.storage.kernel.model.FileDetail
import zone.loong.cube.framework.storage.kernel.model.FilePartDetail
import java.time.ZoneId
import java.util.*

class StorageConvert {
    companion object {
        fun toFileDetail(fileInfo: FileInfo): FileDetail {
            return FileDetail {
                fileInfo.id?.let {
                    id = it
                }
                url = fileInfo.url
                size = fileInfo.size
                filename = fileInfo.filename
                originalFilename = fileInfo.originalFilename
                basePath = fileInfo.basePath
                path = fileInfo.path
                ext = fileInfo.ext
                contentType = fileInfo.contentType
                platform = fileInfo.platform
                thUrl = fileInfo.thUrl
                thFilename = fileInfo.thFilename
                thSize = fileInfo.thSize
                thContentType = fileInfo.thContentType
                objectId = fileInfo.objectId
                objectType = fileInfo.objectType
                metadata = fileInfo.metadata
                userMetadata = fileInfo.userMetadata
                thMetadata = fileInfo.thMetadata
                thUserMetadata = fileInfo.thUserMetadata
                attr = fileInfo.attr
                fileAcl = ""
                thFileAcl = ""
                hashInfo = fileInfo.hashInfo
                uploadId = fileInfo.uploadId
                uploadStatus = fileInfo.uploadStatus
                createdTime = fileInfo.createTime.toInstant().atZone(ZoneId.of("+8")).toLocalDateTime()
            }
        }

        fun toFileInfo(fileDetail: FileDetail): FileInfo {
            return FileInfo().apply {
                id = fileDetail.id
                url = fileDetail.url
                size = fileDetail.size
                filename = fileDetail.filename
                originalFilename = fileDetail.originalFilename
                basePath = fileDetail.basePath
                path = fileDetail.path
                ext = fileDetail.ext
                contentType = fileDetail.contentType
                platform = fileDetail.platform
                thUrl = fileDetail.thUrl
                thFilename = fileDetail.thFilename
                thSize = fileDetail.thSize
                thContentType = fileDetail.thContentType
                objectId = fileDetail.objectId
                objectType = fileDetail.objectType
                metadata = fileDetail.metadata
                userMetadata = fileDetail.userMetadata
                thMetadata = fileDetail.thMetadata
                thUserMetadata = fileDetail.thUserMetadata
                attr = fileDetail.attr
                fileAcl = fileDetail.fileAcl
                createTime = Date.from(fileDetail.createdTime.atZone(ZoneId.of("+8")).toInstant())
            }
        }

        fun toFilePartDetail(filePartInfo: FilePartInfo): FilePartDetail {
            return FilePartDetail {
                filePartInfo.id?.let {
                    id = it
                }
                platform = filePartInfo.platform
                uploadId = filePartInfo.uploadId
                eTag = filePartInfo.eTag
                partNumber = filePartInfo.partNumber
                partSize = filePartInfo.partSize
                hashInfo = filePartInfo.hashInfo
                createTime = filePartInfo.createTime.toInstant().atZone(ZoneId.of("+8")).toLocalDateTime()
            }
        }
    }
}