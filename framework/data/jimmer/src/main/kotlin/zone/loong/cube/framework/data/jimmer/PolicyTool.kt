package zone.loong.cube.framework.data.jimmer

import org.babyfish.jimmer.kt.unload
import zone.loong.cube.framework.data.jimmer.model.Policy
import zone.loong.cube.framework.data.jimmer.model.PolicyDraft
import zone.loong.cube.framework.data.jimmer.model.copy
import zone.loong.cube.framework.data.kernel.model.StrategyType

class PolicyTool {
    companion object {
        fun refPolicy(
            aclId:String?,
            strategyType: StrategyType,
            sourcePolicies: List<Policy>
        ): List<Policy> {
            val policies = mutableListOf<Policy>()
            sourcePolicies.filter { policy: Policy ->
                policy.strategyType != StrategyType.DRILL_UP
            }.forEach { policy ->
                policies.add(Policy(policy) {
                    unload(this, PolicyDraft::id)
                    this.aclId = aclId
                    this.refPolicyId = policy.refPolicy?.id ?: policy.id
                    this.strategyType = strategyType
                    unload(this, PolicyDraft::id)
                })
            }
            return  policies
        }

        //对比两个Policy，返回新增加和删除的Policy
        fun diff(sourcePolicies: List<Policy>, targetPolicies: List<Policy>): PolicyDiff {
            if (targetPolicies.isEmpty()) {
                return PolicyDiff()
            }
            //只保留ROOT的权限，防止参数传递错误导致删除引用传递
            val mutableSourcePolicies = sourcePolicies.filter { it->it.strategyType == StrategyType.ROOT }.toMutableList()
            val mutableTargetPolicies = targetPolicies.toMutableList()
            val policyDiff = PolicyDiff()
            targetPolicies.forEach { targetPolicy ->
                sourcePolicies.firstOrNull { sourcePolicy ->
                      sourcePolicy.refPolicy?.id == targetPolicy.refPolicy?.id
                            && sourcePolicy.policyType == targetPolicy.policyType
                            && sourcePolicy.strategyType == targetPolicy.strategyType
                            && sourcePolicy.value == targetPolicy.value
                }?.let { sourcePolicy ->
                    //两者都有，移除Policy
                    mutableTargetPolicies.remove(targetPolicy)
                    mutableSourcePolicies.remove(sourcePolicy)
                }?: policyDiff.added.add(targetPolicy.copy {
                    unload(this, PolicyDraft::id)
                })
            }
            //mutableSourcePolicies 中还剩下，说明是target中没有，那就是需要删除的
            policyDiff.deleted.addAll(mutableSourcePolicies.filter { it.policyType == targetPolicies[0].policyType })
            return policyDiff
        }
    }
}

data class PolicyDiff(val deleted: MutableSet<Policy> = mutableSetOf(), val added: MutableSet<Policy> =mutableSetOf())