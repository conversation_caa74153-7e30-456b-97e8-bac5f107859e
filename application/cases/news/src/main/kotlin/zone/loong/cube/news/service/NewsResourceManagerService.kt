package zone.loong.cube.news.service

import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsResourceManager
import zone.loong.cube.news.model.dto.PageSpecification
import zone.loong.cube.news.model.dto.SaveNewsResourceManager
import zone.loong.cube.news.model.elasticsearch.ESNewsDispersion

interface NewsResourceManagerService {
    fun fetchNews(pageQuery: PageQuery<ESSpecification>): ESPage<ESNewsDispersion>
    fun fetchResourceManagers(pageQuery: PageQuery<PageSpecification>): Page<NewsResourceManager>
    fun save(saveNewsResourceManager: SaveNewsResourceManager): NewsResourceManager
    fun delete(id: String)
    fun detail(id: String): NewsResourceManager
    fun  sync()
}