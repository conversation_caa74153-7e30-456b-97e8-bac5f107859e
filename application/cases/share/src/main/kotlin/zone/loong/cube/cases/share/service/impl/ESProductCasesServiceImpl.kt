package zone.loong.cube.cases.share.service.impl

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.data.elasticsearch.core.ElasticsearchOperations
import org.springframework.data.elasticsearch.core.document.Document
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates
import org.springframework.data.elasticsearch.core.query.UpdateQuery
import org.springframework.stereotype.Service
import zone.loong.cube.cases.share.model.ShareResource
import zone.loong.cube.cases.share.model.by
import zone.loong.cube.cases.share.model.elasticsearch.ESShareResource
import zone.loong.cube.cases.share.model.fetchBy
import zone.loong.cube.cases.share.model.id
import zone.loong.cube.cases.share.service.ESShareCasesService
import zone.loong.cube.framework.data.elasticsearch.service.SearchService
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.data.kernel.TransactionSync
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.framework.tika.service.TikaService
import zone.loong.cube.platform.storage.service.StorageService

@Service
class ESShareCasesServiceImpl(
    private val searchService: SearchService,
    private val kSqlClient: KSqlClient,
    private val storageService: StorageService,
    private val elasticsearchOperations: ElasticsearchOperations,
    private val tikaService: TikaService
) : ESShareCasesService {
    override fun sync(resourceId: String, extractFile: Boolean) {
        val case = kSqlClient.filters {
            disableByTypes(ACLFilter::class)
        }.findById(newFetcher(ShareResource::class).by {
            allTableFields()
            `parent*`()
            file {
                allScalarFields()
            }
            acl {
                allScalarFields()
                policies {
                    allScalarFields()
                }
            }
        }, resourceId) ?: throw BizException("资源被删除")
        val esExperienceCase = ESShareResource.buildFromResource(case).apply {
            if (extractFile) {
                file?.run {
                    storageService.download(id).inputStream { inputStream ->
                        tikaService.getText(inputStream).takeIf { text -> text.isNotEmpty() }?.let { text ->
                            content.add(text)
                        }
                    }
                }

            }

        }

        TransactionSync.runBeforeCommit("同步到ES",){
            searchService.save(esExperienceCase)
        }

    }


    override fun updateAcl(resourceIds: List<String>) {
        if (resourceIds.isEmpty()) {
            return
        }
        kSqlClient.createQuery(ShareResource::class) {
            where(table.id valueIn resourceIds)
            select(table.fetchBy {
                allTableFields()
                acl {
                    policies {
                        allScalarFields()
                    }
                }
            })
        }.let {
            val updateQueries = it.execute().filter { case-> case.acl?.policies?.isNotEmpty() == true }.map {
                    case ->
                val document = Document.create()
                document["policies"] = case.acl?.policies?.map { policy ->
                    mapOf(
                        "policyType" to policy.policyType,
                        "strategyType" to policy.strategyType,
                        "value" to policy.value
                    )
                }

                UpdateQuery.builder(case.id)
                    .withDocument(document)
                    .build()
            }
            if (updateQueries.isNotEmpty()){
                elasticsearchOperations.bulkUpdate(updateQueries, IndexCoordinates.of("cases_Share"))
            }
        }

    }

    override fun syncWithFile(resourceIds: List<String>) {
        kSqlClient.createQuery(ShareResource::class) {
            where(table.id valueIn resourceIds)
            select(table.fetchBy {
                allTableFields()
                `parent*`()
                file {
                    allScalarFields()
                }
                acl {
                    allScalarFields()
                    policies {
                        allScalarFields()
                    }
                }
            })
        }.map {
            ESShareResource.buildFromResource(it).apply {
                file?.run {
                    storageService.download(id).inputStream { inputStream ->
                        tikaService.getText(inputStream).takeIf { text -> text.isNotEmpty() }?.let { text ->
                            content.add(text)
                        }
                    }
                }

            }
        }.forEach {
            searchService.save(it)
        }
    }

    override fun delete(resourceId: String) {
        searchService.deleteById(resourceId, ESShareResource::class)
    }
}