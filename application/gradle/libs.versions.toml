[versions]
springboot = "3.4.4"
cube-framework = "1.0.0"
ksp = "2.1.20-2.0.0"
jimmer = "0.9.106"
postgresql = "42.7.3"
forest="1.5.36"

[libraries]
postgresql = { module = "org.postgresql:postgresql", version.ref = "postgresql" }
boot-dependencies = { module = "org.springframework.boot:spring-boot-dependencies", version.ref = "springboot" }
cube-framework-kernel = { module = "zone.loong.cube.framework:cube-framework-starter-kernel", version.ref = "cube-framework" }
cube-framework-auth = { module = "zone.loong.cube.framework:cube-framework-starter-auth", version.ref = "cube-framework" }
cube-framework-http-forest = { module = "zone.loong.cube.framework:cube-framework-starter-http-forest", version.ref = "cube-framework" }
cube-framework-data-jimmer = { module = "zone.loong.cube.framework:cube-framework-starter-data-jimmer", version.ref = "cube-framework" }
cube-framework-data-elasticsearch = { module = "zone.loong.cube.framework:cube-framework-starter-data-elasticsearch", version.ref = "cube-framework" }
cube-framework-storage-local = { module = "zone.loong.cube.framework:cube-framework-starter-storage-local", version.ref = "cube-framework" }
cube-framework-cache-jetcache = { module = "zone.loong.cube.framework:cube-framework-starter-cache-jetcache", version.ref = "cube-framework" }
cube-framework-storage-tika = { module = "zone.loong.cube.framework:cube-framework-starter-storage-tika", version.ref = "cube-framework" }
cube-framework-storage-preview = { module = "zone.loong.cube.framework:cube-framework-starter-storage-preview", version.ref = "cube-framework" }
jimmer = { module = "org.babyfish.jimmer:jimmer-spring-boot-starter", version.ref = "jimmer" }
jimmer-ksp = { module = "org.babyfish.jimmer:jimmer-ksp", version.ref = "jimmer" }
forest = {module="com.dtflys.forest:forest-spring-boot3-starter",version.ref="forest"}
[plugins]
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }


