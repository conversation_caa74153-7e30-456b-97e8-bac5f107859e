package zone.loong.cube.framework.data.elasticsearch.autoconfiguration

import jakarta.annotation.PostConstruct
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.boot.autoconfigure.AutoConfigureAfter
import org.springframework.boot.autoconfigure.data.elasticsearch.ElasticsearchDataAutoConfiguration
import org.springframework.context.annotation.Import
import zone.loong.cube.framework.data.elasticsearch.configuration.ElasticsearchConfiguration
import zone.loong.cube.framework.kernel.log.annotation.Log
import zone.loong.cube.framework.kernel.log.annotation.Log.Companion.log

@AutoConfiguration
@Import(ElasticsearchConfiguration::class)
@AutoConfigureAfter(ElasticsearchDataAutoConfiguration::class)
@Log
class ElasticsearchAutoconfiguration {
    @PostConstruct
    fun load() {
        log.info("[CUBE FRAMEWORK] MODULE [DATA-ELASTICSEARCH] LOAD [SUCCESS].")
    }
}