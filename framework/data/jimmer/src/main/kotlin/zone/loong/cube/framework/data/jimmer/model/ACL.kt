package zone.loong.cube.framework.data.jimmer.model

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.Key
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.OneToMany
import org.babyfish.jimmer.sql.Table
import zone.loong.cube.framework.data.jimmer.PolicyDiff
import zone.loong.cube.framework.data.jimmer.PolicyTool
import zone.loong.cube.framework.data.kernel.model.ACL
import zone.loong.cube.framework.data.kernel.model.StrategyType

@Entity
@Table(name = "ACCESS_ACL")
interface ACL:ACL,AuditAware {
    @Key
    override val  resourceId: String
    override val  resourceType:String
    override val  resourceName:String

    @ManyToOne
    val parent: zone.loong.cube.framework.data.jimmer.model.ACL?

    @OneToMany(mappedBy = "parent")
    val children: List< zone.loong.cube.framework.data.jimmer.model.ACL>

    @OneToMany(mappedBy = "acl")
    override val policies: List<Policy>

    fun flattenParent():List<zone.loong.cube.framework.data.jimmer.model.ACL>{
        return parent?.let {
            listOf(it) + it.flattenParent()
        } ?: emptyList()
    }

    fun flattenChildren():List<zone.loong.cube.framework.data.jimmer.model.ACL>{
        return children + children.flatMap { it.flattenChildren() }
    }

}