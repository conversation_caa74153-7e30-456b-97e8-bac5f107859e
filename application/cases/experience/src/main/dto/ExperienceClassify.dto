export zone.loong.cube.cases.experience.model.ExperienceClassify
    -> package zone.loong.cube.cases.experience.model.dto

import zone.loong.cube.framework.data.kernel.model.PolicyType

specification ClassifySpecification {
    associatedIdEq(parent)

    code

    like(name)
}

input ACLClassify {
    id!

    acl {
        policyType :PolicyType?

        policies {
            #allScalars(this)

            id

            id(acl)

            id(refPolicy)

        }
    }
}

input SaveClassify {
    #allScalars(this)

    id?

    id(parent)
}