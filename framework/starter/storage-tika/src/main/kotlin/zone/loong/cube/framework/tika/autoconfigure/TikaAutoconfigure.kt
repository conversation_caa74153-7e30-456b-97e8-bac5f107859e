package zone.loong.cube.framework.tika.autoconfigure

import jakarta.annotation.PostConstruct
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Import
import zone.loong.cube.framework.kernel.log.annotation.Log
import zone.loong.cube.framework.kernel.log.annotation.Log.Companion.log
import zone.loong.cube.framework.tika.TikaProperties
import zone.loong.cube.framework.tika.configure.TikaConfigure

@Log
@AutoConfiguration
@Import(TikaConfigure::class)
@EnableConfigurationProperties(TikaProperties::class)
class TikaAutoconfigure {
    @PostConstruct
    fun load() {
        log.info("[CUBE FRAMEWORK] MODULE [STORAGE-TIKA] LOAD [SUCCESS].")
    }
}