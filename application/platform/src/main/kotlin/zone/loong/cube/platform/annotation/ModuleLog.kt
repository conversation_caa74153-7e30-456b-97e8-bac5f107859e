package zone.loong.cube.platform.annotation

import java.lang.annotation.Inherited
import kotlin.reflect.KClass

@Inherited
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS, AnnotationTarget.TYPE_PARAMETER, AnnotationTarget.FUNCTION)
annotation class ModuleLog(
    // 工作空间
    val workspace: String = "",
    //  模块
    val module: String = "",
    // 接口描述
    val operation: String = "",
    //    资源ID获取SPEL表达式
    val identity: String = "",
    val fetch: KClass<out ModuleResourceFetch>
)

class Resource {
    //    资源名称
    var name: String = ""
}

interface ModuleResourceFetch {
    //如何获取该资源
    fun fetchResource(identity: String): Resource
}

