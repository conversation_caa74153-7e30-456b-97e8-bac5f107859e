package zone.loong.cube.framework.data.kernel.model

import com.fasterxml.jackson.annotation.JsonValue

class PageQuery<E>(
    var specification: E,
    var page: Int,
    var pageSize: Int,
    var sort: List<Sort> = emptyList(),
)

class Sort{
    var field: String = ""
    var order: OrderEnum = OrderEnum.ASC
}

enum class OrderEnum(@JsonValue val value: String) {
    ASC("asc"),
    DESC("desc")
}