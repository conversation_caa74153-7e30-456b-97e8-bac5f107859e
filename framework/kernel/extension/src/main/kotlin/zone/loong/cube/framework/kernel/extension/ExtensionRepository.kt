package zone.loong.cube.framework.kernel.extension

import org.springframework.stereotype.Component


@Component
@Suppress("UNCHECKED_CAST")
class ExtensionRepository {
    val extensionRepo = HashMap<BizScenario, ExtensionPointI>()
    fun <T : ExtensionPointI> locateComponent(bizScenario: BizScenario): T {
        return extensionRepo[bizScenario] as T?
            ?: throw ExtensionException("can't find extension point")
    }
}
