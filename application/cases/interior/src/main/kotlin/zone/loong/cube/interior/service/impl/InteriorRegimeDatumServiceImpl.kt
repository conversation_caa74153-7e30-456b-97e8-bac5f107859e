package zone.loong.cube.interior.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.stereotype.Service
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.interior.model.*
import zone.loong.cube.interior.model.dto.InteriorRegimeDatumSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorRegimeDatum
import zone.loong.cube.interior.service.InteriorRegimeDatumService

@Service
class InteriorRegimeDatumServiceImpl(
    private val kSqlClient: KSqlClient
): InteriorRegimeDatumService {

    override fun fetchRegimeDatum(pageQuery: PageQuery<InteriorRegimeDatumSpecification>): Page<InteriorRegimeDatum> {
        return kSqlClient.page(pageQuery){
            pageQuery.specification.parentId?.let {
                where(table.parentId eq it)
            }?: where(table.parentId eq null)
            select(table.fetchBy {
                allScalarFields()
                regime()
                file {
                    originalFilename()
                    filename()
                }
            })
        }
    }

    override fun save(saveInteriorRegimeDatum: SaveInteriorRegimeDatum): InteriorRegimeDatum {
        return (saveInteriorRegimeDatum.id?.let {
            kSqlClient.entities.saveCommand(saveInteriorRegimeDatum, SaveMode.UPSERT).execute().modifiedEntity
        }?:let{
            kSqlClient.entities.saveCommand(saveInteriorRegimeDatum, SaveMode.INSERT_ONLY).execute().modifiedEntity
        })
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(InteriorProjectDatum::class, id)
    }

    override fun detail(id: String): InteriorRegimeDatum {
        return kSqlClient.findById(newFetcher(InteriorRegimeDatum::class).by {
            allTableFields()
            parent()
            regime {
                title()
            }
            file {
                originalFilename()
                metadata()
            }
        }, id) ?: throw Exception("未找到数据")
    }

}