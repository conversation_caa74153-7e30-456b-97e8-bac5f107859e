package zone.loong.cube.cases.experience.model.elasticsearch

import org.babyfish.jimmer.kt.isLoaded
import org.springframework.data.elasticsearch.annotations.Document
import org.springframework.data.elasticsearch.annotations.Field
import org.springframework.data.elasticsearch.annotations.FieldType
import zone.loong.cube.cases.experience.model.CaseStatus
import zone.loong.cube.cases.experience.model.ExperienceCase
import zone.loong.cube.framework.data.elasticsearch.ACL
import zone.loong.cube.framework.data.elasticsearch.Policy
import java.time.LocalDate.now
import java.time.LocalDateTime


@Document(indexName = "cases_experiences")
class ESExperienceCase : ACL() {
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart")
    private val keywords: String = ""

    //关联分类
    @Field(type = FieldType.Keyword)
    var classifyId: String = ""

    @Field(type = FieldType.Keyword)
    var path: List<String> = emptyList()

    @Field(type = FieldType.Keyword)
    var fileId: String = ""

    @Field(type = FieldType.Keyword)
    var caseOfYear: String = now().year.toString()

    @Field(type = FieldType.Keyword)
    //案例状态
    var status: CaseStatus = CaseStatus.ON_SHELVES

    @Field(type = FieldType.Date)
    //提报日期
    var postDate: LocalDateTime = LocalDateTime.now()

    //提报人
    @Field(type = FieldType.Keyword)
    var postUser: String = ""

    @Field(type = FieldType.Text, copyTo = ["keywords"])
    var title: String = ""

    @Field(type = FieldType.Keyword, copyTo = ["keywords"])
    var tags: List<String> = emptyList()

    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_smart", copyTo = ["keywords"])
    var content: String = ""

    companion object {
        fun buildFromCase(case: ExperienceCase): ESExperienceCase {
            return ESExperienceCase().apply {
                id = case.id
                classifyId = case.classify?.id ?: ""
                path = (case.classify?.flattenParent()?.map { it.id } ?: emptyList()) + listOf(classifyId)
                fileId = case.file.id
                caseOfYear = case.caseOfYear
                status = case.status
                postDate = case.postDate
                postUser = case.postUser
                title = case.title
                tags = case.tags
                createdBy = case.createdBy
                createdTime = case.createdTime
                modifiedBy = case.modifiedBy
                modifiedTime = case.modifiedTime
                //复制权限
                if (isLoaded(case, ExperienceCase::acl)) {
                    case.acl?.let { resource ->
                        policies = resource.policies.map { policy ->
                            Policy().apply {
                                policyType = policy.policyType
                                strategyType = policy.strategyType
                                value = policy.value
                            }
                        }
                    }
                }
            }
        }
    }
}