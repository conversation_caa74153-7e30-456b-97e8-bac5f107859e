package zone.loong.cube.interior.controller

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.interior.model.InteriorProjectStage
import zone.loong.cube.interior.service.InteriorProjectStageService

@RestController
@RequestMapping("/interior/projectStage")
class InteriorProjectStageController(
    private val interiorProjectStageService: InteriorProjectStageService
) {

    @GetMapping("/fetchStages")
    fun fetchStages() : List<InteriorProjectStage> {
        return interiorProjectStageService.fetchStages()
    }

}