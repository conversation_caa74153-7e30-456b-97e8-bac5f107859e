package zone.loong.cube.cases.experience.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.cases.experience.constants.ExperienceConstants
import zone.loong.cube.framework.data.jimmer.model.ACLAware
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.data.kernel.annotations.ACL

@Entity
@Table(name = "CASES_EXPERIENCE_CLASSIFY")
@ACL(
    type = ExperienceConstants.CLASSIFY_RESOURCE,
    name = ExperienceConstants.CLASSIFY_RESOURCE_NAME,
    parentResourceId = "#parent?.id"
)
interface ExperienceClassify : ACLAware, AuditAware {
    @Key
    val code: String
    val name: String

    @ManyToOne
    val parent: ExperienceClassify?

    @OneToMany(mappedBy = "parent")
    val children: List<ExperienceClassify>

    @OneToMany(mappedBy = "classify")
    val cases: List<ExperienceCase>


    fun flattenChildren(): List<ExperienceClassify> {
        return children + children.flatMap { it.flattenChildren() }
    }
    
    fun flattenParent(): List<ExperienceClassify> {
        return parent?.let {
            listOf(it) + it.flattenParent()
        }?: emptyList()
    }
}