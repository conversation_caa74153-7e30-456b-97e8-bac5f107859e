package zone.loong.cube.news.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.springframework.stereotype.Service
import zone.loong.cube.news.model.NewsNavigation
import zone.loong.cube.news.model.dto.NewsNavigationSpecification
import zone.loong.cube.news.model.dto.SaveNewsNavigation
import zone.loong.cube.news.service.NewsNavigationService
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.news.model.fetchBy
import zone.loong.cube.news.model.parentId
import zone.loong.cube.news.model.sequence

@Service
class NewsNavigationServiceImpl(
    private val kSqlClient: KSqlClient,
) : NewsNavigationService {

    override fun fetchNavigations(pageQuery: PageQuery<NewsNavigationSpecification>): Page<NewsNavigation> {
        return kSqlClient.page(pageQuery){
            orderBy(table.sequence)
            select(table.fetchBy {
                allScalarFields()
                parent()
                `children*`()
            })
        }
    }

    override fun fetchNavigationTree(): List<NewsNavigation> {
        return kSqlClient.createQuery(NewsNavigation::class) {
            where(table.parentId eq "0")
            orderBy(table.sequence)
            select(table.fetchBy {
                title()
                code()
                image {
                    url()
                }
                parent()
                `children*`()
            })
        }.execute()
    }

    override fun fetchNavigations(): List<NewsNavigation> {
        return kSqlClient.createQuery(NewsNavigation::class){
            orderBy(table.sequence)
            select(table.fetchBy {
                title()
                blocks {
                    title()
                }
            })
        }.execute()
    }

    override fun save(saveNewsNavigation: SaveNewsNavigation) : NewsNavigation {
        return (saveNewsNavigation.id?.let {
            kSqlClient.entities.saveCommand(saveNewsNavigation, SaveMode.UPSERT).execute().modifiedEntity
        }?:let{
            kSqlClient.entities.saveCommand(saveNewsNavigation, SaveMode.INSERT_ONLY).execute().modifiedEntity
        })
    }

    override fun delete(id: String) {
        kSqlClient.deleteById(NewsNavigation::class, id)
    }

    override fun detail(id: String): NewsNavigation {
        return kSqlClient.findById(NewsNavigation::class, id) ?: throw BizException("找不到该栏目")
    }

}