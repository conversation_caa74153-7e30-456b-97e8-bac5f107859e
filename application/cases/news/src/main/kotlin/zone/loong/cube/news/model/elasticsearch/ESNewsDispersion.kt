package zone.loong.cube.news.model.elasticsearch

import org.springframework.data.elasticsearch.annotations.DateFormat
import org.springframework.data.elasticsearch.annotations.Document
import org.springframework.data.elasticsearch.annotations.Field
import org.springframework.data.elasticsearch.annotations.FieldType
import zone.loong.cube.framework.data.elasticsearch.Audit
import zone.loong.cube.news.model.NewsResourceManager
import java.time.LocalDateTime


@Document(indexName = "news_dispersion")
class ESNewsDispersion : Audit() {
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_max_word")
    private val keywords: String = ""

    //提报人
    @Field(type = FieldType.Keyword)
    var author: String = ""    //提报人

    //摘要
    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", copyTo = ["keywords"])
    var contentBrief: String = ""    //提报人


    @Field(type = FieldType.Date, format = [DateFormat.date_hour_minute_second, DateFormat.epoch_millis])
    var issueTime: LocalDateTime = LocalDateTime.now()

    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", copyTo = ["keywords"])
    var title: String = ""

    @Field(type = FieldType.Keyword, copyTo = ["keywords"])
    var tags: List<String> = emptyList()

    @Field(type = FieldType.Text, analyzer = "ik_max_word", searchAnalyzer = "ik_max_word", copyTo = ["keywords"])
    var content: String = ""

    @Field(type = FieldType.Keyword, copyTo = ["keywords"])
    var coverImg: List<String> = emptyList()

    companion object {
        fun buildFromResource(resource: NewsResourceManager): ESNewsDispersion {
            return ESNewsDispersion().apply {
                id = resource.id
                title = resource.title
                content = ""
                contentBrief = resource.contentBrief ?: ""
                author = resource.author ?: ""
                tags = resource.tags ?: emptyList()
                issueTime = resource.issueTime ?: LocalDateTime.now()
                coverImg = resource.attachments.map { it.file.url }
            }
        }
    }
}