//package zone.loong.cube.framework.storage.convert.service
//
//import kotlinx.coroutines.*
//import java.util.concurrent.ConcurrentLinkedQueue
//import java.util.concurrent.ConcurrentHashMap
//import java.io.File
//import zone.loong.cube.framework.storage.convert.service.FileConvertTask
//import zone.loong.cube.framework.storage.convert.service.FileConvertStatus
//import zone.loong.cube.framework.storage.convert.service.FileConverter
//import zone.loong.cube.framework.storage.convert.service.TargetType
//import zone.loong.cube.framework.storage.kernel.service.StorageService
//import zone.loong.cube.framework.storage.kernel.exception.FileException
//
///**
// * 文件转换队列与进度管理器。
// * 负责任务调度、进度管理、转换器注册与调用。
// * 依赖 StorageService 进行文件存储。
// */
//object FileConvertManager {
//    private val queue = ConcurrentLinkedQueue<FileConvertTask>()
//    private val taskMap = ConcurrentHashMap<String, FileConvertTask>()
//    private val converters = mutableListOf<FileConverter>()
//    var storageService: StorageService? = null
//    private var running = false
//
//    /**
//     * 注册转换器实现。
//     */
//    fun registerConverter(converter: FileConverter) {
//        converters.add(converter)
//    }
//
//    /**
//     * 提交转换任务。
//     */
//    fun submitTask(task: FileConvertTask) {
//        queue.add(task)
//        taskMap[task.id] = task
//        start()
//    }
//
//    /**
//     * 获取任务。
//     */
//    fun getTask(id: String): FileConvertTask? = taskMap[id]
//
//    /**
//     * 获取所有任务。
//     */
//    fun getAllTasks(): List<FileConvertTask> = taskMap.values.toList()
//
//    /**
//     * 启动任务调度。
//     */
//    private fun start() {
//        if (!running) {
//            running = true
//            GlobalScope.launch(Dispatchers.IO) {
//                while (queue.isNotEmpty()) {
//                    val task = queue.poll() ?: continue
//                    val converter = converters.find { it.supports(task.targetType) }
//                    if (converter == null) {
//                        task.status = FileConvertStatus.FAILED
//                        task.errorMsg = "No converter found for type: ${'$'}{task.targetType}"
//                        continue
//                    }
//                    task.status = FileConvertStatus.RUNNING
//                    try {
//                        // 1. 查询源文件
//                        val fileInfo = storageService?.fileRecorder?.getById(task.sourceFileId)
//                            ?: throw FileException("源文件不存在")
//                        // 2. 执行转换，获得本地文件
//                        val convertedFile = converter.convert(task)
//                        // 3. 上传转换后的文件，获取 FileInfo
//                        val uploaded = storageService?.upload(fileInfo.basePath ?: "", convertedFile)
//                            ?: throw FileException("上传转换文件失败")
//                        task.targetFile = uploaded
//                        task.status = FileConvertStatus.SUCCESS
//                        task.progress = 100
//                    } catch (e: FileException) {
//                        task.status = FileConvertStatus.FAILED
//                        task.errorMsg = e.message
//                    } catch (e: Exception) {
//                        task.status = FileConvertStatus.FAILED
//                        task.errorMsg = e.message
//                    }
//                }
//                running = false
//            }
//        }
//    }
//}
