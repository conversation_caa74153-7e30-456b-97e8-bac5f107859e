package zone.loong.cube.framework.data.jimmer.service

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.scheduling.annotation.Async
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.boot.context.properties.EnableConfigurationProperties
import zone.loong.cube.framework.data.jimmer.PolicyTool
import zone.loong.cube.framework.data.jimmer.event.ResourcePolicyUpdate
import zone.loong.cube.framework.data.jimmer.model.*
import zone.loong.cube.framework.data.kernel.model.StrategyType
import com.github.f4b6a3.ulid.UlidCreator
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.ast.expression.valueIn
import java.util.concurrent.CompletableFuture
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import org.slf4j.LoggerFactory

/**
 * ACL性能配置
 */
@ConfigurationProperties(prefix = "cube.acl.performance")
data class ACLPerformanceProperties(
    val batchSize: Int = 100,
    val asyncEnabled: Boolean = true,
    val maxDepth: Int = 10,
    val timeoutSeconds: Long = 30
)

/**
 * ACL权限管理服务
 * 统一处理ACL的创建、更新、树结构变更等操作
 * 包含性能优化：批量操作、异步处理、深度限制
 */
@Service
@EnableConfigurationProperties(ACLPerformanceProperties::class)
class ACLManagementService(
    private val kSqlClient: KSqlClient,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val performanceProperties: ACLPerformanceProperties,
    private val performanceMonitor: ACLPerformanceMonitor
) {

    private val logger = LoggerFactory.getLogger(ACLManagementService::class.java)
    private val executor = Executors.newFixedThreadPool(4)

    /**
     * 创建ACL记录
     * 这个方法在ResourceListener中调用，不在拦截器中调用
     */
    @Transactional
    fun createACLRecord(
        aclId: String,
        resourceId: String,
        resourceType: String,
        resourceName: String,
        parentResourceId: String?
    ): ACL {
        val acl = ACL {
            id = aclId
            this.resourceId = resourceId
            this.resourceType = resourceType
            this.resourceName = resourceName

            // 设置父级关系
            parentResourceId?.let { parentId ->
                parent {
                    resourceId = parentId
                }
            }

            // 继承父级权限
            policies = inheritParentPolicies(aclId, parentResourceId)
        }

        return saveACL(acl)
    }

    /**
     * 清理ACL数据
     */
    @Transactional
    fun cleanupACL(resourceId: String) {
        // 删除ACL记录（级联删除相关的Policy）
        kSqlClient.createDelete(ACL::class) {
            where(table.resourceId eq resourceId)
        }.execute()

        logger.debug("已清理ACL数据: {}", resourceId)
    }

    /**
     * 处理树结构父节点迁移
     */
    @Transactional
    fun handleParentMigration(resourceId: String, oldParentId: String?, newParentId: String?) {
        val acl = fetchACLByResourceId(resourceId)

        // 更新父级关系
        val updatedACL = acl.copy {
            newParentId?.let {
                parent {
                    resourceId = it
                }
            } ?: run {
                parent = null
            }
        }

        saveACL(updatedACL)

        // 重新计算权限继承
        refreshInheritedPolicies(resourceId, newParentId)

        // 异步更新所有子节点的权限
        if (performanceProperties.asyncEnabled) {
            handleChildrenRefreshAsync(resourceId)
        } else {
            refreshChildrenPolicies(resourceId)
        }
    }

    /**
     * 异步处理子节点权限刷新
     */
    @Async
    fun handleChildrenRefreshAsync(parentResourceId: String): CompletableFuture<Void> {
        return CompletableFuture.runAsync({
            try {
                logger.info("开始异步刷新子节点权限: {}", parentResourceId)
                val startTime = System.currentTimeMillis()

                refreshChildrenPolicies(parentResourceId)

                val duration = System.currentTimeMillis() - startTime
                logger.info("完成异步刷新子节点权限: {}, 耗时: {}ms", parentResourceId, duration)
            } catch (e: Exception) {
                logger.error("异步刷新子节点权限失败: {}", parentResourceId, e)
                throw e
            }
        }, executor).orTimeout(performanceProperties.timeoutSeconds, TimeUnit.SECONDS)
    }

    /**
     * 同步等待异步权限刷新完成
     */
    fun handleParentMigrationSync(resourceId: String, oldParentId: String?, newParentId: String?): CompletableFuture<Void> {
        handleParentMigration(resourceId, oldParentId, newParentId)
        return handleChildrenRefreshAsync(resourceId)
    }

    /**
     * 刷新继承的权限策略
     */
    @Transactional
    fun refreshInheritedPolicies(resourceId: String, parentResourceId: String?) {
        val acl = fetchACLByResourceId(resourceId)
        
        // 删除所有继承的权限（DRILL_DOWN 和 DRILL_UP）
        deleteInheritedPolicies(resourceId)
        
        // 重新继承父级权限
        val newInheritedPolicies = inheritParentPolicies(acl.id, parentResourceId)
        
        if (newInheritedPolicies.isNotEmpty()) {
            savePolicies(newInheritedPolicies)
        }
        
        // 发布权限更新事件
        publishPolicyUpdateEvent(listOf(resourceId), acl.resourceType, acl.resourceName)
    }

    /**
     * 递归刷新所有子节点的权限 - 优化版本
     * 使用批量操作和异步处理来提高性能
     */
    @Transactional
    fun refreshChildrenPolicies(parentResourceId: String) {
        val context = performanceMonitor.startOperation("refreshChildrenPolicies", parentResourceId)
        try {
            // 使用广度优先遍历，避免深度递归
            val nodeCount = refreshChildrenPoliciesBFS(parentResourceId)
            performanceMonitor.endOperation(context, nodeCount)
        } catch (e: Exception) {
            performanceMonitor.endOperation(context, 0)
            throw e
        }
    }

    /**
     * 使用广度优先搜索批量刷新子节点权限
     * 包含深度限制和分批处理
     * @return 处理的节点总数
     */
    private fun refreshChildrenPoliciesBFS(parentResourceId: String): Int {
        val queue = mutableListOf(parentResourceId)
        val processed = mutableSetOf<String>()
        var currentDepth = 0
        var totalNodes = 0

        while (queue.isNotEmpty() && currentDepth < performanceProperties.maxDepth) {
            val currentBatch = queue.toList()
            queue.clear()
            currentDepth++

            logger.debug("处理第{}层级，节点数量: {}", currentDepth, currentBatch.size)

            // 分批处理，避免单次处理过多数据
            currentBatch.chunked(performanceProperties.batchSize).forEach { batch ->
                totalNodes += processBatch(batch, queue, processed)
            }
        }

        if (currentDepth >= performanceProperties.maxDepth) {
            logger.warn("权限刷新达到最大深度限制: {}, 父节点: {}, 已处理节点: {}",
                performanceProperties.maxDepth, parentResourceId, totalNodes)
        }

        return totalNodes
    }

    /**
     * 处理单个批次
     * @return 处理的节点数量
     */
    private fun processBatch(
        batch: List<String>,
        queue: MutableList<String>,
        processed: MutableSet<String>
    ): Int {
        // 批量获取当前层级的所有子节点
        val childrenMap = fetchChildrenACLsBatch(batch)

        // 批量处理当前层级的权限刷新
        val refreshRequests = mutableListOf<Pair<String, String>>()
        var nodeCount = 0

        childrenMap.forEach { (parentId, children) ->
            children.forEach { child ->
                if (!processed.contains(child.resourceId)) {
                    refreshRequests.add(child.resourceId to parentId)
                    queue.add(child.resourceId)
                    processed.add(child.resourceId)
                    nodeCount++
                }
            }
        }

        // 批量执行权限刷新
        if (refreshRequests.isNotEmpty()) {
            logger.debug("批量刷新权限，数量: {}", refreshRequests.size)
            batchRefreshInheritedPolicies(refreshRequests)
        }

        return nodeCount
    }

    /**
     * 批量获取多个父节点的子节点
     */
    private fun fetchChildrenACLsBatch(parentResourceIds: List<String>): Map<String, List<ACL>> {
        if (parentResourceIds.isEmpty()) return emptyMap()

        return kSqlClient.createQuery(ACL::class) {
            where(table.parent.resourceId valueIn parentResourceIds)
            select(table.fetchBy {
                resourceId()
                resourceType()
                resourceName()
                parent {
                    resourceId()
                }
            })
        }.execute().groupBy { it.parent?.resourceId ?: "" }
    }

    /**
     * 批量刷新继承权限
     */
    private fun batchRefreshInheritedPolicies(refreshRequests: List<Pair<String, String>>) {
        if (refreshRequests.isEmpty()) return

        val resourceIds = refreshRequests.map { it.first }

        // 批量删除继承权限
        batchDeleteInheritedPolicies(resourceIds)

        // 批量重建继承权限
        val policiesToAdd = mutableListOf<Policy>()
        val aclsToUpdate = mutableListOf<ACL>()

        refreshRequests.forEach { (resourceId, parentResourceId) ->
            val acl = fetchACLByResourceId(resourceId)
            val parentPolicies = fetchRootPolicies(parentResourceId)
            val inheritedPolicies = PolicyTool.refPolicy(acl.id, StrategyType.DRILL_DOWN, parentPolicies)

            policiesToAdd.addAll(inheritedPolicies)
            aclsToUpdate.add(acl)
        }

        // 批量保存权限
        if (policiesToAdd.isNotEmpty()) {
            savePolicies(policiesToAdd)
        }

        // 批量发布事件
        publishBatchPolicyUpdateEvents(aclsToUpdate)
    }

    /**
     * 批量删除继承权限
     */
    private fun batchDeleteInheritedPolicies(resourceIds: List<String>) {
        if (resourceIds.isEmpty()) return

        kSqlClient.createDelete(Policy::class) {
            where(table.acl.resourceId valueIn resourceIds)
            where(table.strategyType valueIn listOf(StrategyType.DRILL_DOWN, StrategyType.DRILL_UP))
        }.execute()
    }

    /**
     * 批量授权操作
     */
    @Transactional
    fun batchAuthorization(authorizationRequests: List<ACLAuthorizationRequest>) {
        val changedACLs = mutableSetOf<ACL>()
        val policiesToAdd = mutableListOf<Policy>()
        val policiesToDelete = mutableListOf<String>()

        authorizationRequests.forEach { request ->
            val acl = fetchACLByResourceId(request.resourceId)
            val currentPolicies = fetchRootPolicies(request.resourceId)
            val targetPolicies = request.policies.map { it.toEntity() }
            
            val (deleted, added) = PolicyTool.diff(currentPolicies, targetPolicies)
            
            if (deleted.isNotEmpty() || added.isNotEmpty()) {
                changedACLs.add(acl)
                
                // 处理新增权限
                added.forEach { policy ->
                    val policyWithACL = policy.copy { aclId = acl.id }
                    policiesToAdd.add(policyWithACL)
                    
                    // 向上传播权限
                    acl.flattenParent().forEach { parent ->
                        changedACLs.add(parent)
                        policiesToAdd.add(policyWithACL.copy {
                            aclId = parent.id
                            strategyType = StrategyType.DRILL_UP
                        })
                    }
                    
                    // 向下传播权限
                    acl.flattenChildren().forEach { child ->
                        changedACLs.add(child)
                        policiesToAdd.add(policyWithACL.copy {
                            aclId = child.id
                            strategyType = StrategyType.DRILL_DOWN
                        })
                    }
                }
                
                // 处理删除权限
                policiesToDelete.addAll(deleted.map { it.id })
            }
        }

        // 批量执行数据库操作
        if (policiesToAdd.isNotEmpty()) {
            savePolicies(policiesToAdd)
        }
        
        if (policiesToDelete.isNotEmpty()) {
            deleteRefPolicies(policiesToDelete)
        }

        // 批量发布权限更新事件
        publishBatchPolicyUpdateEvents(changedACLs.toList())
    }

    /**
     * 继承父级权限
     */
    private fun inheritParentPolicies(aclId: String, parentResourceId: String?): List<Policy> {
        return parentResourceId?.let { parentId ->
            val parentPolicies = fetchRootPolicies(parentId)
            PolicyTool.refPolicy(aclId, StrategyType.DRILL_DOWN, parentPolicies)
        } ?: emptyList()
    }

    /**
     * 删除继承的权限策略
     */
    private fun deleteInheritedPolicies(resourceId: String) {
        kSqlClient.createDelete(Policy::class) {
            where(table.acl.resourceId eq resourceId)
            where(table.strategyType valueIn listOf(StrategyType.DRILL_DOWN, StrategyType.DRILL_UP))
        }.execute()
    }

    /**
     * 获取根权限策略（非继承的）
     */
    private fun fetchRootPolicies(resourceId: String): List<Policy> {
        return kSqlClient.createQuery(Policy::class) {
            where(table.acl.resourceId eq resourceId)
            where(table.strategyType eq StrategyType.ROOT)
            select(table.fetchBy { allTableFields() })
        }.execute()
    }

    /**
     * 获取子级ACL列表
     */
    private fun fetchChildrenACLs(parentResourceId: String): List<ACL> {
        return kSqlClient.createQuery(ACL::class) {
            where(table.parent.resourceId eq parentResourceId)
            select(table.fetchBy {
                resourceId()
                resourceType()
                resourceName()
            })
        }.execute()
    }

    /**
     * 根据资源ID获取ACL
     */
    private fun fetchACLByResourceId(resourceId: String): ACL {
        return kSqlClient.createQuery(ACL::class) {
            where(table.resourceId eq resourceId)
            select(table.fetchBy {
                resourceId()
                resourceType()
                resourceName()
                `parent*`()
                `children*`()
            })
        }.fetchOneOrNull() ?: throw IllegalArgumentException("ACL not found for resource: $resourceId")
    }

    /**
     * 保存ACL
     */
    private fun saveACL(acl: ACL): ACL {
        return kSqlClient.entities.save(acl).modifiedEntity
    }

    /**
     * 批量保存权限策略
     */
    private fun savePolicies(policies: List<Policy>) {
        if (policies.isNotEmpty()) {
            kSqlClient.entities.saveEntities(policies)
        }
    }

    /**
     * 删除引用权限
     */
    private fun deleteRefPolicies(policyIds: List<String>) {
        if (policyIds.isNotEmpty()) {
            kSqlClient.createDelete(Policy::class) {
                where(table.id valueIn policyIds)
                // 同时删除引用这些权限的策略
                where(table.refPolicy.id valueIn policyIds)
            }.execute()
        }
    }

    /**
     * 发布权限更新事件
     */
    private fun publishPolicyUpdateEvent(resourceIds: List<String>, resourceType: String, resourceName: String) {
        applicationEventPublisher.publishEvent(
            ResourcePolicyUpdate(resourceIds, resourceType, resourceName)
        )
    }

    /**
     * 批量发布权限更新事件
     */
    private fun publishBatchPolicyUpdateEvents(acls: List<ACL>) {
        acls.groupBy { it.resourceType }.forEach { (_, aclGroup) ->
            publishPolicyUpdateEvent(
                aclGroup.map { it.resourceId },
                aclGroup.first().resourceType,
                aclGroup.first().resourceName
            )
        }
    }
}

/**
 * ACL授权请求数据类
 */
data class ACLAuthorizationRequest(
    val resourceId: String,
    val policies: List<PolicySave>
)

/**
 * 权限策略保存数据类
 */
data class PolicySave(
    val policyType: zone.loong.cube.framework.data.kernel.model.PolicyType,
    val value: String,
    val ruleId: String? = null
) {
    fun toEntity(): Policy = Policy {
        this.policyType = <EMAIL>
        this.value = <EMAIL>
        this.strategyType = StrategyType.ROOT
        this.ruleId = <EMAIL>
    }
}
