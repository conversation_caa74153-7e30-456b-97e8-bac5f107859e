package zone.loong.cube.framework.data.jimmer.service

import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.model.*
import zone.loong.cube.framework.data.kernel.model.PolicyType
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.LocalDate
import java.time.temporal.ChronoUnit

/**
 * 策略驱动的权限传递服务
 * 实现复杂业务规则下的动态权限传递
 */
@Service
class PolicyBasedPermissionPropagationService {
    
    private val logger = LoggerFactory.getLogger(PolicyBasedPermissionPropagationService::class.java)
    
    /**
     * 权限传递上下文
     */
    data class PropagationContext(
        val sourceResourceId: String,
        val userId: String,
        val changedPolicies: List<Policy>,
        val propagationType: PropagationType,
        val grantTime: LocalDateTime = LocalDateTime.now()
    )
    
    /**
     * 传递类型
     */
    enum class PropagationType {
        PROJECT_TEAM,           // 项目团队
        PROJECT_STAKEHOLDER,    // 项目干系人
        TIME_RESTRICTED,        // 时间限制
        LOCATION_RESTRICTED,    // 地理限制
        COMPLIANCE_REQUIRED     // 合规要求
    }
    
    /**
     * 权限传递策略接口
     */
    interface PermissionPropagationPolicy {
        fun shouldPropagate(context: PropagationContext): Boolean
        fun transformPermissions(permissions: Set<String>, context: PropagationContext): Set<String>
        fun findTargets(context: PropagationContext): Set<String>
        fun getPolicyName(): String
    }
    
    /**
     * 策略注册表
     */
    private val policies = mutableMapOf<String, PermissionPropagationPolicy>()
    
    init {
        // 注册内置策略
        registerPolicy(ProjectBasedPropagationPolicy())
        registerPolicy(TimeBasedPropagationPolicy())
        registerPolicy(LocationBasedPropagationPolicy())
    }
    
    /**
     * 注册权限传递策略
     */
    fun registerPolicy(policy: PermissionPropagationPolicy) {
        policies[policy.getPolicyName()] = policy
        logger.info("注册权限传递策略: {}", policy.getPolicyName())
    }
    
    /**
     * 执行策略驱动的权限传递
     */
    @Transactional
    fun executeStrategyBasedPropagation(context: PropagationContext): PropagationResult {
        logger.debug("开始策略驱动权限传递: {}", context.sourceResourceId)
        
        val applicablePolicies = findApplicablePolicies(context)
        if (applicablePolicies.isEmpty()) {
            logger.debug("没有找到适用的传递策略")
            return PropagationResult.empty()
        }
        
        val results = mutableListOf<PolicyExecutionResult>()
        
        applicablePolicies.forEach { policy ->
            try {
                val result = executeSinglePolicy(policy, context)
                results.add(result)
                logger.debug("策略 {} 执行完成，影响 {} 个资源", 
                    policy.getPolicyName(), result.affectedResources.size)
            } catch (e: Exception) {
                logger.error("策略 {} 执行失败", policy.getPolicyName(), e)
                results.add(PolicyExecutionResult.failed(policy.getPolicyName(), e.message ?: "未知错误"))
            }
        }
        
        return mergeResults(results)
    }
    
    /**
     * 查找适用的策略
     */
    private fun findApplicablePolicies(context: PropagationContext): List<PermissionPropagationPolicy> {
        return policies.values.filter { policy ->
            try {
                policy.shouldPropagate(context)
            } catch (e: Exception) {
                logger.warn("策略 {} 检查失败", policy.getPolicyName(), e)
                false
            }
        }
    }
    
    /**
     * 执行单个策略
     */
    private fun executeSinglePolicy(
        policy: PermissionPropagationPolicy, 
        context: PropagationContext
    ): PolicyExecutionResult {
        
        // 1. 转换权限
        val originalPermissions = context.changedPolicies.map { it.value }.toSet()
        val transformedPermissions = policy.transformPermissions(originalPermissions, context)
        
        // 2. 查找目标资源
        val targetResources = policy.findTargets(context)
        
        // 3. 执行权限传递
        val affectedResources = mutableSetOf<String>()
        
        targetResources.forEach { targetResourceId ->
            transformedPermissions.forEach { permission ->
                try {
                    applyPermissionToResource(targetResourceId, permission, context)
                    affectedResources.add(targetResourceId)
                } catch (e: Exception) {
                    logger.error("应用权限到资源失败: {} -> {}", permission, targetResourceId, e)
                }
            }
        }
        
        return PolicyExecutionResult.success(
            policyName = policy.getPolicyName(),
            affectedResources = affectedResources,
            appliedPermissions = transformedPermissions
        )
    }
    
    /**
     * 应用权限到资源
     */
    private fun applyPermissionToResource(
        targetResourceId: String, 
        permission: String, 
        context: PropagationContext
    ) {
        // 这里应该调用实际的权限应用逻辑
        // 例如：aclManagementService.grantPermission(targetResourceId, permission, context.userId)
        logger.debug("应用权限: {} -> {} (用户: {})", permission, targetResourceId, context.userId)
    }
    
    /**
     * 合并执行结果
     */
    private fun mergeResults(results: List<PolicyExecutionResult>): PropagationResult {
        val allAffectedResources = results.flatMap { it.affectedResources }.toSet()
        val allAppliedPermissions = results.flatMap { it.appliedPermissions }.toSet()
        val errors = results.filter { !it.success }.map { it.errorMessage ?: "未知错误" }
        
        return PropagationResult(
            success = errors.isEmpty(),
            affectedResourceCount = allAffectedResources.size,
            appliedPermissions = allAppliedPermissions,
            executedPolicies = results.map { it.policyName },
            errors = errors
        )
    }
}

/**
 * 项目驱动的权限传递策略
 */
class ProjectBasedPropagationPolicy : PolicyBasedPermissionPropagationService.PermissionPropagationPolicy {
    
    override fun shouldPropagate(context: PolicyBasedPermissionPropagationService.PropagationContext): Boolean {
        // 检查是否是项目相关的权限
        val hasProjectPermission = context.changedPolicies.any { 
            it.value.startsWith("PROJECT_") 
        }
        
        if (!hasProjectPermission) return false
        
        // 检查项目状态（这里简化实现）
        val project = getProjectByResource(context.sourceResourceId)
        return project?.isActive() == true
    }
    
    override fun transformPermissions(
        permissions: Set<String>, 
        context: PolicyBasedPermissionPropagationService.PropagationContext
    ): Set<String> {
        val project = getProjectByResource(context.sourceResourceId)
        val userRole = getUserProjectRole(context.userId, project?.id)
        
        return permissions.mapNotNull { permission ->
            when {
                permission == "PROJECT_ADMIN" && userRole != "MANAGER" -> "PROJECT_MEMBER"
                permission.contains("SENSITIVE") && userRole != "CORE_TEAM" -> null
                else -> "PROJECT_${project?.code}_$permission"
            }
        }.toSet()
    }
    
    override fun findTargets(context: PolicyBasedPermissionPropagationService.PropagationContext): Set<String> {
        val project = getProjectByResource(context.sourceResourceId)
        return getProjectResources(project?.id).toSet()
    }
    
    override fun getPolicyName(): String = "ProjectBasedPropagation"
    
    // 模拟方法
    private fun getProjectByResource(resourceId: String): Project? = null
    private fun getUserProjectRole(userId: String, projectId: String?): String = "MEMBER"
    private fun getProjectResources(projectId: String?): List<String> = emptyList()
}

/**
 * 时间驱动的权限传递策略
 */
class TimeBasedPropagationPolicy : PolicyBasedPermissionPropagationService.PermissionPropagationPolicy {
    
    companion object {
        val TIME_RESTRICTED_PERMISSIONS = setOf(
            "MONTH_END_CLOSING", "EMERGENCY_ACCESS", "WORK_TIME_ONLY"
        )
    }
    
    override fun shouldPropagate(context: PolicyBasedPermissionPropagationService.PropagationContext): Boolean {
        val hasTimeRestrictedPermission = context.changedPolicies.any { 
            it.value in TIME_RESTRICTED_PERMISSIONS 
        }
        
        if (!hasTimeRestrictedPermission) return false
        
        return when {
            context.changedPolicies.any { it.value == "MONTH_END_CLOSING" } -> isMonthEndPeriod()
            context.changedPolicies.any { it.value.contains("WORK_TIME") } -> isWorkingHours()
            else -> true
        }
    }
    
    override fun transformPermissions(
        permissions: Set<String>, 
        context: PolicyBasedPermissionPropagationService.PropagationContext
    ): Set<String> {
        return permissions.mapNotNull { permission ->
            when {
                permission == "MONTH_END_CLOSING" && isMonthEndPeriod() -> 
                    "MONTH_END_CLOSING_UNTIL_${getMonthEndDeadline()}"
                permission.contains("WORK_TIME") && isWorkingHours() -> 
                    "${permission}_UNTIL_${getWorkDayEnd()}"
                permission.contains("EMERGENCY") -> 
                    "${permission}_EXPIRES_${LocalDateTime.now().plusHours(24)}"
                else -> permission
            }
        }.toSet()
    }
    
    override fun findTargets(context: PolicyBasedPermissionPropagationService.PropagationContext): Set<String> {
        return when {
            context.changedPolicies.any { it.value == "MONTH_END_CLOSING" } -> 
                getFinancialResources().toSet()
            context.changedPolicies.any { it.value.contains("WORK_TIME") } -> 
                getActiveUserResources().toSet()
            else -> emptySet()
        }
    }
    
    override fun getPolicyName(): String = "TimeBasedPropagation"
    
    private fun isMonthEndPeriod(): Boolean {
        val now = LocalDate.now()
        val lastDayOfMonth = now.withDayOfMonth(now.lengthOfMonth())
        val daysDiff = ChronoUnit.DAYS.between(now, lastDayOfMonth)
        return daysDiff <= 3
    }
    
    private fun isWorkingHours(): Boolean {
        val now = LocalTime.now()
        return now.isAfter(LocalTime.of(9, 0)) && now.isBefore(LocalTime.of(18, 0))
    }
    
    private fun getMonthEndDeadline(): LocalDate = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth())
    private fun getWorkDayEnd(): LocalTime = LocalTime.of(18, 0)
    private fun getFinancialResources(): List<String> = emptyList()
    private fun getActiveUserResources(): List<String> = emptyList()
}

/**
 * 地理位置驱动的权限传递策略
 */
class LocationBasedPropagationPolicy : PolicyBasedPermissionPropagationService.PermissionPropagationPolicy {
    
    override fun shouldPropagate(context: PolicyBasedPermissionPropagationService.PropagationContext): Boolean {
        val hasLocationRestrictedPermission = context.changedPolicies.any { 
            it.value.contains("CONFIDENTIAL") || it.value.contains("REGIONAL") 
        }
        
        if (!hasLocationRestrictedPermission) return false
        
        val userLocation = getUserLocation(context.userId)
        return when {
            context.changedPolicies.any { it.value.contains("CONFIDENTIAL") } -> 
                isInCompanyPremises(userLocation)
            else -> true
        }
    }
    
    override fun transformPermissions(
        permissions: Set<String>, 
        context: PolicyBasedPermissionPropagationService.PropagationContext
    ): Set<String> {
        val userLocation = getUserLocation(context.userId)
        
        return permissions.mapNotNull { permission ->
            when {
                permission.contains("CONFIDENTIAL") && !isInCompanyPremises(userLocation) -> 
                    "${permission}_READ_ONLY"
                permission.contains("REGIONAL") -> 
                    "${permission}_${getRegionCode(userLocation)}"
                else -> permission
            }
        }.toSet()
    }
    
    override fun findTargets(context: PolicyBasedPermissionPropagationService.PropagationContext): Set<String> {
        val userLocation = getUserLocation(context.userId)
        
        return when {
            context.changedPolicies.any { it.value.contains("CONFIDENTIAL") } -> 
                getResourcesInBuilding(userLocation.buildingId).toSet()
            context.changedPolicies.any { it.value.contains("REGIONAL") } -> 
                getResourcesInRegion(getRegionCode(userLocation)).toSet()
            else -> emptySet()
        }
    }
    
    override fun getPolicyName(): String = "LocationBasedPropagation"
    
    // 模拟数据结构和方法
    data class UserLocation(val buildingId: String, val regionCode: String)
    
    private fun getUserLocation(userId: String): UserLocation = UserLocation("BUILD_001", "CN_EAST")
    private fun isInCompanyPremises(location: UserLocation): Boolean = location.buildingId.startsWith("BUILD_")
    private fun getRegionCode(location: UserLocation): String = location.regionCode
    private fun getResourcesInBuilding(buildingId: String): List<String> = emptyList()
    private fun getResourcesInRegion(regionCode: String): List<String> = emptyList()
}

// 数据类定义
data class Project(val id: String, val code: String, val status: String) {
    fun isActive(): Boolean = status == "ACTIVE"
}

data class PropagationResult(
    val success: Boolean,
    val affectedResourceCount: Int,
    val appliedPermissions: Set<String>,
    val executedPolicies: List<String>,
    val errors: List<String>
) {
    companion object {
        fun empty() = PropagationResult(true, 0, emptySet(), emptyList(), emptyList())
    }
}

data class PolicyExecutionResult(
    val success: Boolean,
    val policyName: String,
    val affectedResources: Set<String>,
    val appliedPermissions: Set<String>,
    val errorMessage: String?
) {
    companion object {
        fun success(policyName: String, affectedResources: Set<String>, appliedPermissions: Set<String>) =
            PolicyExecutionResult(true, policyName, affectedResources, appliedPermissions, null)
        
        fun failed(policyName: String, errorMessage: String) =
            PolicyExecutionResult(false, policyName, emptySet(), emptySet(), errorMessage)
    }
}
