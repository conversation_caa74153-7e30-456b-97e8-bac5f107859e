package zone.loong.cube.interior.controller

import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.interior.model.InteriorPositionsDatum
import zone.loong.cube.interior.model.dto.InteriorPositionsDatumSpecification
import zone.loong.cube.interior.service.InteriorPositionsDatumService
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.interior.model.dto.SaveInteriorPositionsDatum

@RestController
@RequestMapping("/interior/positionsDatum")
class  InteriorPositionsDatumController(
    private val interiorPositionsDatumService: InteriorPositionsDatumService
) {


    @PostMapping("/page")
    fun fetchProjectDatum(@RequestBody pageQuery: PageQuery<InteriorPositionsDatumSpecification>): Page<InteriorPositionsDatum> {
        return interiorPositionsDatumService.fetchPositionsDatum(pageQuery)
    }

    @PostMapping("/save")
    fun save(@RequestBody saveInteriorPositionsDatum: SaveInteriorPositionsDatum): InteriorPositionsDatum {
        return interiorPositionsDatumService.save(saveInteriorPositionsDatum)
    }

    @GetMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        interiorPositionsDatumService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): InteriorPositionsDatum {
        return interiorPositionsDatumService.detail(id)
    }
}