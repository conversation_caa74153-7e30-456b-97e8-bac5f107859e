package zone.loong.cube.framework.storage.kernel.model

import org.babyfish.jimmer.sql.*
import org.dromara.x.file.storage.core.hash.HashInfo
import zone.loong.cube.framework.data.jimmer.ULIDGenerator
import java.time.LocalDateTime

/**
 * 文件分片信息表
 */
@Entity
@Table(name = "file_part_detail")
interface FilePartDetail {

    /**
     * 分片id
     */
    @Id
    @GeneratedValue(generatorType = ULIDGenerator::class)
    val id: String

    /**
     * 存储平台
     */
    val platform: String

    /**
     * 上传ID，仅在手动分片上传时使用
     */
    val uploadId: String

    /**
     * 分片ETag
     */
    val eTag: String

    /**
     * 分片号。每一个上传的分片都有一个分片号，一般情况下取值范围是110000
     */
    val partNumber: Int

    /**
     * 文件大小，单位字节
     */
    val partSize: Long

    /**
     * 哈希信息
     */
    @Serialized
    val hashInfo: HashInfo

    /**
     * 创建时间
     */
    val createTime: LocalDateTime
}

