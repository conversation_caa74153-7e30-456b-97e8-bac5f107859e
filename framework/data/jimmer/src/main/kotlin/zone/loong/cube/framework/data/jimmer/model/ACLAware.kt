package zone.loong.cube.framework.data.jimmer.model

import com.fasterxml.jackson.annotation.JsonIgnore
import org.babyfish.jimmer.sql.MappedSuperclass
import org.babyfish.jimmer.sql.OneToOne
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import kotlin.reflect.full.allSuperclasses

@MappedSuperclass
interface ACLAware :IDAware {
    @OneToOne()
    val acl: ACL?

    @JsonIgnore
    fun resourceName(): String {
        return getACLResourceAnnotation().name
    }

    @JsonIgnore
    fun resourceType(): String {
        return getACLResourceAnnotation().type
    }

    @JsonIgnore
    fun parentResourceId(): String? {
        val annotation = getACLResourceAnnotation()
        if (annotation.parentResourceId.isEmpty()) return null

        // 处理 SpEL 表达式
        return if (annotation.parentResourceId.startsWith("#")) {
            evaluateSpELExpression(annotation.parentResourceId.removePrefix("#"))
        } else {
            annotation.parentResourceId
        }
    }

    private fun getACLResourceAnnotation(): zone.loong.cube.framework.data.kernel.annotations.ACL {
      return this::class.allSuperclasses.flatMap {
            it.annotations
        }.find {
            it is zone.loong.cube.framework.data.kernel.annotations.ACL
        } as zone.loong.cube.framework.data.kernel.annotations.ACL? ?:  throw RuntimeException("${this.javaClass.name} 继承了[ACLAware]但是缺少[@ACL] 注解")

    }

    private fun evaluateSpELExpression(expression: String): String? {
        val parser = SpelExpressionParser()
        val context = StandardEvaluationContext(this)

        return try {
            parser.parseExpression(expression).getValue(context) as? String
        } catch (e: Exception) {
            throw RuntimeException("EL表达式执行错误: $expression", e)
        }
    }

}