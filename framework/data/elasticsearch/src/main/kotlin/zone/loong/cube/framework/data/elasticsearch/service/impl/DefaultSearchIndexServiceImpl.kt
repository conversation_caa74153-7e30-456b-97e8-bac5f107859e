package zone.loong.cube.framework.data.elasticsearch.service.impl

import org.springframework.data.elasticsearch.core.ElasticsearchOperations
import zone.loong.cube.framework.data.elasticsearch.service.SearchIndexService
import kotlin.reflect.KClass

class DefaultSearchIndexServiceImpl(private val elasticsearchOperations: ElasticsearchOperations) : SearchIndexService {
    override fun delete(vararg models: KClass<*>) {
        models.forEach {
            elasticsearchOperations.indexOps(it.java).delete()
        }
    }

    override fun create(vararg models: KClass<*>) {
        models.forEach {
            elasticsearchOperations.indexOps(it.java).createWithMapping()
        }
    }

    override fun dropAndCreate(vararg models: KClass<*>) {
        models.forEach {
            elasticsearchOperations.indexOps(it.java).delete()
            elasticsearchOperations.indexOps(it.java).createWithMapping()
        }
    }
}