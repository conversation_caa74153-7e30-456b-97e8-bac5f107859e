package zone.loong.cube.framework.auth.security.configuration

import cn.dev33.satoken.config.SaTokenConfig
import cn.dev33.satoken.`fun`.SaFunction
import cn.dev33.satoken.interceptor.SaInterceptor
import cn.dev33.satoken.router.SaHttpMethod
import cn.dev33.satoken.router.SaRouter
import cn.dev33.satoken.stp.StpUtil
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import org.springframework.web.servlet.config.annotation.InterceptorRegistry
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer
import zone.loong.cube.framework.auth.security.CubeSecurityProperties
import zone.loong.cube.framework.auth.security.advance.AuthExceptionHandler
import zone.loong.cube.framework.auth.security.service.AccountService
import zone.loong.cube.framework.auth.security.service.AuthService
import zone.loong.cube.framework.auth.security.service.impl.DefaultAccountService
import zone.loong.cube.framework.auth.security.service.impl.DefaultAuthService
import zone.loong.cube.framework.kernel.autoconfigure.FrameworkAutoConfigure
import zone.loong.cube.framework.kernel.extension.ExtensionExecutor


@Configuration
@EnableConfigurationProperties(CubeSecurityProperties::class)
@Import(FrameworkAutoConfigure::class,AuthExceptionHandler::class)
class SecurityConfiguration(private val cubeSecurityProperties: CubeSecurityProperties) :
    WebMvcConfigurer {

    @Bean
    @ConditionalOnMissingBean(AccountService::class)
    fun accountService(): AccountService {
        return DefaultAccountService()
    }

    @Bean
    @ConditionalOnMissingBean(AuthService::class)
    fun securityService(
        extensionExecutor: ExtensionExecutor,
    ): AuthService {
        return DefaultAuthService(extensionExecutor)
    }


    @Bean
    @Primary
    fun saTokenConfig(): SaTokenConfig {
        return SaTokenConfig().apply {
            tokenName = cubeSecurityProperties.tokenName
            sign.secretKey = cubeSecurityProperties.securityKey
            // 接口调用时的时间戳允许的差距（单位：ms），-1 代表不校验差距，默认15分钟
            sign.timestampDisparity = cubeSecurityProperties.timestampDisparity
            //  token 有效期（单位：秒），默认30天，-1代表永不过期
            timeout = cubeSecurityProperties.expireTime
            // token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
            activeTimeout = cubeSecurityProperties.activeTimeout
            isPrint = false
        }
    }

    override fun addInterceptors(registry: InterceptorRegistry) {
        registry.addInterceptor(SaInterceptor {
            // 登录校验 -- 拦截所有路由，并排除/user/doLogin 用于开放登录
            SaRouter.match(SaHttpMethod.OPTIONS).free { }.back()
            SaRouter.match("/**", SaFunction {
                try {
                    StpUtil.checkLogin()
                } catch (e: Exception) {
                    throw e
                }
            })
        }).excludePathPatterns(cubeSecurityProperties.exclude)
    }

}