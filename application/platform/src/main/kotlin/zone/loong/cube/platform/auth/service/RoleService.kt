package zone.loong.cube.platform.auth.service

import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.platform.auth.model.Role
import zone.loong.cube.platform.auth.model.dto.*

interface RoleService {
    fun fetchRole(pageQuery: PageQuery<RoleSpecification>): Page<Role>
    fun fetchAccounts(roleId: String): List<AccountWithUserInfo>
    fun fetchPermissions(roleId: String): List<String>
    fun authorize(roleAuthorization: RoleAuthorization)
    fun saveRole(role: RoleSave): Role
    fun roleAccount(roleAccount: RoleAccount): Role
    fun rolePermission(rolePermission: RolePermission): Role
}