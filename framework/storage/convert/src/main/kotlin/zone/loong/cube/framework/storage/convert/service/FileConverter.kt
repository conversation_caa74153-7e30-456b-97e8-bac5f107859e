package zone.loong.cube.framework.storage.convert.service

import java.io.File
import zone.loong.cube.framework.storage.convert.service.FileConvertTask
import zone.loong.cube.framework.storage.convert.service.TargetType
import zone.loong.cube.framework.storage.kernel.exception.FileException

/**
 * 文件转换器接口，支持多种类型文件的转换。
 * 只负责生成本地目标文件，不负责存储。
 */
interface FileConverter {
    /**
     * 执行文件转换，返回转换后的本地文件。
     * @param task 转换任务，包含源文件ID、目标类型等信息
     * @return 转换后的本地文件
     * @throws FileException 转换失败时抛出
     */
    suspend fun convert(task: FileConvertTask): File

    /**
     * 判断是否支持该目标类型转换。
     * @param targetType 目标类型
     * @return 是否支持
     */
    fun supports(targetType: TargetType): Boolean
}
