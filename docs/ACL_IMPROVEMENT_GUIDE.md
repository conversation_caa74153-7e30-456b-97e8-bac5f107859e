# ACL权限系统改进方案

## 问题分析

### 当前实现的问题
1. **ACL初始化逻辑分散**：`ACLDraftInterceptor` 和 `ResourceListener` 都处理ACL初始化，逻辑重复
2. **树结构迁移缺乏专门处理**：没有统一的机制处理父节点变更时的权限重刷
3. **事务同步使用复杂**：大量使用 `TransactionSync.runBeforeCommit`，增加了复杂性
4. **权限传递逻辑重复**：在多个地方都有类似的权限传递代码

## 改进方案

### 核心设计思想
1. **统一管理**：创建 `ACLManagementService` 统一处理所有ACL相关操作
2. **职责分离**：拦截器只负责触发，具体逻辑由服务层处理
3. **事件驱动**：使用Spring事件机制处理复杂的权限传播
4. **批量操作**：支持批量权限更新，提高性能

### 新增组件

#### 1. ACLManagementService
统一的ACL管理服务，提供以下功能：
- `initializeACL()`: 初始化新资源的ACL
- `handleParentMigration()`: 处理父节点迁移
- `refreshInheritedPolicies()`: 刷新继承权限
- `batchAuthorization()`: 批量授权操作

#### 2. ImprovedACLDraftInterceptor
改进的拦截器，逻辑更简洁：
- 新建资源：调用 `ACLManagementService.initializeACL()`
- 父级变更：调用 `ACLManagementService.handleParentMigration()`

#### 3. 新的事件类型
- `ResourceParentChanged`: 资源父级变更事件
- `ResourceDeleted`: 资源删除事件
- `BatchResourcePolicyUpdate`: 批量权限更新事件

## 使用方式

### 1. 基本权限授权
```kotlin
@Autowired
private lateinit var aclManagementService: ACLManagementService

// 单个资源授权
val request = ACLAuthorizationRequest(
    resourceId = "resource-123",
    policies = listOf(
        PolicySave(PolicyType.USER, "user001"),
        PolicySave(PolicyType.ROLE, "admin")
    )
)
aclManagementService.batchAuthorization(listOf(request))

// 批量授权
val requests = listOf(
    ACLAuthorizationRequest("resource-1", policies1),
    ACLAuthorizationRequest("resource-2", policies2)
)
aclManagementService.batchAuthorization(requests)
```

### 2. 处理树结构迁移
```kotlin
// 当资源的父级发生变更时
aclManagementService.handleParentMigration(
    resourceId = "child-resource",
    oldParentId = "old-parent",
    newParentId = "new-parent"
)
```

### 3. 刷新权限继承
```kotlin
// 刷新单个资源的权限继承
aclManagementService.refreshInheritedPolicies("resource-id", "parent-id")

// 递归刷新所有子节点
aclManagementService.refreshChildrenPolicies("parent-resource-id")
```

## 迁移步骤

### 1. 启用新实现
在 `application.yml` 中配置：
```yaml
cube:
  acl:
    improved:
      enabled: true  # 启用改进的ACL实现
```

### 2. 替换现有服务调用
将现有的 `ACLServiceImpl` 调用替换为 `ImprovedACLServiceImpl`：

```kotlin
// 旧方式
@Autowired
private lateinit var aclService: ACLService

// 新方式
@Autowired
private lateinit var aclService: ImprovedACLServiceImpl
```

### 3. 处理特殊场景
对于需要手动处理权限刷新的场景：

```kotlin
// 批量刷新资源权限
aclService.batchRefreshResourcePermissions(resourceIds)

// 单个资源权限刷新
aclService.refreshResourcePermissions(resourceId)
```

## 性能优化

### 1. 批量操作
新实现支持批量权限更新，减少数据库交互：
- 批量保存权限策略
- 批量删除权限策略
- 批量发布事件

### 2. 事务优化
- 减少 `TransactionSync` 的使用
- 合并数据库操作
- 优化权限传播逻辑

### 3. 缓存支持
可以在 `ACLManagementService` 中添加缓存支持：
```kotlin
@Cacheable("acl-cache")
fun fetchACLByResourceId(resourceId: String): ACL {
    // 实现缓存逻辑
}
```

## 向后兼容

### 1. 配置开关
通过配置可以在新旧实现之间切换，确保平滑迁移。

### 2. 接口保持不变
`ACLService` 接口保持不变，只是实现方式更优雅。

### 3. 数据结构不变
数据库表结构和实体类保持不变，只是操作逻辑优化。

## 测试建议

### 1. 单元测试
为 `ACLManagementService` 编写全面的单元测试：
- 权限初始化测试
- 父级迁移测试
- 批量操作测试

### 2. 集成测试
测试完整的权限传播流程：
- 创建资源时的权限继承
- 移动资源时的权限更新
- 删除资源时的权限清理

### 3. 性能测试
对比新旧实现的性能：
- 批量操作性能
- 权限传播效率
- 数据库查询优化

## 总结

这个改进方案解决了当前ACL系统的主要问题：
1. **统一管理**：所有ACL操作都通过 `ACLManagementService` 处理
2. **简化逻辑**：拦截器和监听器逻辑更简洁
3. **性能优化**：支持批量操作，减少数据库交互
4. **易于维护**：代码结构更清晰，职责分离明确
5. **向后兼容**：可以平滑迁移，不影响现有功能

通过这个改进方案，ACL权限系统将更加健壮、高效和易于维护。
