package zone.loong.cube.interior.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Service
import zone.loong.cube.interior.model.InteriorProject
import zone.loong.cube.interior.model.dto.InteriorProjectSpecification
import zone.loong.cube.interior.model.dto.SaveInteriorProject
import zone.loong.cube.interior.service.InteriorProjectService
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.interior.model.dto.InteriorProjectDatumSpecification
import zone.loong.cube.interior.service.InteriorProjectDatumService

@Service
class InteriorProjectServiceImpl(
    private val kSqlClient: KSqlClient,
    private val interiorProjectDatumService: InteriorProjectDatumService
) : InteriorProjectService {

    override fun fetchProjects(pageQuery: PageQuery<InteriorProjectSpecification>): Page<InteriorProject> {
        return kSqlClient.page(pageQuery){
            select(table)
        }
    }

    override fun save(saveInteriorProject: SaveInteriorProject): InteriorProject {
        return (saveInteriorProject.id?.let {
            kSqlClient.entities.saveCommand(saveInteriorProject, SaveMode.UPSERT).execute().modifiedEntity
        }?:let{
            kSqlClient.entities.saveCommand(saveInteriorProject, SaveMode.INSERT_ONLY).execute().modifiedEntity
        })
    }

    override fun delete(id: String) :  Unit {
        val fetchProjectDatum = interiorProjectDatumService.fetchProjectDatum(
            PageQuery(
                InteriorProjectDatumSpecification(projectId = id),
                1,
                1,
                listOf()
            )
        )
        if (fetchProjectDatum.total > 0) {
            throw Exception("该项目下有数据，请先删除数据")
        }
        kSqlClient.deleteById(InteriorProject::class, id)
    }

    override fun detail(id: String): InteriorProject {
        return kSqlClient.findById(InteriorProject::class, "111") ?: throw Exception("该项目不存在")
    }


}