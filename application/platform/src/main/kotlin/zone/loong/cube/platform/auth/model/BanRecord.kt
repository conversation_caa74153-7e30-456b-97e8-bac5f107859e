package zone.loong.cube.platform.auth.model


import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.Table
import zone.loong.cube.framework.auth.security.model.BanLevel
import zone.loong.cube.framework.auth.security.model.BanRecord
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import java.time.LocalDateTime

@Entity
@Table(name = "AUTH_BAN_RECORD")
interface BanRecord : BanRecord, AuditAware {
    // 封禁等级
    override val banLevel: BanLevel

    // 封禁原因
    override val banReason: String

    // 封禁时间
    override val banAt: LocalDateTime

    //自动解封时间
    override val banUntil: LocalDateTime

    // 封禁者
    override val banBy: String

    //是否解封
    override val unBan: Boolean

    // 解封原因
    override val unBanReason: String

    // 解封者
    override val unBanBy: String

    // 解封时间
    override val unBanAt: LocalDateTime

    @ManyToOne
    val account: Account

}