package zone.loong.cube.framework.data.jimmer.service

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.data.redis.core.RedisTemplate
import zone.loong.cube.framework.data.jimmer.model.*
import zone.loong.cube.framework.data.kernel.model.StrategyType
import zone.loong.cube.framework.data.kernel.model.PolicyType
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.time.Duration
import java.util.concurrent.CompletableFuture
import java.util.concurrent.TimeUnit
import org.springframework.scheduling.annotation.Async

/**
 * 高级ACL权限传递服务
 * 集成多种权限传递模式和性能优化策略
 */
@Service
class AdvancedACLPropagationService(
    private val kSqlClient: KSqlClient,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val redisTemplate: RedisTemplate<String, Any>,
    private val performanceMonitor: ACLPerformanceMonitor
) {
    
    private val logger = LoggerFactory.getLogger(AdvancedACLPropagationService::class.java)
    
    /**
     * 权限传递上下文
     */
    data class PropagationContext(
        val sourceResourceId: String,
        val changedPolicies: List<Policy>,
        val strategy: PropagationStrategy = PropagationStrategy.INTELLIGENT,
        val maxDepth: Int = 10,
        val batchSize: Int = 100,
        val async: Boolean = true
    )
    
    /**
     * 权限传递策略
     */
    enum class PropagationStrategy {
        IMMEDIATE,      // 立即传递
        DEFERRED,       // 延迟传递
        LAZY,          // 懒加载传递
        BATCH,         // 批量传递
        INTELLIGENT    // 智能选择
    }
    
    /**
     * 智能权限传递入口
     */
    @Transactional
    fun propagatePermissions(context: PropagationContext): PropagationResult {
        val monitorContext = performanceMonitor.startOperation("propagatePermissions", context.sourceResourceId)
        
        return try {
            val strategy = if (context.strategy == PropagationStrategy.INTELLIGENT) {
                selectOptimalStrategy(context)
            } else {
                context.strategy
            }
            
            val result = when (strategy) {
                PropagationStrategy.IMMEDIATE -> immediatePropagate(context)
                PropagationStrategy.DEFERRED -> deferredPropagate(context)
                PropagationStrategy.LAZY -> lazyPropagate(context)
                PropagationStrategy.BATCH -> batchPropagate(context)
                PropagationStrategy.INTELLIGENT -> batchPropagate(context) // fallback
            }
            
            performanceMonitor.endOperation(monitorContext, result.affectedResourceCount)
            result
        } catch (e: Exception) {
            performanceMonitor.endOperation(monitorContext, 0)
            throw e
        }
    }
    
    /**
     * 智能选择传递策略
     */
    private fun selectOptimalStrategy(context: PropagationContext): PropagationStrategy {
        val estimatedAffectedCount = estimateAffectedResourceCount(context.sourceResourceId)
        val systemLoad = getCurrentSystemLoad()
        
        return when {
            estimatedAffectedCount < 50 && systemLoad < 0.7 -> PropagationStrategy.IMMEDIATE
            estimatedAffectedCount < 200 -> PropagationStrategy.BATCH
            else -> PropagationStrategy.DEFERRED
        }
    }
    
    /**
     * 立即权限传递
     */
    private fun immediatePropagate(context: PropagationContext): PropagationResult {
        logger.debug("执行立即权限传递: {}", context.sourceResourceId)
        
        val affectedResources = findAffectedResources(context)
        val propagationPlan = createPropagationPlan(context, affectedResources)
        
        return executePropagationPlan(propagationPlan)
    }
    
    /**
     * 批量权限传递
     */
    private fun batchPropagate(context: PropagationContext): PropagationResult {
        logger.debug("执行批量权限传递: {}", context.sourceResourceId)
        
        val affectedResources = findAffectedResources(context)
        val batches = affectedResources.chunked(context.batchSize)
        
        var totalAffected = 0
        val errors = mutableListOf<String>()
        
        batches.forEach { batch ->
            try {
                val batchContext = context.copy(sourceResourceId = batch.first())
                val batchPlan = createPropagationPlan(batchContext, batch)
                val result = executePropagationPlan(batchPlan)
                totalAffected += result.affectedResourceCount
            } catch (e: Exception) {
                logger.error("批量传递失败: {}", batch, e)
                errors.add("批次失败: ${batch.size} 个资源")
            }
        }
        
        return PropagationResult(
            affectedResourceCount = totalAffected,
            executionTimeMs = 0, // 实际应该记录
            errors = errors
        )
    }
    
    /**
     * 延迟权限传递
     */
    @Async("permissionPropagationExecutor")
    fun deferredPropagate(context: PropagationContext): CompletableFuture<PropagationResult> {
        return CompletableFuture.supplyAsync {
            logger.debug("执行延迟权限传递: {}", context.sourceResourceId)
            batchPropagate(context)
        }
    }
    
    /**
     * 懒加载权限传递
     */
    private fun lazyPropagate(context: PropagationContext): PropagationResult {
        logger.debug("标记懒加载权限传递: {}", context.sourceResourceId)
        
        // 标记需要传递的资源，在实际访问时再计算权限
        markForLazyPropagation(context)
        
        return PropagationResult(
            affectedResourceCount = 0,
            executionTimeMs = 0,
            errors = emptyList(),
            lazy = true
        )
    }
    
    /**
     * 查找受影响的资源
     */
    private fun findAffectedResources(context: PropagationContext): List<String> {
        val cacheKey = "affected_resources:${context.sourceResourceId}"
        
        // 尝试从缓存获取
        val cached = redisTemplate.opsForValue().get(cacheKey) as? List<String>
        if (cached != null) {
            return cached
        }
        
        // 计算受影响的资源
        val affected = mutableSetOf<String>()
        
        // 向下传递：获取所有子节点
        val descendants = getAllDescendants(context.sourceResourceId, context.maxDepth)
        affected.addAll(descendants)
        
        // 向上传递：获取所有父节点
        val ancestors = getAllAncestors(context.sourceResourceId, context.maxDepth)
        affected.addAll(ancestors)
        
        val result = affected.toList()
        
        // 缓存结果
        redisTemplate.opsForValue().set(cacheKey, result, Duration.ofMinutes(30))
        
        return result
    }
    
    /**
     * 获取所有后代节点
     */
    private fun getAllDescendants(resourceId: String, maxDepth: Int): List<String> {
        if (maxDepth <= 0) return emptyList()
        
        return kSqlClient.createQuery(ACL::class) {
            where(table.parent.resourceId eq resourceId)
            select(table.resourceId)
        }.execute().flatMap { childId ->
            listOf(childId) + getAllDescendants(childId, maxDepth - 1)
        }
    }
    
    /**
     * 获取所有祖先节点
     */
    private fun getAllAncestors(resourceId: String, maxDepth: Int): List<String> {
        if (maxDepth <= 0) return emptyList()
        
        val parent = kSqlClient.createQuery(ACL::class) {
            where(table.resourceId eq resourceId)
            select(table.parent.resourceId)
        }.fetchOneOrNull()
        
        return if (parent != null) {
            listOf(parent) + getAllAncestors(parent, maxDepth - 1)
        } else {
            emptyList()
        }
    }
    
    /**
     * 创建权限传递计划
     */
    private fun createPropagationPlan(
        context: PropagationContext, 
        affectedResources: List<String>
    ): PropagationPlan {
        
        val operations = mutableListOf<PropagationOperation>()
        
        context.changedPolicies.forEach { policy ->
            affectedResources.forEach { resourceId ->
                val operation = when {
                    isDescendant(resourceId, context.sourceResourceId) -> {
                        PropagationOperation(
                            targetResourceId = resourceId,
                            policy = policy.copy { strategyType = StrategyType.DRILL_DOWN },
                            operationType = OperationType.ADD
                        )
                    }
                    isAncestor(resourceId, context.sourceResourceId) -> {
                        PropagationOperation(
                            targetResourceId = resourceId,
                            policy = policy.copy { strategyType = StrategyType.DRILL_UP },
                            operationType = OperationType.ADD
                        )
                    }
                    else -> null
                }
                
                operation?.let { operations.add(it) }
            }
        }
        
        return PropagationPlan(
            sourceResourceId = context.sourceResourceId,
            operations = operations,
            batchSize = context.batchSize
        )
    }
    
    /**
     * 执行权限传递计划
     */
    private fun executePropagationPlan(plan: PropagationPlan): PropagationResult {
        val startTime = System.currentTimeMillis()
        var affectedCount = 0
        val errors = mutableListOf<String>()
        
        plan.operations.chunked(plan.batchSize).forEach { batch ->
            try {
                executeBatch(batch)
                affectedCount += batch.size
            } catch (e: Exception) {
                logger.error("执行传递批次失败", e)
                errors.add("批次执行失败: ${e.message}")
            }
        }
        
        val executionTime = System.currentTimeMillis() - startTime
        
        return PropagationResult(
            affectedResourceCount = affectedCount,
            executionTimeMs = executionTime,
            errors = errors
        )
    }
    
    /**
     * 执行单个批次
     */
    private fun executeBatch(operations: List<PropagationOperation>) {
        val policiesToAdd = operations.filter { it.operationType == OperationType.ADD }
            .map { it.policy }
        
        if (policiesToAdd.isNotEmpty()) {
            kSqlClient.entities.saveEntities(policiesToAdd)
        }
    }
    
    // 辅助方法
    private fun estimateAffectedResourceCount(resourceId: String): Int {
        // 简化实现，实际应该更精确
        return 100
    }
    
    private fun getCurrentSystemLoad(): Double {
        // 简化实现，实际应该获取真实的系统负载
        return 0.5
    }
    
    private fun markForLazyPropagation(context: PropagationContext) {
        val key = "lazy_propagation:${context.sourceResourceId}"
        redisTemplate.opsForValue().set(key, context, Duration.ofHours(24))
    }
    
    private fun isDescendant(resourceId: String, ancestorId: String): Boolean {
        // 简化实现
        return getAllAncestors(resourceId, 10).contains(ancestorId)
    }
    
    private fun isAncestor(resourceId: String, descendantId: String): Boolean {
        // 简化实现
        return getAllDescendants(resourceId, 10).contains(descendantId)
    }
}

/**
 * 权限传递结果
 */
data class PropagationResult(
    val affectedResourceCount: Int,
    val executionTimeMs: Long,
    val errors: List<String>,
    val lazy: Boolean = false
)

/**
 * 权限传递计划
 */
data class PropagationPlan(
    val sourceResourceId: String,
    val operations: List<PropagationOperation>,
    val batchSize: Int
)

/**
 * 权限传递操作
 */
data class PropagationOperation(
    val targetResourceId: String,
    val policy: Policy,
    val operationType: OperationType
)

/**
 * 操作类型
 */
enum class OperationType {
    ADD, REMOVE, MODIFY
}
