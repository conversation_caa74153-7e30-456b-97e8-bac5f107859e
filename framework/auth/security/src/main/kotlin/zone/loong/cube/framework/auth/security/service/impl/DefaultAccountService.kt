package zone.loong.cube.framework.auth.security.service.impl

import zone.loong.cube.framework.auth.security.model.Account
import zone.loong.cube.framework.auth.security.model.BanLevel
import zone.loong.cube.framework.auth.security.service.AccountService
import zone.loong.cube.framework.kernel.log.annotation.Log

@Log
class DefaultAccountService : AccountService {
    override fun fetchAccount(accountIdentity: String): Account {
        TODO("Not yet implemented")
    }

    override fun fetchAccountByToken(tokenString: String): Account {
        TODO("Not yet implemented")
    }

    override fun fetchAccountGroups(accountIdentity: String): List<String> {
        TODO("Not yet implemented")
    }

    override fun fetchAccountRoles(accountIdentity: String): List<String> {
        TODO("Not yet implemented")
    }

    override fun banAccount(
        accountIdentity: String,
        banLevel: BanLevel,
        banReason: String
    ) {
        TODO("Not yet implemented")
    }

    override fun unbanAccount(accountIdentity: String) {
        TODO("Not yet implemented")
    }
}