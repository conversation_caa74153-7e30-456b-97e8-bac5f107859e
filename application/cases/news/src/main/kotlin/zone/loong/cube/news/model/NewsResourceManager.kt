package zone.loong.cube.news.model

import com.fasterxml.jackson.annotation.JsonValue
import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail
import java.time.LocalDateTime

/**
 * 新闻资源管理
 */
@Entity
interface NewsResourceManager : AuditAware {

    /**
     * 标题
     */
    val title: String

    /**
     * 标签
     */
    @Serialized
    val tags: List<String>?

    /**
     * 作者
     */
    val author: String?

    /**
     * 所属部门
     */
    val issueDept: String?

    /**
     * 发布时间
     */
    val issueTime: LocalDateTime?

    /**
     * 发布人
     */
    val issuer: String?

    /**
     * 发布单位
     */
    val issueUnit: String?

    /**
     * 封面列表
     */
    val images: String?

    /**
     * 内容简要
     */
    val contentBrief: String?

    /**
     * 内容类型(文件/富文本)
     */
    val contentType: ContentType

    /**
     * 内容
     */
    val content: String?

    /**
     * 内容
     */
    @OneToOne
    @JoinColumn(name = "content_file_id")
    val contentFile: FileDetail?

    /**
     * 阅读量
     */
    val readingNum: Int?

    /**
     * 新闻类型
     */
    val newsType: NewsType

    /**
     * 状态
     */
    val status: String?

    /**
     * 状态
     */
    val topStatus: String?

    /**
     * OA新闻id
     */
    val fdId: String?

    /**
     * OA外链地址
     */
    val fdLinkUrl: String?

    //附件
    @OneToMany(mappedBy = "news" )
    val attachments: List<NewsResourceManagerAttachment>

    //版面
    @ManyToMany(mappedBy = "newsResourceManager" )
    val newspaperBoards: List<NewsNewspaperBoard>

    @OneToMany(mappedBy = "news")
    val newsArticles: List<NewsResourceManagerArticle>

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int
}

enum class ContentType(@JsonValue val value: String) {
    FILE("FILE"),
    CONTENT("CONTENT");
}

enum class NewsType(@JsonValue val value: String) {
    PLATFORM("PLATFORM"),
    OA("OA");
}

