package zone.loong.cube.news.controller

import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsResourceManagerArticle
import zone.loong.cube.news.model.dto.NewsArticleSpecification
import zone.loong.cube.news.model.dto.SaveNewsArticle
import zone.loong.cube.news.model.dto.SaveNewsArticles
import zone.loong.cube.news.service.NewsResourceManagerArticleService

@RestController
@RequestMapping("/news/article")
class NewsResourceManagerArticleController(
    private val newsResourceManagerArticleService: NewsResourceManagerArticleService
) {

    @PostMapping("/page")
    fun fetch(@RequestBody pageQuery: PageQuery<NewsArticleSpecification>): Page<NewsResourceManagerArticle> {
        return newsResourceManagerArticleService.fetchArticles(pageQuery)
    }

    @PostMapping("/save")
    fun save(@RequestBody saveNewsArticle: SaveNewsArticle): NewsResourceManagerArticle {
        return newsResourceManagerArticleService.save(saveNewsArticle)
    }

    @PostMapping("/saveArticles")
    fun saveArticles(@RequestBody saveNewsArticles: SaveNewsArticles) {
        return newsResourceManagerArticleService.saveArticles(saveNewsArticles)
    }

    @DeleteMapping("/delete/{id}")
    fun delete(@PathVariable("id") id: String) {
        newsResourceManagerArticleService.delete(id)
    }

    @GetMapping("/detail/{id}")
    fun detail(@PathVariable("id") id: String): NewsResourceManagerArticle {
        return newsResourceManagerArticleService.detail(id)
    }

}