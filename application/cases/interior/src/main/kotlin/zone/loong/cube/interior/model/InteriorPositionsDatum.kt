package zone.loong.cube.interior.model

import org.babyfish.jimmer.sql.*
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import zone.loong.cube.framework.storage.kernel.model.FileDetail

/**
 * 岗位标准数据管理
 */
@Entity
interface InteriorPositionsDatum : AuditAware {

    /**
     * 名称
     */
    val title: String?

    /**
     * 标签
     */
    @Serialized
    val tags: List<String>?

    /**
     * 类型(文件/文件夹)
     */

    val type: FileType
    /**
     * 岗位标准id
     */
    @ManyToOne
    val positions: InteriorPositions?

    /**
     * 父级id
     */
    @ManyToOne
    @OnDissociate(DissociateAction.SET_NULL)
    val parent: InteriorPositionsDatum?

    /**
     * 备注
     */
    val remark: String?

    /**
     * 状态
     */
    val status: String?

    /**
     * 删除标识
     */
    @Default("0")
    @LogicalDeleted("1")
    val deleted: Int

    /**
     * 子节点
     */
    @OneToMany(mappedBy = "parent")
    val children: List<InteriorPositionsDatum>

    @OneToOne
    @JoinColumn(name = "FILE_ID")
    val file: FileDetail?
}

