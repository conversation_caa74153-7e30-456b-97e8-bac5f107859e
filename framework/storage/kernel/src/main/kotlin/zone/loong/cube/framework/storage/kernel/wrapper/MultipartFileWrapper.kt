package zone.loong.cube.framework.storage.kernel.wrapper

import cn.hutool.core.io.FileUtil
import cn.hutool.core.io.IoUtil
import org.dromara.x.file.storage.core.exception.FileStorageRuntimeException
import org.dromara.x.file.storage.core.file.FileWrapper
import org.springframework.web.multipart.MultipartFile
import java.io.BufferedInputStream
import java.io.File
import java.io.IOException
import java.io.InputStream

class MultipartFileWrapper(
    private val file: MultipartFile,
    private var name: String?,
    private var contentType: String?,
    private var size: Long?
) : FileWrapper {
    private var inputStream: InputStream? = null
    override fun getName(): String? {
        return name
    }

    override fun setName(name: String) {
        this.name = name
    }

    override fun getContentType(): String? {
        return contentType
    }

    override fun setContentType(contentType: String) {
        this.contentType = contentType
    }

    @Throws(IOException::class)
    override fun getInputStream(): InputStream {
        if (inputStream == null) {
            inputStream = BufferedInputStream(file.inputStream)
        }
        return inputStream as InputStream
    }

    override fun getSize(): Long? {
        return size
    }

    override fun setSize(size: Long) {
        this.size = size
    }


    override fun transferTo(dest: File) {
        try {
            file.transferTo(dest)
            IoUtil.close(inputStream)
        } catch (ignored: Exception) {
            try {
                FileUtil.writeFromStream(getInputStream(), dest)
            } catch (e: Exception) {
                throw FileStorageRuntimeException("文件移动失败", e)
            }
        }
    }

    override fun supportTransfer(): Boolean {
        return true
    }
}
