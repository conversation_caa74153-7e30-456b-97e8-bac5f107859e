package zone.loong.cube.platform.auth.model

import org.babyfish.jimmer.sql.Entity
import org.babyfish.jimmer.sql.ManyToOne
import org.babyfish.jimmer.sql.Table
import zone.loong.cube.framework.auth.security.model.AccessRecord
import zone.loong.cube.framework.auth.security.model.AccessType
import zone.loong.cube.framework.data.jimmer.model.AuditAware
import java.time.LocalDateTime

@Entity
@Table(name = "AUTH_ACCESS_RECORD")
interface AccessRecord : AccessRecord, AuditAware {

    @ManyToOne
    val account: Account
    override val type: AccessType
    override val time: LocalDateTime
    override val ip: String
    override val device: String
    override val address: String
    override val identity: String
}