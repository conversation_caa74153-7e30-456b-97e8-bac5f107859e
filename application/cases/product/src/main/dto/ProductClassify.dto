export zone.loong.cube.cases.product.model.ProductClassify
    -> package zone.loong.cube.cases.product.model.dto

import zone.loong.cube.framework.data.kernel.model.PolicyType

specification ClassifySpecification {
    associatedIdEq(parent)

    code

    like(name)
}

input SaveClassify {
    #allScalars(this)

    id?

    id(parent)
}

input InitClassify {
    #allScalars(this)
    id?
    children*

    id(parent)
}