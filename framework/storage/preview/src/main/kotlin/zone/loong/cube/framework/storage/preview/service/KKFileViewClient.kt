package zone.loong.cube.framework.storage.preview.service

import com.dtflys.forest.annotation.BaseRequest
import com.dtflys.forest.annotation.GetRequest
import com.dtflys.forest.annotation.Query
import org.springframework.stereotype.Service

@BaseRequest(
    baseURL = "#{cube.storage.preview.kk-file-view-url}",     // 默认域名
)
@Service
interface KKFileViewClient{
    @GetRequest("/onlinePreview")
    fun preview(@Query("url") decodeUrl: String): String
    @GetRequest("/addTask")
    fun addTask(@Query("url") decodeUrl: String): String
}