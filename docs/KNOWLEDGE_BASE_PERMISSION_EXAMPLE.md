# 知识库权限传递详细案例

## 场景设置

### 数据结构
```
知识库结构：
分类A (技术文档) - resourceId: "category_tech_docs"
├── 案例1 (Spring Boot教程) - resourceId: "case_spring_boot"  
├── 案例2 (数据库设计) - resourceId: "case_database_design"
└── 案例3 (微服务架构) - resourceId: "case_microservice"
```

### ACL表数据
```sql
-- ACL记录
INSERT INTO access_acl (id, resource_id, resource_type, resource_name, parent_resource_id) VALUES
('acl_category', 'category_tech_docs', 'KNOWLEDGE_CATEGORY', '技术文档', NULL),
('acl_case1', 'case_spring_boot', 'KNOWLEDGE_CASE', 'Spring Boot教程', 'category_tech_docs'),
('acl_case2', 'case_database_design', 'KNOWLEDGE_CASE', '数据库设计', 'category_tech_docs'),
('acl_case3', 'case_microservice', 'KNOWLEDGE_CASE', '微服务架构', 'category_tech_docs');
```

## 场景1：给分类授权（向下传递）

### 业务需求
管理员给用户 "user123" 授予分类A的 "READ" 权限，期望用户能够：
- 查看分类A
- 查看分类A下的所有案例（案例1、案例2、案例3）

### 权限授权过程

#### 1. 直接授权
```sql
-- 给分类A授予READ权限
INSERT INTO access_policies (id, acl_id, policy_type, value, strategy_type, rule_id) VALUES
('policy_1', 'acl_category', 'USER', 'user123', 'ROOT', 'READ');
```

#### 2. 向下权限传递
系统自动创建传递权限：
```sql
-- 向案例1传递权限
INSERT INTO access_policies (id, acl_id, policy_type, value, strategy_type, rule_id, ref_policy_id) VALUES
('policy_1_drill_down_1', 'acl_case1', 'USER', 'user123', 'DRILL_DOWN', 'READ', 'policy_1');

-- 向案例2传递权限  
INSERT INTO access_policies (id, acl_id, policy_type, value, strategy_type, rule_id, ref_policy_id) VALUES
('policy_1_drill_down_2', 'acl_case2', 'USER', 'user123', 'DRILL_DOWN', 'READ', 'policy_1');

-- 向案例3传递权限
INSERT INTO access_policies (id, acl_id, policy_type, value, strategy_type, rule_id, ref_policy_id) VALUES
('policy_1_drill_down_3', 'acl_case3', 'USER', 'user123', 'DRILL_DOWN', 'READ', 'policy_1');
```

#### 3. 权限检查结果
```kotlin
// 用户可以访问分类
hasPermission("user123", "category_tech_docs", "READ") // true (ROOT权限)

// 用户可以访问所有案例
hasPermission("user123", "case_spring_boot", "READ") // true (DRILL_DOWN权限)
hasPermission("user123", "case_database_design", "READ") // true (DRILL_DOWN权限)  
hasPermission("user123", "case_microservice", "READ") // true (DRILL_DOWN权限)
```

## 场景2：给案例授权（向上传递）

### 业务需求
管理员只给用户 "user456" 授予案例1的 "READ" 权限，期望用户能够：
- 查看案例1（有完整读取权限）
- 看到分类A（但只有可见权限，不能操作分类内容）
- 看不到案例2和案例3

### 权限授权过程

#### 1. 直接授权
```sql
-- 只给案例1授予READ权限
INSERT INTO access_policies (id, acl_id, policy_type, value, strategy_type, rule_id) VALUES
('policy_2', 'acl_case1', 'USER', 'user456', 'ROOT', 'READ');
```

#### 2. 向上权限传递（关键！）
系统自动创建向上传递权限：
```sql
-- 向分类A传递可见权限（注意：这里是VISIBLE权限，不是READ权限）
INSERT INTO access_policies (id, acl_id, policy_type, value, strategy_type, rule_id, ref_policy_id) VALUES
('policy_2_drill_up_1', 'acl_category', 'USER', 'user456', 'DRILL_UP', 'VISIBLE', 'policy_2');
```

#### 3. 权限检查结果
```kotlin
// 用户可以看到分类（但只有可见权限）
hasPermission("user456", "category_tech_docs", "VISIBLE") // true (DRILL_UP权限)
hasPermission("user456", "category_tech_docs", "READ") // false (没有READ权限)

// 用户可以完整访问案例1
hasPermission("user456", "case_spring_boot", "READ") // true (ROOT权限)

// 用户看不到其他案例
hasPermission("user456", "case_database_design", "READ") // false
hasPermission("user456", "case_microservice", "READ") // false
```

## 核心实现逻辑

### 权限传递的核心规则

#### 向下传递（DRILL_DOWN）
```kotlin
fun propagateDown(sourcePolicy: Policy) {
    val children = getChildResources(sourcePolicy.acl.resourceId)
    
    children.forEach { childResourceId ->
        val childPolicy = Policy {
            acl { resourceId = childResourceId }
            policyType = sourcePolicy.policyType
            value = sourcePolicy.value
            strategyType = StrategyType.DRILL_DOWN
            ruleId = sourcePolicy.ruleId  // 保持相同的权限类型
            refPolicy = sourcePolicy      // 引用原始权限
        }
        
        savePolicy(childPolicy)
    }
}
```

#### 向上传递（DRILL_UP）
```kotlin
fun propagateUp(sourcePolicy: Policy) {
    val parents = getParentResources(sourcePolicy.acl.resourceId)
    
    parents.forEach { parentResourceId ->
        val parentPolicy = Policy {
            acl { resourceId = parentResourceId }
            policyType = sourcePolicy.policyType
            value = sourcePolicy.value
            strategyType = StrategyType.DRILL_UP
            ruleId = "VISIBLE"  // 关键：向上只传递可见权限
            refPolicy = sourcePolicy
        }
        
        savePolicy(parentPolicy)
    }
}
```

### 权限检查逻辑

```kotlin
fun hasPermission(userId: String, resourceId: String, requiredPermission: String): Boolean {
    val policies = kSqlClient.createQuery(Policy::class) {
        where(table.acl.resourceId eq resourceId)
        where(table.policyType eq PolicyType.USER)
        where(table.value eq userId)
        where(table.ruleId eq requiredPermission)
        select(table)
    }.execute()
    
    return policies.isNotEmpty()
}
```

## 实际查询示例

### 查询用户在知识库中的所有权限
```sql
-- 查询user123的所有权限
SELECT 
    a.resource_id,
    a.resource_type,
    a.resource_name,
    p.strategy_type,
    p.rule_id as permission,
    CASE 
        WHEN p.strategy_type = 'ROOT' THEN '直接授权'
        WHEN p.strategy_type = 'DRILL_DOWN' THEN '从父级继承'
        WHEN p.strategy_type = 'DRILL_UP' THEN '子级传递'
    END as permission_source
FROM access_policies p
JOIN access_acl a ON p.acl_id = a.id
WHERE p.policy_type = 'USER' 
  AND p.value = 'user123'
ORDER BY a.resource_type, a.resource_id;
```

### 查询结果示例
```
resource_id          | resource_type      | permission | permission_source
--------------------|-------------------|------------|------------------
category_tech_docs  | KNOWLEDGE_CATEGORY| READ       | 直接授权
case_spring_boot    | KNOWLEDGE_CASE    | READ       | 从父级继承  
case_database_design| KNOWLEDGE_CASE    | READ       | 从父级继承
case_microservice   | KNOWLEDGE_CASE    | READ       | 从父级继承
```

## 权限传递的时机

### 在ACL拦截器中触发
```kotlin
@Component
class KnowledgeBaseACLInterceptor : DraftInterceptor<ACLAware, ACLAwareDraft> {
    
    override fun beforeSave(draft: ACLAwareDraft, original: ACLAware?) {
        // 新建资源时初始化ACL
        if (original == null) {
            val aclId = UlidCreator.getUlid().toString()
            draft.aclId = aclId
            
            // 发布资源创建事件，触发权限传递
            TransactionSync.runBeforeCommit("知识库权限传递") {
                applicationEventPublisher.publishEvent(
                    KnowledgeResourceCreated(
                        resourceId = draft.id,
                        resourceType = draft.resourceType(),
                        parentResourceId = draft.parentResourceId()
                    )
                )
            }
        }
    }
}
```

### 在事件监听器中执行传递
```kotlin
@Component
class KnowledgeBasePermissionListener {
    
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onResourceCreated(event: KnowledgeResourceCreated) {
        // 如果有父级，继承父级权限
        event.parentResourceId?.let { parentId ->
            inheritParentPermissions(event.resourceId, parentId)
        }
    }
    
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)  
    fun onPermissionGranted(event: PermissionGrantedEvent) {
        // 执行权限传递
        propagatePermissions(event.resourceId, event.grantedPolicies)
    }
}
```

这样的设计确保了：
1. **向下传递**：分类权限自动传递到所有案例
2. **向上传递**：案例权限向上传递可见性到分类
3. **权限隔离**：只授权特定案例时，其他案例不可见
4. **导航支持**：用户能看到完整的分类路径

你觉得这个解释清楚了吗？有什么地方需要进一步澄清的？
