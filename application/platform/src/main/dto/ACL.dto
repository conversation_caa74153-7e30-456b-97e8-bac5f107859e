export zone.loong.cube.framework.data.jimmer.model.ACL
-> package zone.loong.cube.platform.auth.model.dto

specification ACLSpecification{
    resourceId
    resourceType
    resourceName
    flat(policies) {
        policyType
        value
    }
}

ACLView{
    #allScalars
    policies{
        #allScalars
    }
}

input ACLSave{
    resourceId
    policies{
        #allScalars(this)
        id(acl)
        id(refPolicy)
        id
    }
}

