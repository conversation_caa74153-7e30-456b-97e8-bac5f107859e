package zone.loong.cube.framework.data.jimmer.model

import org.babyfish.jimmer.sql.MappedSuperclass
import zone.loong.cube.framework.data.kernel.model.Audit
import java.time.LocalDateTime

@MappedSuperclass
interface AuditAware : Audit, IDAware {

    override val createdTime: LocalDateTime

    override val createdBy: String

    override val modifiedTime: LocalDateTime

    override val modifiedBy: String

}