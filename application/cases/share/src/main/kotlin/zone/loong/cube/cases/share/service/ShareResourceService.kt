package zone.loong.cube.cases.share.service


import zone.loong.cube.cases.share.model.ShareResource
import zone.loong.cube.cases.share.model.dto.FileSave
import zone.loong.cube.cases.share.model.dto.FolderSave
import zone.loong.cube.cases.share.model.dto.ResourceRename
import zone.loong.cube.cases.share.model.elasticsearch.ESShareResource
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.kernel.model.PageQuery

interface ShareResourceService {
    fun fetchMixedResource(pageQuery: PageQuery<ESSpecification>): ESPage<ESShareResource>
    fun fetchResource(id: String):ShareResource
    fun saveFolder(folder: FolderSave)
    fun savefile(file: FileSave)
    fun rename(resourceRename: ResourceRename)
    fun delete(id: String)
    fun sync()
}