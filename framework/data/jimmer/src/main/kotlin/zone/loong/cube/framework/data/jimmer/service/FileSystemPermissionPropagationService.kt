package zone.loong.cube.framework.data.jimmer.service

import org.babyfish.jimmer.sql.kt.KSqlClient
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.framework.data.jimmer.model.*
import zone.loong.cube.framework.data.kernel.model.StrategyType
import zone.loong.cube.framework.data.kernel.model.PolicyType
import org.slf4j.LoggerFactory

/**
 * 知识库权限传递服务
 * 基于你现有的ACL设计，实现知识库分类和案例之间的权限传递：
 *
 * 场景1：给分类授权 → 向下传递到所有案例（完整权限）
 * 场景2：给案例授权 → 向上传递到分类（可见权限）
 *
 * 数据结构：
 * - 分类 (Category): resourceType = "KNOWLEDGE_CATEGORY"
 * - 案例 (Case): resourceType = "KNOWLEDGE_CASE"
 */
@Service
class KnowledgeBasePermissionPropagationService(
    private val kSqlClient: KSqlClient,
    private val performanceMonitor: ACLPerformanceMonitor
) {

    private val logger = LoggerFactory.getLogger(KnowledgeBasePermissionPropagationService::class.java)
    
    /**
     * 文件系统权限类型
     */
    enum class FileSystemPermission(val code: String, val level: Int) {
        // 基础权限（按权限级别排序）
        VISIBLE("VISIBLE", 1),           // 可见（只能看到存在）
        READABLE("READABLE", 2),         // 可读（可以查看内容）
        WRITABLE("WRITABLE", 3),         // 可写（可以修改内容）
        EXECUTABLE("EXECUTABLE", 4),     // 可执行（可以执行文件）
        DELETABLE("DELETABLE", 5),       // 可删除
        MANAGEABLE("MANAGEABLE", 6);     // 可管理（完整权限）
        
        fun includes(other: FileSystemPermission): Boolean {
            return this.level >= other.level
        }
    }
    
    /**
     * 资源类型
     */
    enum class ResourceType {
        FOLDER,     // 文件夹
        FILE        // 文件
    }
    
    /**
     * 权限传递上下文
     */
    data class FileSystemPropagationContext(
        val sourceResourceId: String,
        val sourceResourceType: ResourceType,
        val grantedPermissions: Set<FileSystemPermission>,
        val userId: String,
        val propagationDirection: PropagationDirection
    )
    
    enum class PropagationDirection {
        DOWN,   // 向下传递
        UP      // 向上传递
    }
    
    /**
     * 执行文件系统权限传递
     */
    @Transactional
    fun propagateFileSystemPermissions(context: FileSystemPropagationContext): FileSystemPropagationResult {
        val monitorContext = performanceMonitor.startOperation("fileSystemPropagation", context.sourceResourceId)
        
        return try {
            when (context.propagationDirection) {
                PropagationDirection.DOWN -> propagateDown(context)
                PropagationDirection.UP -> propagateUp(context)
            }
        } finally {
            performanceMonitor.endOperation(monitorContext)
        }
    }
    
    /**
     * 向下权限传递：给予完整访问权限
     */
    private fun propagateDown(context: FileSystemPropagationContext): FileSystemPropagationResult {
        logger.debug("开始向下权限传递: {}", context.sourceResourceId)
        
        val descendants = getAllDescendants(context.sourceResourceId)
        val propagatedPermissions = mutableMapOf<String, Set<FileSystemPermission>>()
        
        descendants.forEach { descendant ->
            val resourceType = getResourceType(descendant.resourceId)
            val inheritedPermissions = calculateInheritedPermissions(
                context.grantedPermissions, 
                resourceType,
                PropagationDirection.DOWN
            )
            
            if (inheritedPermissions.isNotEmpty()) {
                applyPermissionsToResource(descendant.resourceId, inheritedPermissions, context.userId)
                propagatedPermissions[descendant.resourceId] = inheritedPermissions
            }
        }
        
        return FileSystemPropagationResult(
            success = true,
            propagatedPermissions = propagatedPermissions,
            direction = PropagationDirection.DOWN
        )
    }
    
    /**
     * 向上权限传递：只给予路径可见权限
     */
    private fun propagateUp(context: FileSystemPropagationContext): FileSystemPropagationResult {
        logger.debug("开始向上权限传递: {}", context.sourceResourceId)
        
        val ancestors = getAllAncestors(context.sourceResourceId)
        val propagatedPermissions = mutableMapOf<String, Set<FileSystemPermission>>()
        
        ancestors.forEach { ancestor ->
            // 向上传递时，只给予VISIBLE权限，确保用户能看到路径
            val pathVisibilityPermissions = setOf(FileSystemPermission.VISIBLE)
            
            // 检查是否已有更高权限，避免降级
            val existingPermissions = getExistingPermissions(ancestor.resourceId, context.userId)
            val finalPermissions = mergePermissions(existingPermissions, pathVisibilityPermissions)
            
            if (finalPermissions != existingPermissions) {
                applyPermissionsToResource(ancestor.resourceId, finalPermissions, context.userId)
                propagatedPermissions[ancestor.resourceId] = finalPermissions
            }
        }
        
        return FileSystemPropagationResult(
            success = true,
            propagatedPermissions = propagatedPermissions,
            direction = PropagationDirection.UP
        )
    }
    
    /**
     * 计算继承的权限
     */
    private fun calculateInheritedPermissions(
        sourcePermissions: Set<FileSystemPermission>,
        targetResourceType: ResourceType,
        direction: PropagationDirection
    ): Set<FileSystemPermission> {
        
        return when (direction) {
            PropagationDirection.DOWN -> {
                // 向下传递：完整权限继承
                when (targetResourceType) {
                    ResourceType.FOLDER -> sourcePermissions // 文件夹继承所有权限
                    ResourceType.FILE -> sourcePermissions.filter { 
                        // 文件不需要某些文件夹特有的权限
                        it != FileSystemPermission.MANAGEABLE || sourcePermissions.contains(FileSystemPermission.MANAGEABLE)
                    }.toSet()
                }
            }
            PropagationDirection.UP -> {
                // 向上传递：只给可见权限
                setOf(FileSystemPermission.VISIBLE)
            }
        }
    }
    
    /**
     * 合并权限（取最高权限）
     */
    private fun mergePermissions(
        existing: Set<FileSystemPermission>,
        new: Set<FileSystemPermission>
    ): Set<FileSystemPermission> {
        val allPermissions = existing + new
        
        // 找出最高级别的权限
        val maxLevel = allPermissions.maxOfOrNull { it.level } ?: 0
        val highestPermission = allPermissions.find { it.level == maxLevel }
        
        // 返回包含最高权限及其包含的所有低级权限
        return if (highestPermission != null) {
            FileSystemPermission.values().filter { 
                highestPermission.includes(it) 
            }.toSet()
        } else {
            emptySet()
        }
    }
    
    /**
     * 应用权限到资源
     */
    private fun applyPermissionsToResource(
        resourceId: String,
        permissions: Set<FileSystemPermission>,
        userId: String
    ) {
        // 删除现有权限
        removeExistingPermissions(resourceId, userId)
        
        // 应用新权限
        permissions.forEach { permission ->
            val policy = Policy {
                acl { resourceId = resourceId }
                policyType = PolicyType.USER
                value = userId
                strategyType = when (permission) {
                    FileSystemPermission.VISIBLE -> StrategyType.DRILL_UP
                    else -> StrategyType.DRILL_DOWN
                }
                ruleId = "FS_${permission.code}"
            }
            
            kSqlClient.entities.save(policy)
        }
    }
    
    /**
     * 获取现有权限
     */
    private fun getExistingPermissions(resourceId: String, userId: String): Set<FileSystemPermission> {
        val policies = kSqlClient.createQuery(Policy::class) {
            where(table.acl.resourceId eq resourceId)
            where(table.policyType eq PolicyType.USER)
            where(table.value eq userId)
            where(table.ruleId like "FS_%")
            select(table.ruleId)
        }.execute()
        
        return policies.mapNotNull { ruleId ->
            val permissionCode = ruleId?.removePrefix("FS_")
            FileSystemPermission.values().find { it.code == permissionCode }
        }.toSet()
    }
    
    /**
     * 删除现有权限
     */
    private fun removeExistingPermissions(resourceId: String, userId: String) {
        kSqlClient.createDelete(Policy::class) {
            where(table.acl.resourceId eq resourceId)
            where(table.policyType eq PolicyType.USER)
            where(table.value eq userId)
            where(table.ruleId like "FS_%")
        }.execute()
    }
    
    /**
     * 获取所有后代资源
     */
    private fun getAllDescendants(resourceId: String): List<ResourceNode> {
        return kSqlClient.createQuery(ACL::class) {
            where(table.parent.resourceId eq resourceId)
            select(table.fetchBy {
                resourceId()
                resourceType()
                resourceName()
            })
        }.execute().flatMap { child ->
            val childNode = ResourceNode(child.resourceId, child.resourceType)
            listOf(childNode) + getAllDescendants(child.resourceId)
        }
    }
    
    /**
     * 获取所有祖先资源
     */
    private fun getAllAncestors(resourceId: String): List<ResourceNode> {
        val parent = kSqlClient.createQuery(ACL::class) {
            where(table.resourceId eq resourceId)
            select(table.fetchBy {
                parent {
                    resourceId()
                    resourceType()
                }
            })
        }.fetchOneOrNull()?.parent
        
        return if (parent != null) {
            val parentNode = ResourceNode(parent.resourceId, parent.resourceType)
            listOf(parentNode) + getAllAncestors(parent.resourceId)
        } else {
            emptyList()
        }
    }
    
    /**
     * 获取资源类型
     */
    private fun getResourceType(resourceId: String): ResourceType {
        // 这里应该根据实际的资源类型判断逻辑
        // 简化实现：根据资源ID模式判断
        return if (resourceId.contains("file")) ResourceType.FILE else ResourceType.FOLDER
    }
    
    /**
     * 检查用户对资源的有效权限
     */
    fun getEffectivePermissions(resourceId: String, userId: String): Set<FileSystemPermission> {
        // 获取直接权限
        val directPermissions = getExistingPermissions(resourceId, userId)
        
        // 获取继承权限（从父级）
        val inheritedPermissions = getInheritedPermissions(resourceId, userId)
        
        // 合并权限
        return mergePermissions(directPermissions, inheritedPermissions)
    }
    
    /**
     * 获取继承的权限
     */
    private fun getInheritedPermissions(resourceId: String, userId: String): Set<FileSystemPermission> {
        val ancestors = getAllAncestors(resourceId)
        
        return ancestors.flatMap { ancestor ->
            getExistingPermissions(ancestor.resourceId, userId)
        }.toSet()
    }
}

/**
 * 资源节点
 */
data class ResourceNode(
    val resourceId: String,
    val resourceType: String
)

/**
 * 文件系统权限传递结果
 */
data class FileSystemPropagationResult(
    val success: Boolean,
    val propagatedPermissions: Map<String, Set<FileSystemPermissionPropagationService.FileSystemPermission>>,
    val direction: FileSystemPermissionPropagationService.PropagationDirection,
    val errors: List<String> = emptyList()
)
