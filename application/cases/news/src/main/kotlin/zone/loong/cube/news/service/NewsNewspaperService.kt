package zone.loong.cube.news.service

import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsBlock
import zone.loong.cube.news.model.NewsNewspaper
import zone.loong.cube.news.model.dto.NewsNewsPaperSpecification
import zone.loong.cube.news.model.dto.SaveNewsNewsPaper

interface NewsNewspaperService {
    fun fetchResourceManagers(pageQuery: PageQuery<NewsNewsPaperSpecification>): Page<NewsNewspaper>
    fun fetchResourceManagers(): List<NewsNewspaper>
    fun save(saveNewsNewspaper: SaveNewsNewsPaper): NewsNewspaper
    fun delete(id: String)
    fun detail(id: String): NewsNewspaper
    fun fetchNewsPaperSelectList(): List<NewsNewspaper>;
}