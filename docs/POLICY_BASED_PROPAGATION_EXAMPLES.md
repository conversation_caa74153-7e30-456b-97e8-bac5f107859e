# 策略驱动权限传递详解

## 业务场景对比

### 传统树形继承的局限性

假设你有这样的组织结构：
```
总公司
├── 华东分公司
│   ├── 上海办事处
│   └── 杭州办事处
└── 华南分公司
    ├── 深圳办事处
    └── 广州办事处
```

**传统树形继承的问题：**
- 总经理给华东分公司的权限，会自动传递给上海、杭州办事处
- 但如果总经理要给"所有办事处"权限，却不给分公司，树形结构就无法表达
- 跨部门项目、临时授权、条件性权限等场景无法处理

### 策略驱动的优势场景

## 具体业务案例

### 案例1：项目驱动的权限传递

**业务背景：**
公司启动了一个跨部门项目"数字化转型"，需要给项目组成员特殊权限，但这些权限：
- 只在项目期间有效
- 只对项目相关资源有效
- 项目结束后自动回收

```kotlin
/**
 * 项目驱动的权限传递策略
 */
class ProjectBasedPropagationPolicy : PermissionPropagationPolicy {
    
    override fun shouldPropagate(context: PropagationContext): Boolean {
        // 检查是否是项目相关的权限变更
        val isProjectPermission = context.changedPolicies.any { 
            it.value.startsWith("PROJECT_") 
        }
        
        if (!isProjectPermission) return false
        
        // 检查项目是否还在进行中
        val project = getProjectByResource(context.sourceResourceId)
        return project?.status == ProjectStatus.ACTIVE && 
               project.endDate.isAfter(LocalDateTime.now())
    }
    
    override fun transformPermissions(
        permissions: Set<String>, 
        context: PropagationContext
    ): Set<String> {
        val project = getProjectByResource(context.sourceResourceId)
        
        return permissions.mapNotNull { permission ->
            when {
                // 项目管理员权限转换为项目成员权限
                permission == "PROJECT_ADMIN" && !isProjectManager(context.userId) -> 
                    "PROJECT_MEMBER"
                
                // 敏感权限只给核心成员
                permission.contains("SENSITIVE") && !isProjectCoreTeam(context.userId) -> 
                    null
                
                // 添加项目前缀，限制权限范围
                else -> "PROJECT_${project?.code}_$permission"
            }
        }.toSet()
    }
    
    override fun findTargets(context: PropagationContext): Set<String> {
        val project = getProjectByResource(context.sourceResourceId)
        
        return when (context.propagationType) {
            PropagationType.PROJECT_TEAM -> {
                // 传递给项目团队成员的资源
                getProjectTeamResources(project?.id)
            }
            PropagationType.PROJECT_STAKEHOLDER -> {
                // 传递给项目干系人的资源
                getProjectStakeholderResources(project?.id)
            }
            else -> emptySet()
        }
    }
}
```

### 案例2：时间敏感的权限传递

**业务背景：**
财务部门的月末结账权限，只在每月最后3天开放，且只在工作时间有效。

```kotlin
/**
 * 时间敏感的权限传递策略
 */
class TimeBasedPropagationPolicy : PermissionPropagationPolicy {
    
    override fun shouldPropagate(context: PropagationContext): Boolean {
        val hasTimeRestrictedPermission = context.changedPolicies.any { 
            it.value in TIME_RESTRICTED_PERMISSIONS 
        }
        
        if (!hasTimeRestrictedPermission) return false
        
        return when {
            // 月末结账权限：只在月末3天
            context.changedPolicies.any { it.value == "MONTH_END_CLOSING" } -> 
                isMonthEndPeriod()
            
            // 工作时间权限：只在9-18点
            context.changedPolicies.any { it.value.contains("WORK_TIME") } -> 
                isWorkingHours()
            
            // 紧急权限：24小时有效
            context.changedPolicies.any { it.value.contains("EMERGENCY") } -> 
                isWithinEmergencyWindow(context.grantTime)
            
            else -> true
        }
    }
    
    override fun transformPermissions(
        permissions: Set<String>, 
        context: PropagationContext
    ): Set<String> {
        val currentTime = LocalDateTime.now()
        
        return permissions.mapNotNull { permission ->
            when {
                // 月末权限：添加时间限制
                permission == "MONTH_END_CLOSING" -> {
                    if (isMonthEndPeriod()) {
                        "MONTH_END_CLOSING_${getMonthEndDeadline()}"
                    } else null
                }
                
                // 工作时间权限：添加时间戳
                permission.contains("WORK_TIME") -> {
                    if (isWorkingHours()) {
                        "${permission}_UNTIL_${getWorkDayEnd()}"
                    } else null
                }
                
                // 紧急权限：添加过期时间
                permission.contains("EMERGENCY") -> {
                    "${permission}_EXPIRES_${currentTime.plusHours(24)}"
                }
                
                else -> permission
            }
        }.toSet()
    }
    
    override fun findTargets(context: PropagationContext): Set<String> {
        return when {
            // 月末结账：传递给所有财务相关资源
            context.changedPolicies.any { it.value == "MONTH_END_CLOSING" } -> 
                getFinancialResources()
            
            // 工作时间权限：传递给当前在线用户的资源
            context.changedPolicies.any { it.value.contains("WORK_TIME") } -> 
                getActiveUserResources()
            
            else -> getStandardTargets(context.sourceResourceId)
        }
    }
    
    private fun isMonthEndPeriod(): Boolean {
        val now = LocalDate.now()
        val lastDayOfMonth = now.withDayOfMonth(now.lengthOfMonth())
        val daysDiff = ChronoUnit.DAYS.between(now, lastDayOfMonth)
        return daysDiff <= 3
    }
    
    private fun isWorkingHours(): Boolean {
        val now = LocalTime.now()
        return now.isAfter(LocalTime.of(9, 0)) && now.isBefore(LocalTime.of(18, 0))
    }
}
```

### 案例3：地理位置敏感的权限传递

**业务背景：**
某些敏感数据只能在公司内网访问，出差员工的权限需要根据地理位置动态调整。

```kotlin
/**
 * 地理位置敏感的权限传递策略
 */
class LocationBasedPropagationPolicy : PermissionPropagationPolicy {
    
    override fun shouldPropagate(context: PropagationContext): Boolean {
        val hasLocationRestrictedPermission = context.changedPolicies.any { 
            it.value in LOCATION_RESTRICTED_PERMISSIONS 
        }
        
        if (!hasLocationRestrictedPermission) return false
        
        val userLocation = getUserCurrentLocation(context.userId)
        val resourceLocation = getResourceLocation(context.sourceResourceId)
        
        return when {
            // 机密数据：必须在公司内部
            context.changedPolicies.any { it.value.contains("CONFIDENTIAL") } -> 
                isInCompanyPremises(userLocation)
            
            // 区域数据：必须在同一区域
            context.changedPolicies.any { it.value.contains("REGIONAL") } -> 
                isSameRegion(userLocation, resourceLocation)
            
            // 国际数据：遵守数据主权法律
            context.changedPolicies.any { it.value.contains("INTERNATIONAL") } -> 
                isDataSovereigntyCompliant(userLocation, resourceLocation)
            
            else -> true
        }
    }
    
    override fun transformPermissions(
        permissions: Set<String>, 
        context: PropagationContext
    ): Set<String> {
        val userLocation = getUserCurrentLocation(context.userId)
        
        return permissions.mapNotNull { permission ->
            when {
                // 机密权限：公司外部降级为只读
                permission.contains("CONFIDENTIAL") && !isInCompanyPremises(userLocation) -> 
                    "${permission}_READ_ONLY"
                
                // 区域权限：添加地理标识
                permission.contains("REGIONAL") -> 
                    "${permission}_${getRegionCode(userLocation)}"
                
                // 国际权限：添加合规标识
                permission.contains("INTERNATIONAL") -> 
                    "${permission}_${getComplianceLevel(userLocation)}"
                
                else -> permission
            }
        }.toSet()
    }
    
    override fun findTargets(context: PropagationContext): Set<String> {
        val userLocation = getUserCurrentLocation(context.userId)
        
        return when {
            // 机密数据：只传递给同一建筑内的资源
            context.changedPolicies.any { it.value.contains("CONFIDENTIAL") } -> 
                getResourcesInBuilding(userLocation.buildingId)
            
            // 区域数据：传递给同一区域的资源
            context.changedPolicies.any { it.value.contains("REGIONAL") } -> 
                getResourcesInRegion(getRegionCode(userLocation))
            
            // 国际数据：传递给合规的资源
            context.changedPolicies.any { it.value.contains("INTERNATIONAL") } -> 
                getCompliantResources(userLocation.countryCode)
            
            else -> getStandardTargets(context.sourceResourceId)
        }
    }
}
```

## 完整的业务流程示例

### 场景：跨国项目的权限管理

**背景：**
- 中美合作的AI项目
- 涉及敏感技术数据
- 需要遵守两国的数据法规
- 项目周期6个月

```kotlin
/**
 * 跨国项目权限传递策略
 */
class InternationalProjectPropagationPolicy : PermissionPropagationPolicy {
    
    override fun shouldPropagate(context: PropagationContext): Boolean {
        val project = getProjectByResource(context.sourceResourceId)
        
        // 检查项目状态
        if (project?.type != ProjectType.INTERNATIONAL || 
            project.status != ProjectStatus.ACTIVE) {
            return false
        }
        
        // 检查用户的合规状态
        val user = getUser(context.userId)
        if (!user.hasComplianceTraining || user.securityClearance < project.requiredClearance) {
            return false
        }
        
        // 检查数据分类
        val dataClassification = getResourceDataClassification(context.sourceResourceId)
        return isTransferAllowed(dataClassification, user.nationality, project.countries)
    }
    
    override fun transformPermissions(
        permissions: Set<String>, 
        context: PropagationContext
    ): Set<String> {
        val user = getUser(context.userId)
        val userLocation = getUserCurrentLocation(context.userId)
        val project = getProjectByResource(context.sourceResourceId)
        
        return permissions.mapNotNull { permission ->
            when {
                // 核心技术权限：只给核心团队
                permission.contains("CORE_TECH") -> {
                    if (user.id in project.coreTeamMembers) {
                        "${permission}_CORE_TEAM_${project.code}"
                    } else null
                }
                
                // 敏感数据权限：添加地理和时间限制
                permission.contains("SENSITIVE") -> {
                    val restrictions = mutableListOf<String>()
                    
                    // 地理限制
                    if (!isInSecureLocation(userLocation)) {
                        restrictions.add("SECURE_LOCATION_REQUIRED")
                    }
                    
                    // 时间限制
                    if (!isWorkingHours(userLocation.timeZone)) {
                        restrictions.add("WORK_HOURS_ONLY")
                    }
                    
                    // 审计要求
                    restrictions.add("AUDIT_REQUIRED")
                    
                    "${permission}_${restrictions.joinToString("_")}"
                }
                
                // 协作权限：根据国籍调整
                permission.contains("COLLABORATE") -> {
                    when (user.nationality) {
                        "CN" -> "${permission}_CN_COMPLIANT"
                        "US" -> "${permission}_US_COMPLIANT"
                        else -> "${permission}_INTERNATIONAL"
                    }
                }
                
                else -> permission
            }
        }.toSet()
    }
    
    override fun findTargets(context: PropagationContext): Set<String> {
        val project = getProjectByResource(context.sourceResourceId)
        val user = getUser(context.userId)
        
        val targets = mutableSetOf<String>()
        
        // 基于项目角色确定目标
        when (user.projectRole) {
            ProjectRole.TECH_LEAD -> {
                // 技术负责人：传递给技术团队资源
                targets.addAll(getProjectTechResources(project.id))
            }
            
            ProjectRole.COMPLIANCE_OFFICER -> {
                // 合规官：传递给合规相关资源
                targets.addAll(getComplianceResources(project.id))
            }
            
            ProjectRole.TEAM_MEMBER -> {
                // 普通成员：传递给个人工作区资源
                targets.addAll(getUserWorkspaceResources(user.id, project.id))
            }
        }
        
        // 基于数据分类过滤目标
        val dataClassification = getResourceDataClassification(context.sourceResourceId)
        return targets.filter { targetResource ->
            isDataTransferAllowed(dataClassification, context.sourceResourceId, targetResource)
        }.toSet()
    }
}
```

## 策略驱动 vs 树形继承对比

| 特性 | 树形继承 | 策略驱动 |
|------|---------|---------|
| **适用场景** | 标准组织架构 | 复杂业务规则 |
| **权限表达** | 静态层级关系 | 动态条件规则 |
| **跨部门协作** | ❌ 难以支持 | ✅ 灵活支持 |
| **临时权限** | ❌ 需要手动管理 | ✅ 自动管理 |
| **条件性权限** | ❌ 不支持 | ✅ 完全支持 |
| **合规要求** | ❌ 需要额外实现 | ✅ 内置支持 |
| **性能** | 高（简单查询） | 中（规则计算） |
| **维护复杂度** | 低 | 中高 |

## 实际应用建议

1. **混合使用**：基础权限用树形继承，特殊场景用策略驱动
2. **渐进实施**：先实现简单策略，逐步增加复杂规则
3. **性能优化**：缓存策略计算结果，避免重复计算
4. **监控审计**：记录所有策略决策过程，便于调试和合规

策略驱动的权限传递让你的系统能够处理现实世界中复杂多变的业务需求，而不仅仅是简单的组织架构关系。
