package zone.loong.cube.framework.data.elasticsearch.service.impl

import co.elastic.clients.elasticsearch._types.FieldValue
import co.elastic.clients.elasticsearch._types.SortOrder
import co.elastic.clients.elasticsearch._types.aggregations.Aggregation
import co.elastic.clients.elasticsearch._types.query_dsl.Query
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.node.ObjectNode
import org.springframework.data.domain.PageRequest
import org.springframework.data.elasticsearch.client.elc.ElasticsearchAggregations
import org.springframework.data.elasticsearch.client.elc.NativeQuery
import org.springframework.data.elasticsearch.core.ElasticsearchOperations
import org.springframework.data.elasticsearch.core.query.FetchSourceFilter
import org.springframework.data.elasticsearch.core.query.HighlightQuery
import org.springframework.data.elasticsearch.core.query.highlight.Highlight
import org.springframework.data.elasticsearch.core.query.highlight.HighlightField
import org.springframework.data.elasticsearch.core.query.highlight.HighlightParameters
import zone.loong.cube.framework.data.elasticsearch.ESBucket
import zone.loong.cube.framework.data.elasticsearch.ESPage
import zone.loong.cube.framework.data.elasticsearch.ESSpecification
import zone.loong.cube.framework.data.elasticsearch.EsAgg
import zone.loong.cube.framework.data.elasticsearch.constants.AggConstants
import zone.loong.cube.framework.data.elasticsearch.constants.HighlightConstants
import zone.loong.cube.framework.data.elasticsearch.constants.PolicyConstants
import zone.loong.cube.framework.data.elasticsearch.service.SearchService
import zone.loong.cube.framework.data.kernel.constants.ACLConstants
import zone.loong.cube.framework.data.kernel.model.ACL
import zone.loong.cube.framework.data.kernel.model.OrderEnum.ASC
import zone.loong.cube.framework.data.kernel.model.OrderEnum.DESC
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.data.kernel.model.PolicyType
import zone.loong.cube.framework.data.kernel.service.AuthProvider
import java.io.StringReader
import kotlin.reflect.KClass

class DefaultSearchServiceImpl(
    private val elasticsearchOperations: ElasticsearchOperations,
    private val objectMapper: ObjectMapper,
    private val authProvider: AuthProvider,
) : SearchService {
    override fun <E : Any> page(pageQuery: PageQuery<ESSpecification>, kClass: KClass<E>,disableFilter: Boolean): ESPage<E> {
        val acl = (ACL::class.java).isAssignableFrom(kClass.java) && !ignoreAcl() && !disableFilter
        elasticsearchOperations.search(buildQuery(pageQuery, acl), kClass.java).apply {
            val data = searchHits.map {
                val content = it.content
                val json = objectMapper.valueToTree<ObjectNode>(content)
                it.highlightFields.forEach { (key, value) ->
                    val field = kClass.java.declaredFields.first { it.name === key }
                    if (List::class.java.isAssignableFrom(field.type)){
                        json.putArray(key).add(value.first())
                    }else{
                        json.put(key, value.first())
                    }
                }
                objectMapper.convertValue(json, kClass.java)
            }
            val aggs = mutableMapOf<String, EsAgg>()
            (aggregations as ElasticsearchAggregations).aggregationsAsMap().forEach { (_, value) ->
                val name = value.aggregation().name
                val buckets = value.aggregation().aggregate.takeIf { !it.isLterms }?.let {
                    it.sterms().buckets().array()
                        .filter { it.key().stringValue().isNotEmpty() }
                }
                buckets?.let {
                    aggs[name] = EsAgg(name, buckets.map {
                        ESBucket(
                            it.key().stringValue(),
                            it.docCount(),
                            it.aggregations()[name + AggConstants.AGG_FIELD_SUFFIX]?.sterms()?.buckets()?.array()
                                ?.firstOrNull()
                                ?.key()?.stringValue() ?: ""
                        )
                    })
                }

            }
            return ESPage(pageQuery.pageSize, pageQuery.page, this.totalHits, data, aggs)
        }
    }

    override fun <E : Any> save(data: E): E {
        return elasticsearchOperations.save(data)
    }

    override fun <E : Any> save(data: List<E>): List<E> {
        return elasticsearchOperations.save(data).toList()
    }

    override fun <E : Any> deleteById(id: String, kClass: KClass<E>) {
        elasticsearchOperations.delete(id, kClass.java)
    }

    private fun buildQuery(pageQuery: PageQuery<ESSpecification>, acl: Boolean): NativeQuery {
        val builder = NativeQuery.builder()
        val aggs = pageQuery.specification.aggs
        val source = pageQuery.specification.source
        val conditions = pageQuery.specification.conditions
        val highlight = pageQuery.specification.highlight
        //构造条件

        builder.withQuery { query ->
            query.bool { bool ->
                conditions.forEach { (_, condition) ->
                    bool.must { must ->
                        must.withJson(StringReader(objectMapper.writeValueAsString(condition)))
                    }
                }
                if (acl) {
                    bool.must { must ->
                        buildAclQuery(must)
                    }
                }
                bool
            }
        }


        //ACL控制


        //聚合
        aggs.forEach { aggField ->
            builder.withAggregation(aggField, Aggregation.of { a ->
                a.terms { terms ->
                    terms.field(aggField)
                    terms.size(10001)
                }.aggregations(aggField + AggConstants.AGG_FIELD_SUFFIX, Aggregation.of { sub ->
                    sub.terms { terms ->
                        terms.field(aggField + AggConstants.AGG_FIELD_SUFFIX)
                        terms.size(1)
                    }
                })

            })
        }
        //高亮
        if (highlight.isNotEmpty()) {
            val highlightFields = highlight.map {
                HighlightField(it)
            }
            val highlightFieldParameters =
                HighlightParameters.builder().withPreTags(HighlightConstants.HIGHLIGHT_PRE_TAG)
                    .withPostTags(HighlightConstants.HIGHLIGHT_POST_TAG).build()

            builder.withHighlightQuery(HighlightQuery(Highlight(highlightFieldParameters, highlightFields), null))
        }
        //优先按照分数排序
        builder.withSort {
            it.field { fieldSortBuilder ->
                fieldSortBuilder.field("_score")
            }
        }
        //排序
        pageQuery.sort.forEach {sort ->
            builder.withSort {
                it.field { fieldSort ->
                    fieldSort.field(sort.field)
                    when (sort.order) {
                        DESC -> fieldSort.order(SortOrder.Desc)
                        ASC -> fieldSort.order(SortOrder.Asc)
                    }
                }
            }
        }


        //字段
        builder.withSourceFilter(
            FetchSourceFilter(
                source.toTypedArray(), emptyList<String>().toTypedArray()
            )
        )
        builder.withPageable(PageRequest.of(pageQuery.page, pageQuery.pageSize))
        return builder.build()
    }


    private fun ignoreAcl(): Boolean {
        val userRoles = authProvider.getUserRoles()
        val userGroups = authProvider.getUserGroups()
        return userGroups.any {
            it.equals(ACLConstants.GROUP_SUPER_ADMIN, true)
        } || userRoles.any {
            it.equals(ACLConstants.ROLE_SUPER_ADMIN, true)
        }
    }

    private fun buildAclQuery(queryBuilder: Query.Builder): Query.Builder {
        val userRoles = authProvider.getUserRoles()
        val userGroups = authProvider.getUserGroups()
        val userIdentity = authProvider.getUserIdentity()
        val userOrganizationIds = authProvider.getUserOrganizationIds()
        queryBuilder.nested {
            it.path(PolicyConstants.POLICY_PATH)
            it.query { query ->
                query.bool { bool ->
                    //userFilter
                    bool.should { should ->
                        should.bool { shouldBool ->
                            shouldBool.must { must ->
                                must.term { term ->
                                    term.field(PolicyConstants.POLICY_TYPE).value(PolicyType.USER.value)
                                }
                            }
                            shouldBool.must { must ->
                                must.term { term ->
                                    term.field(PolicyConstants.POLICY_VALUE).value(userIdentity)
                                }
                            }
                        }
                    }

                    if (userRoles.isNotEmpty()) {
                        bool.should { should ->
                            should.bool { shouldBool ->
                                shouldBool.must { must ->
                                    must.term { term ->
                                        term.field(PolicyConstants.POLICY_TYPE).value(PolicyType.ROLE.value)
                                    }
                                }
                                shouldBool.must { must ->
                                    must.terms { terms ->
                                        terms.field(PolicyConstants.POLICY_VALUE)
                                        terms.terms { value ->
                                            value.value(userRoles.map { roleId ->
                                                FieldValue.of(roleId)
                                            })
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (userOrganizationIds.isNotEmpty()) {
                        bool.should { should ->
                            should.bool { shouldBool ->
                                shouldBool.must { must ->
                                    must.term { term ->
                                        term.field(PolicyConstants.POLICY_TYPE).value(PolicyType.ORGANIZATION.value)
                                    }
                                }
                                shouldBool.must { must ->
                                    must.terms { terms ->
                                        terms.field(PolicyConstants.POLICY_VALUE)
                                        terms.terms { value ->
                                            value.value(userOrganizationIds.map { orgId ->
                                                FieldValue.of(orgId)
                                            })
                                        }
                                    }
                                }
                            }
                        }
                    }

                    if (userGroups.isNotEmpty()) {
                        bool.should { should ->
                            should.bool { shouldBool ->
                                shouldBool.must { must ->
                                    must.term { term ->
                                        term.field(PolicyConstants.POLICY_TYPE).value(PolicyType.GROUP.value)
                                    }
                                }
                                shouldBool.must { must ->
                                    must.terms { terms ->
                                        terms.field(PolicyConstants.POLICY_VALUE)
                                        terms.terms { value ->
                                            value.value(userGroups.map { orgId ->
                                                FieldValue.of(orgId)
                                            })
                                        }
                                    }
                                }
                            }
                        }
                    }
                    bool
                }
            }
        }
        return queryBuilder
    }
}