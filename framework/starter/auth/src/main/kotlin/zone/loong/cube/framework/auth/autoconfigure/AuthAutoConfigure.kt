package zone.loong.cube.framework.auth.autoconfigure

import jakarta.annotation.PostConstruct
import org.springframework.boot.autoconfigure.AutoConfiguration
import org.springframework.context.annotation.Import
import zone.loong.cube.framework.auth.security.configuration.SecurityConfiguration
import zone.loong.cube.framework.kernel.log.annotation.Log
import zone.loong.cube.framework.kernel.log.annotation.Log.Companion.log

@AutoConfiguration
@Import(SecurityConfiguration::class)
@Log
class AuthAutoConfigure {
    @PostConstruct
    fun init() {
        log.info("[CUBE FRAMEWORK] MODULE [AUTH] LOAD [SUCCESS].")
    }
}