package zone.loong.cube.news.service

import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.news.model.NewsResourceManagerArticle
import zone.loong.cube.news.model.dto.NewsArticleSpecification
import zone.loong.cube.news.model.dto.SaveNewsArticle
import zone.loong.cube.news.model.dto.SaveNewsArticles

interface NewsResourceManagerArticleService {
    fun fetchArticles(pageQuery: PageQuery<NewsArticleSpecification>): Page<NewsResourceManagerArticle>
    fun save(saveNewsArticle: SaveNewsArticle): NewsResourceManagerArticle
    fun saveArticles(saveNewsArticles: SaveNewsArticles)
    fun delete(id: String)
    fun detail(id: String): NewsResourceManagerArticle
}