package zone.loong.cube.cases.product.service.impl

import org.babyfish.jimmer.sql.ast.mutation.SaveMode
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.expression.eq
import org.babyfish.jimmer.sql.kt.fetcher.newFetcher
import org.springframework.context.ApplicationContext
import org.springframework.expression.EvaluationException
import org.springframework.expression.Expression
import org.springframework.expression.ExpressionParser
import org.springframework.expression.spel.standard.SpelExpressionParser
import org.springframework.expression.spel.support.StandardEvaluationContext
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.cases.product.model.*
import zone.loong.cube.cases.product.model.dto.AuthorizationRuleSave
import zone.loong.cube.cases.product.model.dto.AuthorizationRuleSpecification
import zone.loong.cube.cases.product.model.dto.AuthorizationRuleView
import zone.loong.cube.cases.product.service.AuthorizationRuleService
import zone.loong.cube.cases.product.service.ESProductCasesService
import zone.loong.cube.framework.data.jimmer.ext.page
import zone.loong.cube.framework.data.jimmer.interceptor.ACLFilter
import zone.loong.cube.framework.data.jimmer.model.ACL
import zone.loong.cube.framework.data.jimmer.model.Policy
import zone.loong.cube.framework.data.jimmer.model.policies
import zone.loong.cube.framework.data.jimmer.model.ruleId
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.data.kernel.model.PolicyType
import zone.loong.cube.framework.data.kernel.model.StrategyType
import zone.loong.cube.framework.kernel.exception.BizException
import zone.loong.cube.platform.auth.model.Group
import zone.loong.cube.platform.auth.model.dto.ACLSave
import zone.loong.cube.platform.dataAuth.service.ACLService
import java.util.stream.Collectors

@Service
class AuthorizationRuleServiceImpl(
    private val kSqlClient: KSqlClient,
    private val aclService: ACLService,
    private val esProductCasesService: ESProductCasesService,
    private val applicationContent: ApplicationContext
) : AuthorizationRuleService {
    override fun fetchRules(pageQuery: PageQuery<AuthorizationRuleSpecification>): Page<AuthorizationRuleView> {
        return kSqlClient.page(pageQuery) {
            select(table.fetch(AuthorizationRuleView::class))
        }
    }

    @Transactional
    override fun save(authorizationRule: AuthorizationRuleSave): AuthorizationRule {
        val rule = authorizationRule.id?.let {
            //修改
            kSqlClient.entities.save(authorizationRule).modifiedEntity
        } ?: kSqlClient.entities.save(authorizationRule, SaveMode.INSERT_ONLY).modifiedEntity
        refreshPolicy(rule.id)
        return rule
    }

    override fun detail(id: String): AuthorizationRuleView {
        return kSqlClient.findById(AuthorizationRuleView::class, id) ?: throw BizException("该规则未找到，或已被删除")
    }

    override fun fetchRuleGroups(id: String): List<Group> {
        return kSqlClient.createQuery(AuthorizationRule::class) {
            select(table.fetchBy {
                groups {
                    code()
                    name()
                }
            })
        }.fetchOneOrNull()?.groups ?: throw BizException("规则不存在")
    }

    @Transactional
    override fun delete(id: String) {
        kSqlClient.deleteById(AuthorizationRule::class, id)

        //查询受到该规则影响的案例
        val effectCaseIds = kSqlClient.createQuery(ProductCase::class) {
            where(table.acl.policies {
                ruleId eq id
            })
            select(table.id)
        }.execute()

        //删除对应规则生成的Policy
        kSqlClient.createDelete(Policy::class) {
            where(table.ruleId eq id)
        }.execute()

        esProductCasesService.updateAcl(effectCaseIds)
    }

    /**
     * 刷新授权
     */
    @Transactional
    fun refreshPolicy(ruleId: String) {
        //查询对应的规则
        val authorizationRule = detail(ruleId)
        val cases = kSqlClient.filters {
            disableByTypes(ACLFilter::class)
        }.entities.findAll(newFetcher(ProductCase::class).by {
            tags()
            acl {
                policies {
                    acl()
                    policyType()
                    strategyType()
                    value()
                    ruleId()
                }
            }
        })

        //查询受到该规则影响的案例
        val effectCaseIds = kSqlClient.createQuery(ProductCase::class) {
            where(table.acl.policies {
                this.ruleId eq ruleId
            })
            select(table.id)
        }.execute()


        //删除对应规则生成的Policy
        kSqlClient.createDelete(Policy::class) {
            where(table.ruleId eq ruleId)
        }.execute()
        val updates = cases.parallelStream()
            .filter { productCase -> evaluateCondition(authorizationRule.expression, productCase) }.map { productCase ->
                val currentPolicy =
                    productCase.acl?.policies?.filter { it.ruleId != authorizationRule.id } ?: emptyList()
                val rulePolicies: List<Policy> = authorizationRule.groups.map { group ->
                    Policy {
                        aclId = productCase.acl?.id
                        policyType = PolicyType.GROUP
                        strategyType = StrategyType.ROOT
                        value = group.code
                        this.ruleId = authorizationRule.id
                    }
                }
                ACLSave(
                    ACL {
                        resourceId = productCase.id
                        policies = currentPolicy + rulePolicies
                    })
            }.collect(Collectors.toList())
        aclService.authorization(updates)
        //刷新受影响的案例的权限
        esProductCasesService.updateAcl(effectCaseIds)

    }


    private fun evaluateCondition(spelExpression: String, rootObject: Any): Boolean {
        val parser: ExpressionParser = SpelExpressionParser()
        val expression: Expression = parser.parseExpression(spelExpression)
        val context = StandardEvaluationContext(rootObject)
        try {
            return expression.getValue(context, Boolean::class.java) ?: false
        } catch (_: EvaluationException) {

        }
        return false
    }
}