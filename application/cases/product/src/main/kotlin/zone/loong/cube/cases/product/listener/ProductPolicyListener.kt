package zone.loong.cube.cases.product.listener

import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import zone.loong.cube.cases.product.constants.ProductConstants
import zone.loong.cube.cases.product.service.ESProductCasesService
import zone.loong.cube.framework.data.jimmer.event.ResourcePolicyUpdate

@Component
class ProductPolicyListener(private val esProductService: ESProductCasesService) {
    @EventListener
    @Transactional(propagation = Propagation.MANDATORY)
    fun onPolicyChange(event: ResourcePolicyUpdate) {
        if (event.resourceType == ProductConstants.CASE_RESOURCE){
            esProductService.updateAcl(event.resourceId)
        }
    }
}