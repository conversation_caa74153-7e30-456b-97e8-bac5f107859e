package zone.loong.cube.framework.data.jimmer.ext

import org.babyfish.jimmer.View
import org.babyfish.jimmer.sql.kt.KSqlClient
import org.babyfish.jimmer.sql.kt.ast.query.KConfigurableRootQuery
import org.babyfish.jimmer.sql.kt.ast.query.KMutableRootQuery
import org.babyfish.jimmer.sql.kt.ast.query.specification.KSpecification
import org.babyfish.jimmer.sql.kt.ast.table.KNonNullTable
import org.babyfish.jimmer.sql.kt.ast.table.makeOrders
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import kotlin.reflect.KClass

fun <E> KConfigurableRootQuery<*, E>.page(pageIndex: Int, pageSize: Int): Page<E> {
    return fetchPage(pageIndex, pageSize) { entities, totalCount, _ ->
        Page(
            pageSize, pageIndex, totalCount, entities
        )
    }
}

inline fun <reified E : Any, S : KSpecification<E>> KSqlClient.page(
    pageQuery: PageQuery<S>,
): Page<E> {
    return createQuery(E::class) {
        where(pageQuery.specification)
        orderBy(table.makeOrders(pageQuery.sort.joinToString(",") { "${it.field} ${it.order.value}" }))
        select(table)
    }.fetchPage(pageQuery.page, pageQuery.pageSize) { entities, totalCount, _ ->
        Page(
            pageQuery.pageSize, pageQuery.page, totalCount, entities
        )
    }
}

inline fun <reified E : Any, R : Any, S : KSpecification<E>> KSqlClient.page(
    pageQuery: PageQuery<S>,
    crossinline block: KMutableRootQuery<KNonNullTable<E>>.() -> KConfigurableRootQuery<KNonNullTable<E>, R>
): Page<R> {
    return createQuery(E::class) {
        where(pageQuery.specification)
        orderBy(table.makeOrders(pageQuery.sort.joinToString(",") { "${it.field} ${it.order.value}" }))
        block()
    }.fetchPage(pageQuery.page, pageQuery.pageSize) { entities, totalCount, _ ->
        Page(
            pageQuery.pageSize, pageQuery.page, totalCount, entities
        )
    }
}

inline fun <reified E : Any, S : KSpecification<E>, R : View<E>> KSqlClient.page(
    pageQuery: PageQuery<S>,
    view: KClass<R>,
    crossinline block: KMutableRootQuery<KNonNullTable<E>>.() -> Unit
): Page<R> {
    return page(pageQuery) {
        block()
        select(table.fetch(view))
    }
}

inline fun <reified E : Any, S : KSpecification<E>, R : View<E>> KSqlClient.page(
    pageQuery: PageQuery<S>,
    view: KClass<R>,
): Page<R> {
    return page(pageQuery) {
        select(table.fetch(view))
    }
}



fun <E> KConfigurableRootQuery<*, E>.exists(): Boolean {
    val result = execute()
    return result.isNotEmpty()
}
