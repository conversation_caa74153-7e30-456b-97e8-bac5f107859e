package zone.loong.cube.platform.organization.controller

import org.springframework.web.bind.annotation.*
import zone.loong.cube.framework.data.kernel.model.Page
import zone.loong.cube.framework.data.kernel.model.PageQuery
import zone.loong.cube.framework.kernel.model.Tree
import zone.loong.cube.platform.organization.model.Org
import zone.loong.cube.platform.organization.model.dto.OrganizationSpecification
import zone.loong.cube.platform.organization.service.OrganizationService

@RestController
@RequestMapping("/organization")
class OrganizationController(private val organizationService: OrganizationService) {
    @PostMapping("/page")
    fun fetchOrganization(@RequestBody query: PageQuery<OrganizationSpecification>): Page<Org> {
       return organizationService.fetchOrganization(query)
    }

    @GetMapping("/list")
    fun fetchOrganization(@RequestParam pid: String?): List<Org> {

        return organizationService.fetchOrganization(pid)
    }

    @GetMapping("/filter")
    fun filterOrganization(@RequestParam keyword: String): List<Tree> {
        return organizationService.filterOrganization(keyword)
    }
}