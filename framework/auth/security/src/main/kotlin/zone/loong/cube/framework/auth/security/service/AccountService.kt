package zone.loong.cube.framework.auth.security.service

import zone.loong.cube.framework.auth.security.model.Account
import zone.loong.cube.framework.auth.security.model.BanLevel


interface AccountService {
    fun fetchAccount(accountIdentity: String): Account
    fun fetchAccountByToken(tokenString: String): Account
    fun fetchAccountGroups(accountIdentity: String): List<String>
    fun fetchAccountRoles(accountIdentity: String): List<String>

    //临时禁用账户
    fun banAccount(
        accountIdentity: String,
        banLevel: BanLevel,
        banReason: String
    )

    //解封账户
    fun unbanAccount(accountIdentity: String)
}